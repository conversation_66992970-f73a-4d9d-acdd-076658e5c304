@using System.Linq.Expressions

<select multiple class="@AdditionalAttributes?["class"]" @onchange="OnChange" style="@AdditionalAttributes?["style"]">
    <option value="__all__" selected="@(IsAllSelected ? "selected" : null)">【全選】</option>
    @if (ChildContent != null)
    {
        @ChildContent
    }
</select>

@code {
    [Parameter] public List<string> Value { get; set; } = new();
    [Parameter] public EventCallback<List<string>> ValueChanged { get; set; }
    [Parameter] public Expression<Func<List<string>>> ValueExpression { get; set; } = null!;
    [Parameter(CaptureUnmatchedValues = true)] public Dictionary<string, object> AdditionalAttributes { get; set; } = new();
    [Parameter] public RenderFragment ChildContent { get; set; } = null!;

    [Parameter] public List<string> AllValues { get; set; } = new();

    private bool IsAllSelected => Value != null && AllValues != null && Value.Count == AllValues.Count &&
    !AllValues.Except(Value).Any();

    private async Task OnChange(ChangeEventArgs e)
    {
        var selected = new List<string>();
        if (e.Value is IEnumerable<object> values)
        {
            selected = values.Select(v => v?.ToString() ?? "").Where(s => !string.IsNullOrEmpty(s)).ToList();
        }
        else if (e.Value is string singleValue)
        {
            selected.Add(singleValue);
        }
        // 處理全選邏輯
        if (selected.Contains("__all__"))
        {
            // 若原本已全選，則取消全選
            if (IsAllSelected)
                selected.Clear();
            else
                selected = AllValues.ToList();
        }
        // 移除有問題的 else if 邏輯，讓用戶可以正常選擇特定項目
        Value = selected;
        await ValueChanged.InvokeAsync(Value);
    }
}