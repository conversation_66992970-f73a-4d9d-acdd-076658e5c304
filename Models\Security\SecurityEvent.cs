using System.Text.Json;

namespace Intra2025.Models.Security
{
    /// <summary>
    /// 安全事件類型枚舉
    /// </summary>
    public enum SecurityEventType
    {
        // Authentication Events (身份驗證事件)
        LOGIN_SUCCESS,
        LOGIN_FAILURE,
        LOGOUT,
        SESSION_EXPIRED,
        TOKEN_INVALID,
        SESSION_REGENERATED,

        // Authorization Events (授權事件)
        ACCESS_GRANTED,
        ACCESS_DENIED,
        PERMISSION_ESCALATION,
        UNAUTHORIZED_OPERATION,

        // Data Access Events (資料存取事件)
        DATA_VIEW,
        DATA_MODIFY,
        DATA_DELETE,
        FILE_DOWNLOAD,
        FILE_UPLOAD,
        SENSITIVE_DATA_ACCESS,

        // Security Events (安全事件)
        SUSPICIOUS_ACTIVITY,
        MULTIPLE_LOGIN_FAILURES,
        UNUSUAL_IP_ACCESS,
        CSRF_ATTEMPT,
        SQL_INJECTION_ATTEMPT,
        PATH_TRAVERSAL_ATTEMPT,

        // System Events (系統事件)
        APPLICATION_START,
        APPLICATION_ERROR,
        CONFIGURATION_CHANGE,
        SERVICE_UNAVAILABLE
    }

    /// <summary>
    /// 安全事件嚴重性等級
    /// </summary>
    public enum SecurityEventSeverity
    {
        Information,
        Warning,
        Error,
        Critical
    }

    /// <summary>
    /// 安全事件模型
    /// </summary>
    public class SecurityEvent
    {
        /// <summary>
        /// 事件時間戳
        /// </summary>
        public DateTime Timestamp { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// 事件類型
        /// </summary>
        public SecurityEventType EventType { get; set; }

        /// <summary>
        /// 事件嚴重性
        /// </summary>
        public SecurityEventSeverity Severity { get; set; }

        /// <summary>
        /// 使用者 ID
        /// </summary>
        public string? UserId { get; set; }

        /// <summary>
        /// 使用者名稱
        /// </summary>
        public string? UserName { get; set; }

        /// <summary>
        /// IP 位址
        /// </summary>
        public string? IpAddress { get; set; }

        /// <summary>
        /// 使用者代理字串
        /// </summary>
        public string? UserAgent { get; set; }

        /// <summary>
        /// 會話 ID
        /// </summary>
        public string? SessionId { get; set; }

        /// <summary>
        /// 事件詳細資訊
        /// </summary>
        public Dictionary<string, object>? Details { get; set; }

        /// <summary>
        /// 事件來源（服務或組件名稱）
        /// </summary>
        public string? Source { get; set; }

        /// <summary>
        /// 關聯 ID（用於追蹤相關事件）
        /// </summary>
        public string? CorrelationId { get; set; }

        /// <summary>
        /// 請求路徑
        /// </summary>
        public string? RequestPath { get; set; }

        /// <summary>
        /// HTTP 方法
        /// </summary>
        public string? HttpMethod { get; set; }

        /// <summary>
        /// 轉換為 JSON 字串
        /// </summary>
        public string ToJson()
        {
            var options = new JsonSerializerOptions
            {
                WriteIndented = false,
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase
            };
            return JsonSerializer.Serialize(this, options);
        }

        /// <summary>
        /// 創建登入成功事件
        /// </summary>
        public static SecurityEvent CreateLoginSuccess(string userId, string userName, string ipAddress, string? sessionId = null)
        {
            return new SecurityEvent
            {
                EventType = SecurityEventType.LOGIN_SUCCESS,
                Severity = SecurityEventSeverity.Information,
                UserId = userId,
                UserName = userName,
                IpAddress = ipAddress,
                SessionId = sessionId,
                Source = "Authentication",
                Details = new Dictionary<string, object>
                {
                    ["LoginMethod"] = "SSO"
                }
            };
        }

        /// <summary>
        /// 創建登入失敗事件
        /// </summary>
        public static SecurityEvent CreateLoginFailure(string? userId, string ipAddress, string reason, int attemptCount = 1)
        {
            return new SecurityEvent
            {
                EventType = SecurityEventType.LOGIN_FAILURE,
                Severity = attemptCount > 3 ? SecurityEventSeverity.Warning : SecurityEventSeverity.Information,
                UserId = userId,
                IpAddress = ipAddress,
                Source = "Authentication",
                Details = new Dictionary<string, object>
                {
                    ["Reason"] = reason,
                    ["AttemptCount"] = attemptCount,
                    ["LoginMethod"] = "SSO"
                }
            };
        }

        /// <summary>
        /// 創建存取被拒絕事件
        /// </summary>
        public static SecurityEvent CreateAccessDenied(string? userId, string? userName, string ipAddress, string resource, string reason)
        {
            return new SecurityEvent
            {
                EventType = SecurityEventType.ACCESS_DENIED,
                Severity = SecurityEventSeverity.Warning,
                UserId = userId,
                UserName = userName,
                IpAddress = ipAddress,
                Source = "Authorization",
                Details = new Dictionary<string, object>
                {
                    ["Resource"] = resource,
                    ["Reason"] = reason
                }
            };
        }

        /// <summary>
        /// 創建敏感資料存取事件
        /// </summary>
        public static SecurityEvent CreateSensitiveDataAccess(string userId, string userName, string ipAddress, string dataType, string operation)
        {
            return new SecurityEvent
            {
                EventType = SecurityEventType.SENSITIVE_DATA_ACCESS,
                Severity = SecurityEventSeverity.Information,
                UserId = userId,
                UserName = userName,
                IpAddress = ipAddress,
                Source = "DataAccess",
                Details = new Dictionary<string, object>
                {
                    ["DataType"] = dataType,
                    ["Operation"] = operation
                }
            };
        }

        /// <summary>
        /// 創建可疑活動事件
        /// </summary>
        public static SecurityEvent CreateSuspiciousActivity(string? userId, string ipAddress, string activityType, string description)
        {
            return new SecurityEvent
            {
                EventType = SecurityEventType.SUSPICIOUS_ACTIVITY,
                Severity = SecurityEventSeverity.Warning,
                UserId = userId,
                IpAddress = ipAddress,
                Source = "SecurityMonitoring",
                Details = new Dictionary<string, object>
                {
                    ["ActivityType"] = activityType,
                    ["Description"] = description
                }
            };
        }
    }
}
