/*!*  filename: sf-gantt.min.js 
*     version : 26.1.41 
*     Copyright Syncfusion Inc. 2001 - 2024. All rights reserved. 
*     Use of this code is subject to the terms of our license.
*     A copy of the current license can be obtained at any time by e-mailing 
*     <EMAIL>. Any infringement will be prosecuted under 
*     applicable laws. 
*/
(window.webpackJsonp=window.webpackJsonp||[]).push([[28],{"./bundles/sf-gantt.js":function(e,t,i){"use strict";i.r(t);i("./modules/sf-gantt.js")},"./modules/sf-gantt.js":function(e,t){window.sfBlazor=window.sfBlazor||{},window.sfBlazor.Gantt=function(){"use strict";var e=function(){function e(e,t){this.previousCount=-1,this.timelineScrollLeft=0,this.parent=e,this.chartRootElement=t.querySelector(".e-chart-root-container"),this.chartElement=t.querySelector(".e-gantt-chart-pane"),this.element=t.querySelector(".e-chart-scroll-container"),this.timelineHeaderElement=t.querySelector(".e-timeline-header-container"),this.chartVirtualTable=this.element.querySelector(".e-virtualtable"),this.timelineVirtualTrack=this.timelineHeaderElement.querySelector(".e-virtualtrack"),this.dependencyViewContainer=t.querySelector(".e-gantt-dependency-view-container"),this.holidayContainer=t.querySelector(".e-holiday-container"),this.addEventListeners(),this.ChartHeight(t,this.chartRootElement)}return e.prototype.addEventListeners=function(){sf.base.EventHandler.add(this.element,"scroll",this.onScroll,this),sf.base.EventHandler.add(this.chartElement,sf.base.Browser.touchStartEvent,this.mouseDownHandler,this)},e.prototype.removeEventListeners=function(e){var t=e.element.querySelector(".e-gantt-chart-pane"),i=e.element.querySelector(".e-chart-scroll-container");sf.base.EventHandler.remove(i,"scroll",this.onScroll),sf.base.EventHandler.remove(t,sf.base.Browser.touchStartEvent,this.mouseDownHandler)},e.prototype.gridScrollHandler=function(e){this.element.scrollTop=e,this.parent.isFromTreeGrid=!0},e.prototype.showHideSpinner=function(e){var t=this.parent;e&&!this.parent.spinnerShown?(this.parent.dotNetRef.invokeMethodAsync("ShowSpinner"),this.parent.spinnerShown=!0):!e&&this.parent.spinnerShown&&(t.dotNetRef.invokeMethodAsync("HideSpinner"),t.spinnerShown=!1)},e.prototype.getTimelineLeft=function(){var e=this.parent.element,t=this.timelineVirtualTrack.offsetWidth-3*e.offsetWidth;return this.timelineScrollLeft==(this.parent.enableRTL?-t:t)?this.timelineScrollLeft:this.parent.enableRTL?this.timelineScrollLeft+e.offsetWidth:this.timelineScrollLeft>e.offsetWidth?this.timelineScrollLeft-e.offsetWidth:0},e.prototype.updateSpinner=function(e){var t,i=this;this.showHideSpinner(!0),window.clearTimeout(this.isScrolling),this.isScrolling=setTimeout((function(){e&&(t=i.getTimelineLeft(),i.parent.dotNetRef.invokeMethodAsync("VirtualRefresh",t,i.parent.element.offsetWidth).then((function(){i.updateChartElementStyle()}))),i.element.querySelector(".e-chart-rows-container").style.visibility="visible",i.showHideSpinner(!1),window.clearTimeout(this.isScrolling)}),1e3)},e.prototype.getTopPosition=function(){var e=this.element.querySelector(".e-virtualtable"),t=e.style.transform.split(","),i=t.length>1?t[1].trim().split(")")[0]:e.style.transform.substring(e.style.transform.lastIndexOf("(")+1,e.style.transform.lastIndexOf(")"));return parseFloat(i)},e.prototype.getContentHeight=function(){var e=this.element.scrollHeight,t=this.chartElement.offsetHeight;return t<e?t:e},e.prototype.updateTopPosition=function(){var e,t=this.parent.treeGrid.element.querySelector(".e-content"),i=this.parent.chartScrollModule.getContentHeight(),s=t.scrollTop;this.parent.chartScrollModule&&this.parent.options.enableRowVirtualization?e=s-this.parent.chartScrollModule.getTopPosition():e=s;sf.base.isNullOrUndefined(this.holidayContainer)||(this.holidayContainer.style.height=i+"px",this.holidayContainer.style.top=e+"px")},e.prototype.updateScrollTopPosition=function(){var e=this.parent.treeGrid.element.querySelector(".e-content");sf.base.isNullOrUndefined(e)||this.element.scrollTop==e.scrollTop||(this.element.scrollTop=e.scrollTop)},e.prototype.onScroll=function(){var e=this.parent;if(e.scrollbarUpdate=!0,this.element.scrollLeft!==this.parent.chartPreviousScroll.left&&(sf.base.isNullOrUndefined(this.parent.taskbarEditModule)||(this.parent.taskbarEditModule.LongPress=!1)),this.element.scrollTop!==e.chartPreviousScroll.top&&this.parent.options.enableRowVirtualization&&(this.element.scrollTop-e.chartPreviousScroll.top>40||e.chartPreviousScroll.top-this.element.scrollTop>40)&&this.updateSpinner(!1),this.element.scrollLeft!==this.parent.chartPreviousScroll.left){if(this.timelineHeaderElement.scrollLeft=this.element.scrollLeft,e.options.enableTimelineVirtualization){var t=this.parent.chartPreviousScroll.left,i=this.element.scrollLeft;!this.parent.enableRTL&&(this.element.scrollLeft-this.parent.chartPreviousScroll.left>50||this.parent.chartPreviousScroll.left-this.element.scrollLeft>50)||this.parent.enableRTL&&(Math.abs(i)-Math.abs(t)>50||Math.abs(t)-Math.abs(i)>50)?(this.appendDummyTable(),this.updateSpinner(!0)):this.updateVirtualContent()}this.parent.chartPreviousScroll.left=this.element.scrollLeft}this.element.scrollTop!==this.parent.chartPreviousScroll.top&&(this.parent.isFromTreeGrid||this.parent.treeGridModule.updateScrollTop(this.element.scrollTop),this.parent.chartPreviousScroll.top=this.element.scrollTop,this.updateTopPosition(),this.parent.isFromTreeGrid=!1)},e.prototype.updateContent=function(){var e=this;this.timelineScrollLeft=this.timelineHeaderElement.scrollLeft;var t=this.parent.element,i=Math.round(this.timelineScrollLeft/(2*t.offsetWidth));if(this.previousCount!=i){this.calculateScrollLeft();var s=this.getTimelineLeft();e.parent.dotNetRef.invokeMethodAsync("VirtualRefresh",s,t.offsetWidth).then((function(){e.updateChartElementStyle()}))}this.previousCount=i},e.prototype.updateVirtualContent=function(){this.timelineScrollLeft=this.timelineHeaderElement.scrollLeft;var e=this.parent.element,t=Math.round(this.timelineScrollLeft/e.offsetWidth);this.previousCount!=t&&(this.appendDummyTable(),this.timelineScrollLeft-=e.offsetWidth,this.updateSpinner(!0)),this.previousCount=t},e.prototype.updateChartElementStyle=function(){var e=this.getTimelineLeft(),t=3*this.parent.element.offsetWidth;if(this.timelineVirtualTrack.offsetWidth>t){var i=new DOMMatrixReadOnly(this.chartVirtualTable.style.transform).m42;this.chartVirtualTable.style.transform="translate("+e+"px,"+i+"px)";var s=this.parent.enableRTL?t+Math.abs(e):t+e,r=this.parent.enableRTL?e:-e;sf.base.isNullOrUndefined(this.chartTaskTable)||(this.chartTaskTable.style.setProperty(this.parent.enableRTL?"right":"left",r+"px"),this.chartTaskTable.style.width=s+"px"),sf.base.isNullOrUndefined(this.dependencyViewContainer)||(this.dependencyViewContainer.style.left=-e+"px")}else this.timelineVirtualTrack.offsetWidth<t&&!sf.base.isNullOrUndefined(this.chartTaskTable)&&(this.chartTaskTable.style.width=this.timelineVirtualTrack.offsetWidth+"px")},e.prototype.calculateScrollLeft=function(){var e=this.parent.element,t=3*e.offsetWidth,i=this.timelineVirtualTrack.offsetWidth>t?this.timelineVirtualTrack.offsetWidth-t:t,s=Math.abs(this.timelineScrollLeft);this.timelineScrollLeft>=i?this.timelineScrollLeft=i:s>=i&&this.parent.enableRTL?this.timelineScrollLeft=-i:this.element.scrollLeft<this.parent.chartPreviousScroll.left&&!this.parent.enableRTL?this.timelineScrollLeft<=e.offsetWidth&&(this.timelineScrollLeft=0):this.timelineScrollLeft<0&&!this.parent.enableRTL&&(this.timelineScrollLeft=0)},e.prototype.appendDummyTable=function(){this.timelineScrollLeft=this.timelineHeaderElement.scrollLeft;var e=this.timelineHeaderElement.querySelectorAll(".e-virtualtable");this.element.querySelector(".e-chart-rows-container").style.visibility="hidden",this.calculateScrollLeft();for(var t=this.getTimelineLeft(),i=0;i<e.length;i++){var s=e[i],r=new DOMMatrixReadOnly(s.style.transform).m42;s.style.transform="translate("+t+"px,"+r+"px)"}},e.prototype.mouseDownHandler=function(e){var t,i=e.target,s=!1,r=this.parent.getParentElement(i,"e-gantt-chart"),n=this.parent.getParentElement(i,"e-chart-row");this.parent.getParentElement(i,"e-segmented-taskbar");if(t=this.getTaskbarLeft(e),null!=n&&(s=!!n.querySelector(".e-chart-empty-row")),null!=r){var a=null,o=null;if(this.parent.getParentElement(i,"e-timeline-header-container"))a="Header";else if(this.parent.getParentElement(i,"e-content")){if(!sf.base.isNullOrUndefined(e.target.classList)&&(e.target.classList.contains("e-connector-line")||e.target.classList.contains("e-rg-rangdiv")))return;a="Content",o=this.parent.getParentElement(i,"e-chart-row-cell")?this.parent.getParentElement(e.target,"e-chart-row-cell").getAttribute("data-uid"):null}"Header"!=a&&"Content"!=a||this.parent.dotNetRef.invokeMethodAsync("ChartMouseDownHandler",a,o,s,t)}},e.prototype.setScrollLeft=function(e,t){e=-1==t&&this.parent.enableRTL?-e:e,this.element.scrollLeft=e,this.timelineHeaderElement.scrollLeft=this.element.scrollLeft,this.parent.chartPreviousScroll.left=this.element.scrollLeft},e.prototype.setScrollTop=function(e){this.element.scrollTop=e,this.parent.treeGrid.element.querySelector(".e-content").scrollTop=e},e.prototype.ChartHeight=function(e,t){this.toolbarElement=e.querySelector("#"+e.id+"_Gantt_Toolbar"),this.viewPortHeight=this.parent.height-this.parent.toolbarHeight-this.timelineHeaderElement.offsetHeight,t.style.height="calc(100% - "+this.timelineHeaderElement.offsetHeight+"px)"},e.prototype.getTaskbarLeft=function(e){var t,i,s;if(this.parent.enableRTL){var r=this.parent.element.getBoundingClientRect(),n=window.pageXOffset||document.documentElement.scrollLeft||document.body.scrollLeft,a=document.documentElement.clientLeft||document.body.clientLeft||0;t=r.left+n-a}else t=this.parent.getOffsetRect(this.parent.element).left;return this.parent.enableRTL?(i=Math.abs(t+this.chartElement.offsetWidth),s=Math.abs(("touchstart"==e.type?e.changedTouches[0].clientX:e.clientX)-i)):(i=t+this.chartElement.offsetLeft,s=("touchstart"==e.type?e.changedTouches[0].clientX:e.clientX)-i),this.parent.enableRTL?Math.abs(Math.abs(s)+Math.abs(this.parent.chartScrollModule.element.scrollLeft)):Math.abs(s)+this.parent.chartScrollModule.element.scrollLeft},e.prototype.destroy=function(e){this.removeEventListeners(e)},e}(),t=function(){function e(e,t){this.parent=e,this.content=t,this.treegridPane=this.parent.element.querySelector(".e-grid"),this.addEventListeners()}return e.prototype.addEventListeners=function(){sf.base.EventHandler.add(this.content,"scroll",this.scrollHandler,this),sf.base.EventHandler.add(this.treegridPane,sf.base.Browser.touchStartEvent,this.treeMouseDownHandler,this),sf.base.EventHandler.add(window,"resize",this.windowResize,this)},e.prototype.removeEventListeners=function(e){var t=e.element.querySelector(".e-gridcontent");sf.base.EventHandler.remove(this.treegridPane,sf.base.Browser.touchStartEvent,this.treeMouseDownHandler),sf.base.EventHandler.remove(t,"scroll",this.scrollHandler),sf.base.EventHandler.remove(window,"resize",this.windowResize)},e.prototype.scrollHandler=function(e){this.content.scrollTop!==this.parent.treegridPreviousScroll.top&&this.parent.chartScrollModule.gridScrollHandler(this.content.scrollTop),this.parent.treegridPreviousScroll.top=this.content.scrollTop},e.prototype.treeMouseDownHandler=function(e){if(null!=this.parent.getParentElement(e.target,"e-grid")){var t=null,i=null;if(this.parent.getParentElement(e.target,"e-gridcontent"))t="GridContent",i=this.parent.getParentElement(e.target,"e-row")?this.parent.getParentElement(e.target,"e-row").getAttribute("data-uid"):null;else if(this.parent.getParentElement(e.target,"e-headercontent")){t="Header";var s=!0}"Header"!=t&&"GridContent"!=t||this.parent.dotNetRef.invokeMethodAsync("TreeMouseDownHandler",t,i,s)}},e.prototype.windowResize=function(){if(sf.base.Browser.isDevice&&this.parent.dotNetRef.invokeMethodAsync("WindowResize"),this.parent.options.enableTimelineVirtualization){var e=this.parent.chartScrollModule.timelineHeaderElement;e&&(e.style.width=this.parent.chartScrollModule.element.offsetWidth-this.parent.scrollWidth+"px"),this.parent.chartScrollModule.updateContent()}},e.prototype.updateScrollTop=function(e){this.content.scrollTop=e,this.parent.treegridPreviousScroll.top=this.content.scrollTop},e.prototype.destroy=function(e){this.removeEventListeners(e)},e}(),i="e-taskbar-main-container",s="e-active-container",r="e-segmented-taskbar",n=function(){return(n=Object.assign||function(e){for(var t,i=1,s=arguments.length;i<s;i++)for(var r in t=arguments[i])Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r]);return e}).apply(this,arguments)},a=function(){function e(e){this.elementOffsetLeft=0,this.elementOffsetTop=0,this.elementOffsetWidth=0,this.elementOffsetHeight=0,this.restrictTaskbarEdit=!1,this.restrictConnectorLineEdit=!1,this.dblClick=0,this.segmentIndex=-1,this.updateProgress=!0,this.segmentPosition=0,this.parent=e,this.initPublicProp(),this.ganttChartTableBody=this.parent.element.querySelector("#"+this.parent.element.id+"_chartContentBody"),this.chartPane=this.parent.element.querySelector(".e-gantt-chart-pane"),this.chartBodyContainer=this.parent.element.querySelector(".e-chart-root-container"),this.addEventListeners()}return e.prototype.initPublicProp=function(){this.taskBarEditElement=null,this.taskBarEditRecord=null,this.taskBarEditAction=null,this.dragMouseLeave=!1,this.isMouseDragging=!1,this.isTaskbarHold=!1,this.taskbarTouchEditing=!1,this.connectorPointWidth=null,this.connectorSecondAction=null,this.toPredecessorText=null,this.highlightedSecondElement=null,this.falseLine=null,this.segmentElement=null,this.segmentIndex=-1,this.taskBarEditSegmentElement=null,this.updateProgress=!0},e.prototype.addEventListeners=function(){sf.base.EventHandler.add(this.chartPane,sf.base.Browser.touchStartEvent,this.ganttMouseDown,this),sf.base.EventHandler.add(this.chartPane,sf.base.Browser.touchMoveEvent,this.ganttMouseMove,this),sf.base.EventHandler.add(this.chartPane,sf.base.Browser.touchCancelEvent,this.ganttMouseLeave,this),sf.base.EventHandler.add(document,sf.base.Browser.touchEndEvent,this.ganttChartMouseUp,this),sf.base.EventHandler.add(this.chartBodyContainer,"click",this.click,this),sf.base.EventHandler.add(this.chartPane,"mouseover",this.ganttMouseOver,this)},e.prototype.removeEventListeners=function(e){var t=e.element.querySelector(".e-gantt-chart-pane"),i=e.element.querySelector(".e-chart-root-container");sf.base.EventHandler.remove(t,sf.base.Browser.touchStartEvent,this.ganttMouseDown),sf.base.EventHandler.remove(t,sf.base.Browser.touchMoveEvent,this.ganttMouseMove),sf.base.EventHandler.remove(t,sf.base.Browser.touchCancelEvent,this.ganttMouseLeave),sf.base.EventHandler.remove(document,sf.base.Browser.touchEndEvent,this.ganttChartMouseUp),sf.base.EventHandler.remove(i,"click",this.click),sf.base.EventHandler.remove(t,"mouseover",this.ganttMouseOver)},e.prototype.checkPointerType=function(e){return-1!==e.type.indexOf("pointer")&&"safari"===sf.base.Browser.info.name&&parseInt(sf.base.Browser.info.version)>12},e.prototype.isTaskbarDragAndDrop=function(e){return!sf.base.isNullOrUndefined(e.querySelector(".e-gantt-taskbar-draganddrop"))},e.prototype.ganttMouseDown=function(e){this.taskbarCanDrag=!1;var t=this.parent.getOffsetRect(this.chartBodyContainer);this.event=this.getCoordinate(e),this.parent.enableRTL?this.mouseDownX=Math.abs(e.pageX-(t.left+Math.abs(this.parent.chartPreviousScroll.left))):this.mouseDownX=this.event.pageX-t.left+this.parent.chartPreviousScroll.left;var s=e.target,n=s.classList.contains("e-chart-row-cell"),a=this.getElementByPosition(e),o=this.parent.getParentElement(a,"e-chart-row-cell"),l=null;if(sf.base.isNullOrUndefined(o)||(l=o.querySelector(".e-taskbar-main-container")),this.taskBarRow=this.parent.getParentElement(a,"e-chart-row"),sf.base.isNullOrUndefined(this.taskBarRow)||(this.parent.options.allowTaskbarDragAndDrop=this.isTaskbarDragAndDrop(this.parent.element)),this.updateTaskBarEditElement(e),!this.taskBarEditAction&&null==l&&this.parent.options.allowSchedulingOnDrag){if(this.chartEmptyRowElement=sf.base.createElement("div",{className:"e-chart-empty-row"}),sf.base.isNullOrUndefined(this.taskBarRow)||(this.taskBarRow.appendChild(this.chartEmptyRowElement),this.emptyRow=this.taskBarRow.querySelector(".e-chart-empty-row")),this.emptyRow){var h=document.querySelectorAll(".e-row");this.dataGuid=this.taskBarRow.getAttribute("data-rowindex");var c=h[this.dataGuid].querySelector(".e-treegridexpand");this.parentRow=!!c,this.parentRow?this.taskBarRow.style.cursor="no-drop":(this.taskBarRow.style.position="relative",this.taskBarRow.style.cursor="crosshair",this.emptyRow.style.left="0px",this.emptyRow.style.backgroundColor="transparent",this.emptyRow.style.top=(s.offsetHeight-(Math.floor(.62*s.offsetHeight)-2))/2-1+"px",this.emptyRow.style.height=Math.floor(.62*s.offsetHeight)+"px",this.parent.dotNetRef.invokeMethodAsync("ChartRowMouseDownHandler",this.dataGuid),this.isDown=!0)}this.longPress(!0)}else!this.taskBarEditAction&&this.taskBarRow&&n&&this.parent.options.allowSchedulingOnDrag&&(this.taskBarRow.style.cursor="no-drop");var d=this.parent.getParentElement(a,i);sf.base.isNullOrUndefined(d)||sf.base.isNullOrUndefined(this.taskbarCurrentEditElement)||d!=this.taskbarCurrentEditElement||(this.taskbarCanDrag=!0,this.checkPointerType(e)&&(e.stopPropagation(),this.parent.element.querySelector(".e-chart-scroll-container").style.overflow="hidden"));this.dblClick++,this.dblClick>=2&&(this.dblClick=1),sf.base.isNullOrUndefined(this.taskBarEditSegmentElement)&&(this.taskBarEditSegmentElement=this.parent.getParentElement(e.target,r)),sf.base.isNullOrUndefined(this.taskBarEditSegmentElement)||sf.base.EventHandler.add(this.taskBarEditSegmentElement,sf.base.Browser.touchCancelEvent,this.ganttChartMouseLeave,this)},e.prototype.click=function(e){var t=this.getElementByPosition(e);this.taskbarCurrentEditElement=this.parent.getParentElement(t,i),sf.base.isNullOrUndefined(this.taskBarEditSegmentElement)&&(this.taskBarEditSegmentElement=this.parent.getParentElement(e.target,r)),sf.base.isNullOrUndefined(this.taskBarEditSegmentElement)||sf.base.EventHandler.add(this.taskBarEditSegmentElement,sf.base.Browser.touchCancelEvent,this.ganttChartMouseLeave,this)},e.prototype.ganttMouseLeave=function(e){this.dragMouseLeave=!0,sf.base.isNullOrUndefined(this.taskBarEditSegmentElement)||this.ganttChartMouseLeave(e)},e.prototype.ganttChartMouseUp=function(e){var t=this;this.isTaskbarHold=!1,this.taskBarRow&&(this.isDown=!1,this.taskBarRow.style.cursor="");var i=-1;if(this.parent.options.allowTaskbarDragAndDrop&&this.parent.options.enableRowVirtualization){var s=document.elementFromPoint(e.clientX,e.clientY).getBoundingClientRect().x-5,n=document.elementFromPoint(s,e.clientY),a=this.parent.getParentElement(n,"e-chart-row");i=sf.base.isNullOrUndefined(a)?i:Number(a.getAttribute("data-rowindex"))}this.taskBarEditAction&&this.isMouseDragging?(this.parent.isTaskbarEditingEventMapped?this.parent.dotNetRef.invokeMethodAsync("TaskbarEditBeforeSave",this.taskBarEditAction).then((function(e){e||t.parent.dotNetRef.invokeMethodAsync("UpdateResizedData",t.dataGuid,t.taskBarEditAction,t.taskBarEditRecord,i).then((function(e){null!=t.taskBarEditRecord&&null!=e&&(t.taskBarEditRecord.left=e.left,t.taskBarEditRecord.width=e.width,t.taskBarEditRecord.progress=e.progress,t.taskBarEditRecord.progressWidth=e.progressWidth),t.updateItemPosition()}))})):this.dragMouseLeave||this.parent.isTaskbarEditingEventMapped?(this.parent.dotNetRef.invokeMethodAsync("UpdateResizedData",null,null,null,i),this.taskBarEditRecord=this.previousItem,this.updateItemPosition()):this.parent.dotNetRef.invokeMethodAsync("UpdateResizedData",this.dataGuid,this.taskBarEditAction,this.taskBarEditRecord,i).then((function(e){null!=t.taskBarEditRecord&&null!=e&&(t.taskBarEditRecord.left=e.left,t.taskBarEditRecord.width=e.width,t.taskBarEditRecord.progress=e.progress,t.taskBarEditRecord.progressWidth=e.progressWidth),t.updateItemPosition()})),this.checkPointerType(e)&&(this.parent.element.querySelector(".e-chart-scroll-container").style.overflow="scroll auto")):this.taskBarEditElement&&this.taskbarTouchEditing||this.taskBarEditAction?(this.parent.dotNetRef.invokeMethodAsync("UpdateResizedData",null,null,null,i),this.falseLine&&this.removeFalseLine(!0),this.initPublicProp()):this.tappedPoint?this.taskbarCurrentEditElement=null:!this.taskBarEditAction&&this.emptyRow&&!this.parentRow&&this.parent.options.allowSchedulingOnDrag&&(this.emptyRow.style.border="none",sf.base.isNullOrUndefined(this.chartEmptyRowElement)||sf.base.remove(this.chartEmptyRowElement),this.parent.dotNetRef.invokeMethodAsync("UpdateTaskbarResizedData",this.dataGuid),this.taskBarRow=null,this.emptyRow=null);this.removeCloneElement(),this.stopScrollTimer(),this.longPress(!1),sf.base.isNullOrUndefined(this.taskBarEditSegmentElement)&&(this.taskBarEditSegmentElement=this.parent.getParentElement(e.target,r)),sf.base.isNullOrUndefined(this.taskBarEditSegmentElement)||sf.base.EventHandler.add(this.taskBarEditSegmentElement,sf.base.Browser.touchCancelEvent,this.ganttChartMouseLeave,this)},e.prototype.ganttMouseOver=function(e){if(sf.base.isNullOrUndefined(this.taskBarEditSegmentElement)&&(this.taskBarEditSegmentElement=this.parent.getParentElement(e.target,"e-segmented-taskbar")),sf.base.isNullOrUndefined(this.taskBarEditSegmentElement)||null!=this.taskBarEditAction)sf.base.isNullOrUndefined(this.taskBarEditSegmentElement)||null==this.taskBarEditAction||this.ganttChartMouseLeave(e);else{sf.base.EventHandler.add(this.taskBarEditSegmentElement,sf.base.Browser.touchCancelEvent,this.ganttChartMouseLeave,this);var t=this.taskBarEditSegmentElement.querySelector(".e-taskbar-left-resizer"),i=this.taskBarEditSegmentElement.querySelector(".e-taskbar-right-resizer");sf.base.isNullOrUndefined(t)||t.classList.add("e-left-resize-gripper"),sf.base.isNullOrUndefined(i)||i.classList.add("e-right-resize-gripper")}},e.prototype.ganttChartMouseLeave=function(e){if(!sf.base.isNullOrUndefined(this.taskBarEditSegmentElement)){var t=this.taskBarEditSegmentElement.querySelector(".e-taskbar-left-resizer"),i=this.taskBarEditSegmentElement.querySelector(".e-taskbar-right-resizer");!sf.base.isNullOrUndefined(t)&&t.classList.contains("e-left-resize-gripper")&&t.classList.remove("e-left-resize-gripper"),!sf.base.isNullOrUndefined(i)&&i.classList.contains("e-right-resize-gripper")&&i.classList.remove("e-right-resize-gripper"),sf.base.EventHandler.remove(this.taskBarEditSegmentElement,sf.base.Browser.touchCancelEvent,this.ganttChartMouseLeave),null==this.taskBarEditAction&&(this.taskBarEditSegmentElement=null)}},e.prototype.updateItemPosition=function(){this.setItemPosition(),this.falseLine&&this.removeFalseLine(!0),this.initPublicProp()},e.prototype.createResizeElement=function(){var e=sf.base.createElement("div");e.classList.add("e-gantt-chart-resize-indicator"),e.style.setProperty("width",this.taskBarEditElement.style.width);var t=this.parent.enableRTL?"right":"left";if(e.style.setProperty(t,this.taskBarEditElement.style.left),this.parent.options.enableTimelineVirtualization&&this.parent.options.enableRowVirtualization){var i=this.parent.chartScrollModule.getTimelineLeft(),s=e.style.left,r=s.substring(0,s.length-2);e.style.left=parseInt(r)+(this.parent.enableRTL?i:-i)+"px"}this.parent.element.querySelector(".e-chart-rows-container").appendChild(e)},e.prototype.createTaskbarClone=function(e){sf.base.isNullOrUndefined(this.taskBarEditSegmentElement)||this.ganttChartMouseLeave();var t=this.parent.element.querySelector(".e-gantt-clone-taskbar"),i=e.cloneNode(!0);if(!t){i.classList.add("e-gantt-clone-taskbar");var s=0!=this.segmentIndex||"ChildDrag"!==this.taskBarEditAction&&"ProgressResizing"!==this.taskBarEditAction?this.segmentIndex<0?1:0:1;if(i.style.setProperty("top",e.parentElement.offsetTop+s+"px"),"LeftResizing"===this.taskBarEditAction)i.querySelector(".e-taskbar-left-resizer").classList.add("e-left-resize-gripper");if("RightResizing"===this.taskBarEditAction)i.querySelector(".e-taskbar-right-resizer").classList.add("e-right-resize-gripper");if("ProgressResizing"===this.taskBarEditAction)i.querySelector(".e-child-progress-resizer").classList.add("e-progress-resize-gripper");var r=i.querySelector(".e-left-connectorpoint-outer-div"),n=i.querySelector(".e-right-connectorpoint-outer-div");sf.base.isNullOrUndefined(r)||sf.base.isNullOrUndefined(n)||(r.style.setProperty("visibility","hidden"),n.style.setProperty("visibility","hidden"))}(this.cloneTaskbarElement=i,this.parent.options.allowTaskbarDragAndDrop&&(this.cloneTaskbarElement.style.zIndex="999"),this.cloneTaskbarElement.style.setProperty("opacity","40%"),this.taskBarEditElement=this.cloneTaskbarElement,sf.base.isNullOrUndefined(i))||(this.parent.options.enableRowVirtualization&&this.parent.options.allowTaskbarDragAndDrop?this.parent.element.querySelector(".e-chart-scroll-container").appendChild(i):e.parentElement.appendChild(i))},e.prototype.removeCloneElement=function(){var e=this.parent.element.querySelector(".e-gantt-chart-resize-indicator"),t=this.parent.element.querySelector(".e-gantt-clone-taskbar");if(sf.base.isNullOrUndefined(e)||e.remove(),!sf.base.isNullOrUndefined(t)){var s=t.parentElement.querySelector("."+i);this.segmentIndex<1&&(s=t.parentElement.querySelector("."+r)),sf.base.isNullOrUndefined(s)||s.style.removeProperty("z-index"),t.remove()}},e.prototype.updateTaskBarEditElement=function(e){var t,s=this,n=this.getElementByPosition(e),a=this.parent.getParentElement(n,i);if(this.taskBarEditElement=a,this.taskBarEditElement&&!this.tappedPoint){var o=a.style.cursor;if("mousedown"===e.type||"touchstart"===e.type||"click"===e.type||this.checkPointerType(e)){if(this.dataGuid=this.taskBarEditElement.getAttribute("rowuniqueid"),this.parent.dotNetRef.invokeMethodAsync("GetConnectorValues").then((function(e){s.connectorPointWidth=e})),this.taskBarEditAction=this.getTaskBarAction(e,o),this.parent.isTaskbarEditingEventMapped&&this.parent.dotNetRef.invokeMethodAsync("OnTaskbarEditing",this.dataGuid,this.taskBarEditAction).then((function(e){s.restrictTaskbarEdit=e})),"ProgressResizing"==this.taskBarEditAction){var l=a.querySelectorAll("."+r);!sf.base.isNullOrUndefined(l)&&l.length>1&&(this.segmentIndex=0)}else sf.base.isNullOrUndefined(this.parent.getParentElement(n,r))||(this.segmentElement=this.parent.getParentElement(n,r),this.segmentIndex=Number(this.segmentElement.getAttribute("data-segment-index")),0!=this.segmentIndex&&"ChildDrag"==this.taskBarEditAction?t=this.segmentElement:"RightResizing"!=this.taskBarEditAction&&"LeftResizing"!=this.taskBarEditAction||(t=this.segmentElement));this.parent.dotNetRef.invokeMethodAsync("GetEditedRecord",this.dataGuid,this.taskBarEditAction,this.segmentIndex).then((function(t){s.taskBarEditRecord=t,s.updateMouseDownProperties(e)})),this.isTaskbarHold=!0,sf.base.isNullOrUndefined(t)||(this.taskBarEditElement=t)}}else if(this.taskBarEditElement&&this.tappedPoint){var h=a.getAttribute("rowuniqueid");this.parent.dotNetRef.invokeMethodAsync("RenderConnectorLine",this.tappedPoint,this.dataGuid,h),this.initPublicProp()}},e.prototype.updateMouseDownProperties=function(e){var t=this.getCoordinate(e);if(t.pageX||t.pageY){var i=this.parent.getOffsetRect(this.chartBodyContainer);this.parent.enableRTL?this.mouseDownX=Math.abs(t.pageX-(i.left+Math.abs(this.parent.chartPreviousScroll.left))):this.mouseDownX=t.pageX-i.left+Math.abs(this.parent.chartPreviousScroll.left),this.mouseDownY=t.pageY-i.top+this.parent.chartPreviousScroll.top}},e.prototype.getTaskBarAction=function(e,t){var i=this.getElementByPosition(e),s=(this.taskBarEditRecord,"");return i.classList.contains("e-taskbar-left-resizer")?s="LeftResizing":i.classList.contains("e-taskbar-right-resizer")?s="RightResizing":i.classList.contains("e-child-progress-resizer")||sf.base.closest(i,".e-child-progress-resizer")?s="ProgressResizing":i.classList.contains("e-connectorpoint-left-hover")?(s="ConnectorPointLeftDrag",("touchstart"===e.type||"click"===e.type||this.checkPointerType(e))&&(this.tappedPoint="ConnectorPointLeftDrag")):i.classList.contains("e-connectorpoint-right-hover")?(s="ConnectorPointRightDrag",("touchstart"===e.type||"click"===e.type||this.checkPointerType(e))&&(this.tappedPoint="ConnectorPointRightDrag")):"Auto"===this.parent.options.taskMode&&(sf.base.closest(i,".e-gantt-parent-taskbar")||sf.base.closest(i,".e-gantt-parent-milestone"))&&"default"!=t?s="ParentDrag":sf.base.closest(i,".e-gantt-child-taskbar")&&"default"!=t?s="ChildDrag":sf.base.closest(i,".e-gantt-milestone")&&"default"!=t&&(s="MilestoneDrag"),s},e.prototype.getElementByPosition=function(e){var t=this.getCoordinate(e);return t.pageX=null!=t.pageX?t.pageX:0,t.pageY=null!=t.pageY?t.pageY:0,document.elementFromPoint(t.pageX-window.pageXOffset,t.pageY-window.pageYOffset)},e.prototype.getCoordinate=function(e){var t={};if("touchstart"===e.type||"touchmove"===e.type||"touchend"===e.type||"touchleave"===e.type){var i=e;t.pageX=i.changedTouches[0].pageX,t.pageY=i.changedTouches[0].pageY}else{i=e;t.pageX=i.pageX,t.pageY=i.pageY}return t},e.prototype.ganttMouseMove=function(e){if("touchmove"!==e.type&&"click"!==e.type&&!this.checkPointerType(e)||this.taskbarCanDrag){if(this.isDown&&!this.taskBarEditAction&&!this.parentRow&&"mousemove"==e.type||this.LongPress&&this.isDown){var t=e.target,i=this.parent.getParentElement(t,"e-chart-scroll-container");this.parent.chartPreviousScroll.left>0&&i.scrollWidth-this.chartBodyContainer.offsetWidth>this.parent.chartPreviousScroll.left&&e.preventDefault();var s=this.parent.getOffsetRect(this.chartBodyContainer),r=this.getCoordinate(e);if(this.parent.enableRTL?this.mouseMoveX=Math.abs(r.pageX-(s.left+Math.abs(this.parent.chartPreviousScroll.left))):this.mouseMoveX=r.pageX-s.left+this.parent.chartPreviousScroll.left,this.emptyRow){var n=document.getElementById(this.parent.element.id+"_editingtooltip_content");if(!sf.base.isNullOrUndefined(n))n.style.left=this.event.pageX-10+"px",n.style.top=this.event.pageY-80+"px",sf.base.select(".e-arrow-tip",n).style.left="2px";(this.mouseMoveX-this.mouseDownX>2||this.mouseDownX-this.mouseMoveX>2)&&(this.mouseMoveX>this.mouseDownX?(this.taskBarWidth=this.mouseMoveX-this.mouseDownX,this.taskBarRight=this.mouseDownX+this.taskBarWidth,this.parent.dotNetRef.invokeMethodAsync("UpdateTaskbarResizingData",this.dataGuid,this.mouseDownX,this.taskBarRight)):(this.taskBarWidth=this.mouseDownX-this.mouseMoveX,this.taskBarRight=this.mouseDownX-this.taskBarWidth,this.parent.dotNetRef.invokeMethodAsync("UpdateTaskbarResizingData",this.dataGuid,this.taskBarRight,this.mouseDownX))),this.emptyRow.style.width=this.taskBarWidth+"px",this.parent.enableRTL?this.emptyRow.style.left=this.mouseMoveX<this.mouseDownX?i.scrollWidth-this.mouseDownX+"px":i.scrollWidth-this.taskBarRight+"px":this.emptyRow.style.left=this.mouseMoveX>this.mouseDownX?this.mouseDownX+"px":this.taskBarRight+"px",this.emptyRow.style.backgroundColor="";var a=0;(a=this.parent.enableRTL?Math.abs(Math.abs(this.mouseMoveX)-Math.abs(this.parent.chartPreviousScroll.left)+s.left):this.mouseMoveX-this.parent.chartPreviousScroll.left+s.left)+20>s.left+this.chartBodyContainer.offsetWidth?(this.timerCount=this.parent.chartPreviousScroll.left,this.startScrollTimer("right")):a+40>s.left+this.chartBodyContainer.offsetWidth&&this.parent.enableRTL&&0==this.parent.chartPreviousScroll.left?(this.parent.chartPreviousScroll.left=-1,this.timerCount=this.parent.chartPreviousScroll.left,this.startScrollTimer("right")):a-10<s.left?(this.timerCount=this.parent.chartPreviousScroll.left,this.startScrollTimer("left")):null!=this.scrollTimer&&this.stopScrollTimer()}}if(this.taskBarEditAction&&this.taskBarEditRecord&&this.isTaskbarHold){s=this.parent.getOffsetRect(this.chartBodyContainer),r=this.getCoordinate(e);this.parent.enableRTL?this.mouseMoveX=Math.abs(r.pageX-(s.left+Math.abs(this.parent.chartPreviousScroll.left))):this.mouseMoveX=r.pageX-s.left+this.parent.chartPreviousScroll.left,this.mouseMoveY=r.pageY-s.top+this.parent.chartPreviousScroll.top,this.dragMouseLeave=!1,this.isMouseDragCheck(),this.isMouseDragging&&(this.taskBarEditingAction(e,!1),1===this.dblClick&&"ConnectorPointRightDrag"!==this.taskBarEditAction&&"ConnectorPointLeftDrag"!==this.taskBarEditAction&&("ProgressResizing"!==this.taskBarEditAction&&this.createResizeElement(),this.taskBarEditElement.style.setProperty("z-index","4"),this.createTaskbarClone(this.taskBarEditElement),this.dblClick++))}var o=sf.base.closest(e.target,".e-taskbar-main-container");o&&"default"!=o.style.cursor&&(this.parent.options.allowTaskbarEditing&&"ConnectorPointLeftDrag"!==this.taskBarEditAction&&"ConnectorPointRightDrag"!==this.taskBarEditAction?o.style.cursor="move":o.style.cursor="auto")}},e.prototype.isMouseDragCheck=function(){if(!this.isMouseDragging&&this.taskBarEditAction&&(this.mouseDownX!==this.mouseMoveX&&(this.mouseDownX+3<this.mouseMoveX||this.mouseDownX-3>this.mouseMoveX)||this.mouseDownY!==this.mouseMoveY&&(this.mouseDownY+3<this.mouseMoveY||this.mouseDownY-3>this.mouseMoveY))){this.isMouseDragging=!0;var e=this.taskBarEditRecord;this.previousItem=n({},e),this.taskBarEditElement.setAttribute("aria-grabbed","true")}},e.prototype.updateMouseMoveProperties=function(e){var t=this.parent.getOffsetRect(this.chartBodyContainer),i=this.getCoordinate(e),s="ConnectorPointLeftDrag"===this.taskBarEditAction||"ConnectorPointRightDrag"===this.taskBarEditAction,r=!("ChildDrag"!==this.taskBarEditAction&&"MilestoneDrag"!==this.taskBarEditAction||!this.parent.options.allowTaskbarDragAndDrop);if((i.pageX||i.pageY)&&(this.parent.enableRTL?this.mouseMoveX=Math.abs(i.pageX-(t.left+Math.abs(this.parent.chartPreviousScroll.left))):this.mouseMoveX=i.pageX-t.left+this.parent.chartPreviousScroll.left,this.mouseMoveY=i.pageY-t.top+this.parent.chartPreviousScroll.top),this.taskBarEditRecord.width>3&&("ProgressResizing"!==this.taskBarEditAction||0!==this.taskBarEditRecord.progress&&100!==this.taskBarEditRecord.progress)||s){var n=0;n=this.parent.enableRTL?Math.abs(Math.abs(this.mouseMoveX)-Math.abs(this.parent.chartPreviousScroll.left)+t.left):this.mouseMoveX-this.parent.chartPreviousScroll.left+t.left;var a=this.mouseMoveY-this.parent.chartPreviousScroll.top+t.top;n+20>t.left+this.chartBodyContainer.offsetWidth?(this.timerCount=this.parent.chartPreviousScroll.left,this.startScrollTimer("right")):n+40>t.left+this.chartBodyContainer.offsetWidth&&this.parent.enableRTL&&0==this.parent.chartPreviousScroll.left?(this.parent.chartPreviousScroll.left=-1,this.timerCount=this.parent.chartPreviousScroll.left,this.startScrollTimer("right")):n-10<t.left?(this.timerCount=this.parent.chartPreviousScroll.left,this.startScrollTimer("left")):(s||r)&&a>t.top+this.chartBodyContainer.offsetHeight-80?(this.timerCount=this.parent.chartPreviousScroll.top,this.startScrollTimer("bottom")):(s||r)&&a-25<t.top?(this.timerCount=this.parent.chartPreviousScroll.top,this.startScrollTimer("top")):null!=this.scrollTimer&&this.stopScrollTimer()}e.preventDefault()},e.prototype.longPress=function(e){var t=this;e?t.longPressTimer=window.setTimeout((function(){t.LongPress=!0}),1e3):(t.LongPress=!1,window.clearTimeout(t.longPressTimer))},e.prototype.startScrollTimer=function(e){var t=this;this.parent.chartScrollModule.previousCount=0,this.stopScrollTimer();var i=0,s=this.parent.element.querySelector(".e-chart-root-container");this.scrollTimer=window.setInterval((function(){if(-1==Math.sign(t.timerCount)&&(i=-1,t.timerCount=Math.abs(t.timerCount)),"right"===e)t.timerCount=t.timerCount+1>=t.parent.totalTimelineWidth?t.parent.totalTimelineWidth:t.timerCount+1;else if("bottom"!==e||sf.base.isNullOrUndefined(s))t.timerCount=t.timerCount-1<0?0:t.timerCount-1;else{var r=t.parent.options.enableRowVirtualization?parseFloat(s.querySelector(".e-virtualtrack").style.height):parseFloat(s.querySelector(".e-chart-rows-container").style.height);t.timerCount=t.timerCount+1>=r?r:t.timerCount+1}"bottom"===e||"top"===e?t.parent.chartScrollModule.setScrollTop(t.timerCount):(t.parent.chartScrollModule.setScrollLeft(t.timerCount,i),t.parent.options.enableTimelineVirtualization&&t.parent.chartScrollModule.updateContent()),"ConnectorPointLeftDrag"!==t.taskBarEditAction&&"ConnectorPointRightDrag"!==t.taskBarEditAction||t.drawFalseLine()}),0)},e.prototype.stopScrollTimer=function(){window.clearInterval(this.scrollTimer),this.scrollTimer=null},e.prototype.taskBarEditingAction=function(e,t){this.restrictTaskbarEdit||this.updateMouseMoveProperties(e),"ProgressResizing"!==this.taskBarEditAction||this.restrictTaskbarEdit?"LeftResizing"!==this.taskBarEditAction||this.restrictTaskbarEdit?"RightResizing"!==this.taskBarEditAction&&"ParentResizing"!==this.taskBarEditAction||this.restrictTaskbarEdit?"ParentDrag"!==this.taskBarEditAction&&"ChildDrag"!==this.taskBarEditAction&&"MilestoneDrag"!==this.taskBarEditAction||this.restrictTaskbarEdit?"ConnectorPointLeftDrag"!==this.taskBarEditAction&&"ConnectorPointRightDrag"!==this.taskBarEditAction||"touchmove"==e.type&&!this.checkPointerType(e)||this.restrictConnectorLineEdit||(this.updateConnectorLineSecondProperties(e),this.triggerDependencyEvent(e),this.drawFalseLine()):this.enableDragging(e):this.enableRightResizing(e):this.enableLeftResizing(e):(this.updateSplitTaskProgress(),this.performProgressResize(e)),this.restrictTaskbarEdit||this.setItemPosition(),"ConnectorPointLeftDrag"!==this.taskBarEditAction&&"ConnectorPointRightDrag"!==this.taskBarEditAction&&this.parent.dotNetRef.invokeMethodAsync("UpdateResizingData",this.dataGuid,this.taskBarEditAction,this.taskBarEditRecord),this.taskbarTouchEditing=!0,this.updateProgress&&this.updateTooltip(e)},e.prototype.updateTooltip=function(e){var t=document.getElementById(this.parent.element.id+"_editingtooltip_content");if(!sf.base.isNullOrUndefined(t)){var s=this.taskBarEditRecord,r=s.width,n=s.progressWidth,a=this.parent.getOffsetRect(this.taskBarEditElement);if(-1!=this.segmentIndex&&"ProgressResizing"==this.taskBarEditAction){var o=sf.base.closest(this.taskBarEditElement,"."+i);sf.base.isNullOrUndefined(o)||(a=this.parent.getOffsetRect(o))}var l=this.parent.tooltipModule.getPointerPosition(e),h=sf.base.select(".e-arrow-tip",t),c=h.offsetWidth/2+(t.offsetWidth-t.clientWidth);if(h.style.left="2px","ConnectorPointLeftDrag"===this.taskBarEditAction||"ConnectorPointRightDrag"===this.taskBarEditAction)return;"LeftResizing"===this.taskBarEditAction?t.style.left=a.left-c+"px":"RightResizing"===this.taskBarEditAction||"ParentResizing"===this.taskBarEditAction?t.style.left=(this.parent.enableRTL?a.left-r-c:a.left+r-c)+"px":"ProgressResizing"===this.taskBarEditAction?t.style.left=(this.parent.enableRTL?a.left-n-c:a.left+n-c)+"px":t.style.left=l.x-c+"px",this.parent.options.allowTaskbarDragAndDrop&&(t.style.top=Math.abs(a.top-(t.offsetHeight+10))+"px")}},e.prototype.setItemPosition=function(){var e=this.taskBarEditRecord,t=this.parent.enableRTL?"right":"left";if(null!=e&&null!=this.taskBarEditElement&&(null!=sf.base.closest(this.taskBarEditElement,"tr.e-chart-row")||this.parent.options.allowTaskbarDragAndDrop&&this.parent.options.enableRowVirtualization)){var s,n="MilestoneDrag"===this.taskBarEditAction||e.isMilestone?this.milestoneHeight:e.width,a=n-10,o=n-2,l=!sf.base.isNullOrUndefined(e.segmentData)&&this.segmentIndex>-1?e.segmentData.segmentProgress/100*e.width:e.progress/100*e.width;s=this.taskBarEditElement.classList.contains(r)||this.taskBarEditElement.classList.contains(i)||"ChildDrag"===this.taskBarEditAction||"LeftResizing"===this.taskBarEditAction?this.taskBarEditElement:sf.base.closest(this.taskBarEditElement,"tr.e-chart-row").querySelector("."+i);var h=this.taskBarEditElement.querySelector(".e-gantt-child-progressbar"),c=this.taskBarEditElement.querySelector(".e-gantt-child-taskbar"),d=this.taskBarEditElement.querySelector(".e-child-progress-resizer"),u=this.taskBarEditElement.querySelector(".e-taskbar-right-resizer"),f=(this.taskBarEditElement.querySelector(".e-gantt-parent-taskbar"),this.taskBarEditElement.querySelector(".e-gantt-parent-progressbar")),p=this.taskBarEditElement.querySelector(".e-right-connectorpoint-outer-div");this.parent.biggerMode&&(a=n-24,o=n+10);var m=this.parent.element.querySelector(".e-chart-rows-container").querySelector(".e-gantt-chart-resize-indicator");if(!sf.base.isNullOrUndefined(m)&&("ChildDrag"===this.taskBarEditAction||"ParentDrag"===this.taskBarEditAction||"RightResizing"===this.taskBarEditAction||"LeftResizing"===this.taskBarEditAction)){var g=0;if(sf.base.isNullOrUndefined(this.segmentElement)||sf.base.isNullOrUndefined(e.segmentData)||!(this.segmentIndex>0||0==this.segmentIndex&&("RightResizing"===this.taskBarEditAction||"LeftResizing"===this.taskBarEditAction))||(g=e.segmentData.taskbarLeft),m.style.width=n+"px",m.style.setProperty(t,e.left+g+"px"),this.parent.options.enableTimelineVirtualization&&this.parent.options.enableRowVirtualization){var b=this.parent.chartScrollModule.getTimelineLeft();m.style.left=e.left+(this.parent.enableRTL?b:-b)+"px"}}if(s.classList.contains("e-gantt-clone-taskbar")&&"ParentResizing"!==this.taskBarEditAction&&"ConnectorPointLeftDrag"!==this.taskBarEditAction&&"ConnectorPointRightDrag"!==this.taskBarEditAction&&("ProgressResizing"!=this.taskBarEditAction&&(s.style.width=n+"px",s.style.setProperty(t,e.left+"px")),this.parent.options.allowTaskbarDragAndDrop&&("ChildDrag"===this.taskBarEditAction&&this.segmentIndex<1||"MilestoneDrag"===this.taskBarEditAction)&&(s.style.top=e.top+"px")),p&&p.style.setProperty(t,o+"px"),(!u||sf.base.isNullOrUndefined(c)||c.classList.contains(r))&&("RightResizing"!==this.taskBarEditAction||-1==this.segmentIndex||sf.base.isNullOrUndefined(u))||u.style.setProperty(t,a+"px"),"MilestoneDrag"===this.taskBarEditAction||e.isMilestone)s.style.setProperty(t,e.left-n/2+2+"px"),sf.base.isNullOrUndefined(m)||m.style.setProperty("left",e.left-n/2+"px");else if("ProgressResizing"===this.taskBarEditAction)if(-1==this.segmentIndex)c.style.setProperty(t,e.left+e.progressWidth-10+"px"),sf.base.isNullOrUndefined(h)||(h.style.width=e.progressWidth+"px",h.style.borderBottomRightRadius=this.progressBorderRadius+"px",h.style.borderTopRightRadius=this.progressBorderRadius+"px",d.style.setProperty(t,e.progressWidth-8+"px"));else{if(this.updateProgress){this.updateSegmentProgress(e);for(var v=this.taskBarEditElement.querySelectorAll(".e-gantt-child-progressbar"),y=0;y<v.length;y++)v[y].style.width=e.segmentData.segments[y].progressWidth+"px"}else this.updateProgress=!1;h.style.borderBottomRightRadius=this.progressBorderRadius+"px",h.style.borderTopRightRadius=this.progressBorderRadius+"px",d.style.setProperty(t,e.progressWidth-8+"px")}else s.classList.contains("e-gantt-clone-taskbar")&&"RightResizing"===this.taskBarEditAction&&!sf.base.isNullOrUndefined(c)?(c.style.width=n+"px",sf.base.isNullOrUndefined(h)||(h.style.width=l+"px",u.style.setProperty(t,a+"px"),d.style.setProperty(t,l-8+"px"))):s.classList.contains("e-gantt-clone-taskbar")&&"RightResizing"===this.taskBarEditAction&&s.classList.contains(r)?u.style.setProperty(t,a+"px"):"ParentDrag"===this.taskBarEditAction?sf.base.isNullOrUndefined(h)||(f.style.width=e.progressWidth+"px"):(!s.classList.contains("e-gantt-clone-taskbar")||sf.base.isNullOrUndefined(c)||c.classList.contains(r)||(c.style.width=n+"px"),s.classList.contains("e-gantt-clone-taskbar")&&!sf.base.isNullOrUndefined(h)&&(sf.base.isNullOrUndefined(c)||c.classList.contains(r)||u.style.setProperty(t,a+"px"),0==this.segmentIndex&&this.parent.options.allowTaskbarDragAndDrop||(h.style.width=l+"px"),sf.base.isNullOrUndefined(d)||d.style.setProperty(t,l-8+"px")))}},e.prototype.enableLeftResizing=function(e){var t=this.taskBarEditRecord,i=0;this.taskBarEditElement.classList.contains(r)?this.enableSplitTaskLeftResize():(this.mouseDownX>this.mouseMoveX?this.mouseMoveX<t.left+t.width?(i=this.mouseDownX-this.mouseMoveX,t.left>0&&(t.left=this.previousItem.left-i,t.width=this.previousItem.width+i)):this.mouseMoveX>t.left+t.width&&(i=this.mouseDownX-this.mouseMoveX,t.left=this.previousItem.left-i,t.width=3):this.mouseMoveX<t.left+t.width?(i=this.mouseMoveX-this.mouseDownX,t.left<t.left+t.width&&this.previousItem.left+i<=this.previousItem.left+this.previousItem.width&&(t.left=this.previousItem.left+i,t.width=this.previousItem.width-i)):(i=this.mouseMoveX-this.mouseDownX,t.left=this.previousItem.left+i,t.width=3),this.updateEditPosition(e,t)),t.left=this.previousItem.left+this.previousItem.width-t.width},e.prototype.enableRightResizing=function(e){var t=this.taskBarEditRecord,i=0;if(this.mouseDownX>this.mouseMoveX?this.mouseMoveX>t.left&&this.mouseDownX-this.mouseMoveX>3?(i=this.mouseDownX-this.mouseMoveX,t.width=this.previousItem.width-i):this.mouseMoveX<t.left&&(t.width=3):this.mouseMoveX>t.left&&(i=this.mouseMoveX-this.mouseDownX,t.width=this.previousItem.width+i),!sf.base.isNullOrUndefined(t.segmentData)&&!sf.base.isNullOrUndefined(this.segmentElement)&&-1!=this.segmentIndex){var s=0;s=0==this.segmentIndex?t.segmentData.nextLeft:t.segmentData.nextLeft-this.segmentElement.offsetLeft,0!=t.segmentData.nextLeft&&t.width>Math.abs(s)&&(t.width=Math.abs(s))}sf.base.isNullOrUndefined(this.segmentElement)&&this.updateEditPosition(e,t)},e.prototype.enableDragging=function(e){var t=this.taskBarEditRecord,i=0;if(this.mouseDownX>this.mouseMoveX?(i=this.mouseDownX-this.mouseMoveX)>0&&(t.left=this.previousItem.left-i):(i=this.mouseMoveX-this.mouseDownX,t.left=this.previousItem.left+i),!sf.base.isNullOrUndefined(null!=t.segmentData)&&!sf.base.isNullOrUndefined(this.segmentElement)&&this.segmentIndex>0){var s=t.segmentData.previousLeft+t.segmentData.taskbarLeft,r=t.left,n=t.segmentData.segments.length;r=this.segmentIndex>0&&n-1!=this.segmentIndex?t.segmentData.taskbarLeft+t.left<s+t.segmentData.previousWidth?t.segmentData.previousLeft+t.segmentData.previousWidth:t.width+t.left>t.segmentData.nextLeft?t.segmentData.nextLeft-t.width:t.left:t.segmentData.taskbarLeft+t.left<s+t.segmentData.previousWidth?t.segmentData.previousLeft+t.segmentData.previousWidth:t.left,t.left=r}var a=this.chartBodyContainer.querySelector(".e-task-table"),o=t.left<0?0:t.left+t.width>=a.offsetWidth?a.offsetWidth-t.width:t.left;this.parent.options.allowTaskbarDragAndDrop&&(t.top=this.previousItem.top+Math.abs(15-this.mouseMoveY)),t.left=o},e.prototype.performProgressResize=function(e){var t=this.taskBarEditRecord,i=0;this.mouseDownX>this.mouseMoveX?this.mouseMoveX>t.left&&this.mouseMoveX<t.left+t.width&&t.left>0?(i=this.mouseMoveX-t.left,t.progressWidth=i):this.mouseMoveX>=t.left+t.width?t.progressWidth=t.width:t.progressWidth=0:this.mouseMoveX>t.left&&this.mouseMoveX<t.left+t.width?(i=this.mouseMoveX-t.left,t.progressWidth=i):this.mouseMoveX<=t.left?t.progressWidth=0:t.progressWidth=t.width;var s=t.progressWidth>t.width?t.width:t.progressWidth;s=t.progressWidth<0?0:t.progressWidth,t.progressWidth=s;var r=t.width-t.progressWidth;this.progressBorderRadius=r<=4?4-r:0},e.prototype.updateEditPosition=function(e,t){this.updateIsMilestone(t)},e.prototype.updateIsMilestone=function(e){e.width<=3?(e.width=3,e.isMilestone=!0):e.isMilestone=!1},e.prototype.updateConnectorLineSecondProperties=function(e){var t=this.getElementByPosition(e),s=this.parent.getParentElement(t,i);this.connectorSecondAction=null;var r=0;this.parent.getParentElement(t,"e-connectorpoint-left")?(this.connectorSecondAction="ConnectorPointLeftDrag",this.toPredecessorText="Start"):this.parent.getParentElement(t,"e-connectorpoint-right")?(this.connectorSecondAction="ConnectorPointRightDrag",this.toPredecessorText="Finish"):(this.connectorSecondAction=null,this.toPredecessorText=null),this.taskBarEditElement!==s&&this.taskBarEditElement!==this.highlightedSecondElement&&(this.parent.options.enableRowVirtualization&&(r=this.getTopPosition(this.parent.element)),(this.parent.options.enableRowVirtualization&&0==this.elementOffsetLeft||!this.parent.options.enableRowVirtualization)&&(this.elementOffsetLeft=this.taskBarEditElement.offsetLeft,this.elementOffsetTop=this.taskBarEditElement.offsetTop+r,this.elementOffsetWidth=this.taskBarEditElement.offsetWidth,this.elementOffsetHeight=this.taskBarEditElement.offsetHeight,this.showHideTaskBarEditingElements(s,this.highlightedSecondElement))),sf.base.isNullOrUndefined(this.connectorSecondAction)&&!sf.base.isNullOrUndefined(this.connectorSecondElement)&&(this.connectorSecondElement.querySelector(".e-connectorpoint-left").classList.remove("e-connectorpoint-allow-block"),this.connectorSecondElement.querySelector(".e-connectorpoint-right").classList.remove("e-connectorpoint-allow-block")),this.connectorSecondElement=this.connectorSecondAction?s:null,this.toDataGuid=sf.base.isNullOrUndefined(this.connectorSecondElement)?null:this.connectorSecondElement.getAttribute("rowuniqueid")},e.prototype.getTopPosition=function(e){var t,i=e.querySelector(".e-virtualtable"),s=i.style.transform.split(",");if(s.length>1)t=s[1].trim().split(")")[0];else{var r=i.style.transform;t=r.substring(r.lastIndexOf("(")+1,r.lastIndexOf(")"))}return parseFloat(t)},e.prototype.showHideTaskBarEditingElements=function(e,t){if((t=t||this.taskBarEditElement)&&e!==t&&(t.querySelector(".e-taskbar-left-resizer")&&(t.querySelector(".e-taskbar-left-resizer").classList.remove("e-left-resize-gripper"),t.querySelector(".e-taskbar-right-resizer").classList.remove("e-right-resize-gripper"),t.querySelector(".e-child-progress-resizer")&&t.querySelector(".e-child-progress-resizer").classList.remove("e-progress-resize-gripper")),t.querySelector(".e-connectorpoint-left")||t.parentElement.querySelector(".e-connectorpoint-left"))){var i=sf.base.isNullOrUndefined(t.querySelector(".e-connectorpoint-left"))?t.parentElement:t;i.querySelector(".e-connectorpoint-left").classList.remove("e-connectorpoint-left-hover"),i.querySelector(".e-connectorpoint-right").classList.remove("e-connectorpoint-right-hover")}},e.prototype.triggerDependencyEvent=function(e,t){var i=this;this.parent.dotNetRef.invokeMethodAsync("DrawConnectorLine",this.taskBarEditAction,this.connectorSecondAction,this.toDataGuid).then((function(t){if(t){var s=document.getElementById(i.parent.element.id+"_editingtooltip_content");if(!sf.base.isNullOrUndefined(s)){var r=i.parent.tooltipModule.getPointerPosition(e),n=sf.base.select(".e-arrow-tip",s).offsetWidth/2+(s.offsetWidth-s.clientWidth);s.style.left=r.x-n+"px",s.style.visibility="visible"}}else{var a=i.getElementByPosition(e);(a.classList.contains("e-connectorpoint-left")||a.classList.contains("e-connectorpoint-right"))&&a.classList.add("e-connectorpoint-allow-block")}}))},e.prototype.drawFalseLine=function(){var e,t=this.mouseDownX,i=this.mouseDownY,s=this.mouseMoveX,r=this.mouseMoveY,n=Math.sqrt((t-s)*(t-s)+(i-r)*(i-r)),a=180*Math.atan2(r-i,s-t)/Math.PI,o="rotate("+(this.parent.enableRTL?-a:a)+"deg)",l=0;sf.base.isNullOrUndefined(document.querySelectorAll(".e-chart-row")[0])||(l=document.querySelectorAll(".e-chart-row")[0].offsetWidth),"ConnectorPointLeftDrag"===this.taskBarEditAction&&(e=this.parent.enableRTL?l-(this.elementOffsetLeft+this.elementOffsetWidth+this.connectorPointWidth/2)-Math.abs(this.parent.chartPreviousScroll.left):this.elementOffsetLeft-this.connectorPointWidth/2-Math.abs(this.parent.chartPreviousScroll.left)),"ConnectorPointRightDrag"===this.taskBarEditAction&&(e=this.parent.enableRTL?l-(this.elementOffsetLeft-this.connectorPointWidth/2)-Math.abs(this.parent.chartPreviousScroll.left):this.elementOffsetLeft+this.elementOffsetWidth+this.connectorPointWidth/2-Math.abs(this.parent.chartPreviousScroll.left));var h=this.elementOffsetTop+this.elementOffsetHeight/2+this.chartBodyContainer.offsetTop-this.parent.chartPreviousScroll.top;this.removeFalseLine(!1),this.falseLine=document.createElement("div"),this.falseLine.className="e-gantt-false-line",this.falseLine.id="ganttfalseline"+this.parent.element.id,this.falseLine.style.position="absolute",this.falseLine.style.transform=o,this.falseLine.style.borderTopWidth="1px",this.falseLine.style.borderTopStyle="dashed",this.falseLine.style.zIndex="5",this.falseLine.style.width=n-3+"px",this.falseLine.style.top=h+"px",this.parent.enableRTL?(this.falseLine.style.left="auto",this.falseLine.style.right=e+"px",this.falseLine.style.transformOrigin="100% 0%"):(this.falseLine.style.right="auto",this.falseLine.style.left=e+"px",this.falseLine.style.transformOrigin="0% 100%"),this.chartBodyContainer.appendChild(this.falseLine)},e.prototype.removeFalseLine=function(e){this.falseLine&&(sf.base.remove(this.falseLine),this.falseLine=null,e&&(this.elementOffsetLeft=0,this.elementOffsetTop=0,this.elementOffsetWidth=0,this.elementOffsetHeight=0))},e.prototype.updateSegmentProgress=function(e){for(var t=e.segmentData.segments,i=!0,s=e.width,r=s*Math.ceil(e.progressWidth/s*100)/100,n=r,a=0,o=0;o<t.length;o++){var l=t[o];if(0!=o)if(l.left<=n)r=n-l.left;else r=0;delete l.progressWidth,r>0&&r>l.width?(r-=l.width,l.progressWidth=l.width,a+=l.progressWidth):i&&(l.progressWidth=r,r-=l.width,i=!1,a+=l.progressWidth)}e.segmentData.segmentProgressWidth=a},e.prototype.enableSplitTaskLeftResize=function(){var e=this.taskBarEditRecord,t=0;this.mouseDownX>this.mouseMoveX?this.mouseMoveX<e.segmentData.taskbarLeft+e.left+e.width&&(t=this.mouseDownX-this.mouseMoveX,e.left>0?(e.left=this.previousItem.left-t,e.width=this.previousItem.width+t,e.segmentData.taskbarLeft+e.left<e.segmentData.taskbarLeft+e.segmentData.previousLeft+e.segmentData.previousWidth&&(t=e.segmentData.taskbarLeft+e.segmentData.previousLeft+e.segmentData.previousWidth-(e.segmentData.taskbarLeft+e.left),e.left=this.previousItem.left+t,e.width-=t)):e.left<=0&&0==this.segmentIndex&&(e.left=this.previousItem.left-t,e.width=this.previousItem.width+t)):this.mouseMoveX<e.segmentData.taskbarLeft+e.width+e.left&&(t=this.mouseMoveX-this.mouseDownX,0==this.segmentIndex&&e.left<=0?e.width=e.width-t:e.left<e.left+e.width&&(e.left=this.previousItem.left+t,e.width=this.previousItem.width-t))},e.prototype.updateSplitTaskProgress=function(){if(null!=this.taskBarEditElement.querySelector(".e-child-progress-resizer")&&-1!=this.segmentIndex){var e=this.taskBarEditElement.querySelector(".e-child-progress-resizer"),t=e.offsetLeft<0?1:e.offsetLeft+8;this.taskBarEditRecord.segmentData.updateTooltip=this.updateProgress=!1;for(var i=0;i<this.taskBarEditRecord.segmentData.segments.length;i++){var s=this.taskBarEditRecord.segmentData.segments[i];(0==s.segmentIndex&&s.left<=t&&s.width>t||0!=s.segmentIndex&&s.left<t&&s.left+s.width>t)&&(this.updateProgress=!0,this.taskBarEditRecord.segmentData.updateTooltip=!0)}}},e.prototype.destroy=function(e){this.stopScrollTimer(),this.removeEventListeners(e)},e}(),o=function(){function e(e){this.parent=e,this.chartPane=this.parent.element.querySelector(".e-gantt-chart-pane"),this.addEventListeners()}return e.prototype.addEventListeners=function(){sf.base.EventHandler.add(this.chartPane,sf.base.Browser.touchStartEvent,this.ganttMouseMove,this),sf.base.EventHandler.add(this.chartPane,sf.base.Browser.touchMoveEvent,this.ganttMouseMove,this)},e.prototype.removeEventListeners=function(e){var t=e.element.querySelector(".e-gantt-chart-pane");sf.base.EventHandler.remove(t,sf.base.Browser.touchStartEvent,this.ganttMouseMove),sf.base.EventHandler.remove(t,sf.base.Browser.touchMoveEvent,this.ganttMouseMove)},e.prototype.ganttMouseMove=function(e){this.tooltipMouseEvent=e},e.prototype.tooltipOpened=function(e){var t=document.getElementById(e.element.id);if(!sf.base.isNullOrUndefined(this.tooltipMouseEvent)){var i=this.getPointerPosition(this.tooltipMouseEvent),s=this.parent.getOffsetRect(this.chartPane),r=s.top+this.chartPane.offsetHeight,n=s.left+this.chartPane.offsetWidth,a=i.x,o=i.y;if(n<a+t.offsetWidth+10)for(;n<a+t.offsetWidth+10;)a=n-t.offsetWidth-10,t.style.left=a+"px";else a+=10,t.style.left=a+"px";window.innerHeight<t.offsetHeight+o&&(o=o-t.offsetHeight-10),r<o+t.offsetHeight+20?o=o-t.offsetHeight-10:o+=10,t.style.top=o+"px",t.style.visibility="visible"}},e.prototype.predecessorTooltipOpened=function(e){var t=document.getElementById(e.element.id);if(!sf.base.isNullOrUndefined(this.tooltipMouseEvent)){var i=this.getPointerPosition(this.tooltipMouseEvent),s=this.parent.getOffsetRect(this.chartPane),r=s.top+this.chartPane.offsetHeight,n=s.left+this.chartPane.offsetWidth,a=i.x,o=i.y;if(n<a+t.offsetWidth+10)for(;n<a+t.offsetWidth+10;)a=n-t.offsetWidth-10,t.style.left=a+"px";else a=a,t.style.left=a+"px";if(window.innerHeight<t.offsetHeight+o&&(o=o-t.offsetHeight-10),r<o+t.offsetHeight+20?o=o-t.offsetHeight-10:o+=10,document.getElementsByClassName("e-arrow-tip e-tip-bottom").length>0)document.getElementsByClassName("e-arrow-tip e-tip-bottom")[0].style.left=i.x-t.offsetLeft+"px";t.style.visibility="visible"}},e.prototype.getPointerPosition=function(e){var t,i;return sf.base.isNullOrUndefined(sf.base.getValue("pageX",e))&&sf.base.isNullOrUndefined(sf.base.getValue("pageY",e))?sf.base.isNullOrUndefined(sf.base.getValue("touches[0].pageX",e))&&sf.base.isNullOrUndefined(sf.base.getValue("touches[0].pageY",e))?sf.base.isNullOrUndefined(sf.base.getValue("clientX",e))&&sf.base.isNullOrUndefined(sf.base.getValue("clientY",e))||(t=sf.base.getValue("clientX",e)+document.body.scrollLeft+document.documentElement.scrollLeft,i=sf.base.getValue("clientY",e)+document.body.scrollTop+document.documentElement.scrollTop):(t=sf.base.getValue("touches[0].pageX",e),i=sf.base.getValue("touches[0].pageY",e)):(t=sf.base.getValue("pageX",e),i=sf.base.getValue("pageY",e)),{x:t,y:i}},e.prototype.destroy=function(e){this.removeEventListeners(e)},e}(),l=function(){function i(e,t,i,s){this.toolbarHeight=0,this.chartPreviousScroll={top:0,left:0},this.treegridPreviousScroll={top:0,left:0},window.sfBlazor=window.sfBlazor,this.element=t,this.dotNetRef=s,this.isFromTreeGrid=!1,this.options=i,this.dataId=e,window.sfBlazor.setCompInstance(this),this.enableRTL=i.enableRtl,this.enableAdaptiveUI=i.enableAdaptiveUI}return i.prototype.getOffsetRect=function(e){var t=e.getBoundingClientRect(),i=window.pageYOffset||document.documentElement.scrollTop||document.body.scrollTop,s=window.pageXOffset||document.documentElement.scrollLeft||document.body.scrollLeft,r=document.documentElement.clientTop||document.body.clientTop||0,n=document.documentElement.clientLeft||document.body.clientLeft||0,a=t.top+i-r,o=this.enableRTL?t.right+s-n:t.left+s-n;return{top:Math.round(a),left:Math.round(o),width:t.width,height:t.height}},i.prototype.getParentElement=function(e,t,i){for(var s=e;s&&!(i?s.id===t:s.classList.contains(t));)s=s.parentElement;return s},i.prototype.destroy=function(){e.prototype.destroy(this),a.prototype.destroy(this),o.prototype.destroy(this),t.prototype.destroy(this)},i}(),h=function(){function e(e){this.activeElementName=null,this.activeElement=null,this.parent=e}return e.prototype.taskbarFocus=function(e){var t=e.querySelector(".e-gantt-child-taskbar"),r=e.querySelector(".e-gantt-parent-taskbar"),n=e.querySelector(".e-gantt-milestone"),a=e.querySelector(".e-gantt-parent-milestone");return this.activeElementName=i,n?(this.activeElement=n,n.focus(),e.querySelector("."+i).querySelector(".e-gantt-milestone").classList.add(s)):a?(this.activeElement=a,a.focus(),e.querySelector("."+i).querySelector(".e-gantt-parent-milestone").classList.add(s)):t?(this.activeElement=t,t.focus(),e.querySelector("."+i).querySelector(".e-gantt-child-taskbar").classList.add(s)):(this.activeElement=r,r.focus(),e.querySelector("."+i).querySelector(".e-gantt-parent-taskbar").classList.add(s)),"isNextTaskbar"},e.prototype.RightLabelFocus=function(e){var t=e.querySelector(".e-right-label-container");return this.activeElementName="e-right-label-container",this.activeElement=t,t.focus(),t.querySelector(".e-label").classList.add(s),"isNextRightLabel"},e.prototype.LeftLabelFocus=function(e){var t=e.querySelector(".e-left-label-container");return this.activeElementName="e-left-label-container",this.activeElement=t,t.focus(),t.querySelector(".e-label").classList.add(s),"isNextLeftLabel"},e}();return{initialize:function(e,t,i,s,r,n){var a={taskMode:i.taskMode,allowTaskbarEditing:i.allowTaskbarEditing,enableRowVirtualization:i.enableRowVirtualization,enableTimelineVirtualization:i.enableTimelineVirtualization,allowSchedulingOnDrag:i.allowSchedulingOnDrag,enableRtl:i.enableRtl,enableAdaptiveUI:i.enableAdaptiveUI},o=new l(e,t,a,s);this.dotnetRef=s;var h={};this.instance=window.sfBlazor.getCompInstance(e);var c=o.element.querySelector("#"+o.element.id+"_Gantt_Splitter");return o.height=h.height=t.offsetHeight,o.isMobileDevice=sf.base.Browser.isDevice,o.width=h.width=t.offsetWidth,o.toolbarElement=t.querySelector("#"+t.id+"_Gantt_Toolbar"),sf.base.isNullOrUndefined(o.toolbarElement)||(o.toolbarHeight=h.toolbarHeight=o.toolbarElement.offsetHeight),document.body.classList.contains("e-bigger")?o.biggerMode=h.biggerMode=!0:this.instance.enableAdaptiveUI&&(o.biggerMode=h.biggerMode=!0,document.body.classList.add("e-bigger")),c.style.height="calc(100% - "+o.toolbarHeight+"px)",h.ganttOffsetSize=this.ChartInitialize(i.timelineWidth,i.milestoneHeight,i.taskbarEvent,i.connectorLineEvent),this.getTreeGrid(e,r,n),h},refreshGantt:function(e){var t=window.sfBlazor.getCompInstance(e);return document.body.classList.contains("e-bigger")?t.biggerMode=!0:t.biggerMode=!1,t.biggerMode},adjustTable:function(e,t){var i,s=e.chartScrollModule,r=s.chartVirtualTable,n=s.chartRootElement.querySelector(".e-virtualtrack");if(e.options.enableRowVirtualization){var a=document.getElementById("treeGrid"+e.element.id+"_gridcontrol"),o=a.querySelector(".e-gridcontent").querySelector(".e-virtualtable"),l=a.querySelector(".e-gridcontent").querySelector(".e-virtualtrack");i=new DOMMatrixReadOnly(r.style.transform);var h=new DOMMatrixReadOnly(o.style.transform);sf.base.isNullOrUndefined(o)||sf.base.isNullOrUndefined(r)||(n.style.height=l.style.height,""==r.style.transform?r.style.transform=o.style.transform:r.style.transform="translate("+i.m41+"px,"+h.m42+"px)")}if(e.options.enableTimelineVirtualization){var c=3*e.element.offsetWidth;i=new DOMMatrixReadOnly(r.style.transform);var d=t<c?0:t<i.m41?t-c:i.m41;n.style.width=s.timelineVirtualTrack.offsetWidth+"px",r.style.transform="translate("+d+"px,"+i.m42+"px)",sf.base.isNullOrUndefined(s.dependencyViewContainer)||(s.dependencyViewContainer.style.left=-d+"px")}},setChartHeight:function(e,t){var i=window.sfBlazor.getCompInstance(e),s=0,r={chartHeight:0,viewPortHeight:0},n=i.chartScrollModule,a=i.element.querySelector(".e-holiday-container"),o=i.element.querySelector(".e-gridcontent").querySelector(".e-virtualtrack"),l=i.element.querySelector(".e-gridcontent").querySelector(".e-content");if(l.setAttribute("tabindex","0"),i.element.querySelector(".e-gridcontent").querySelector(".e-table").setAttribute("role","treegrid"),sf.base.isNullOrUndefined(o)&&(o=i.element.querySelector("#treeGrid"+i.element.id+"_gridcontrol_content_table")),o&&o.clientHeight){var h=i.element.querySelector("#treeGrid"+i.element.id+"_gridcontrol"),c=o.clientHeight+"px",d=n.chartRootElement;n.chartTaskTable=d.querySelector(".e-task-table");var u=n.chartTaskTable.clientHeight,f=d.getElementsByClassName("e-chart-rows-container")[0],p=h.querySelectorAll(".e-content")[0].clientHeight,m=n.timelineHeaderElement;p<o.clientHeight?(s=o.clientHeight,f.style.height=i.options.enableRowVirtualization?u+"px":c,m.style.width="calc(100% - "+(i.isMobileDevice?0:i.scrollWidth)+"px)",l.style.cssText+="width: calc(100% + "+(i.scrollWidth+1)+"px);",this.viewPortHeight=p):(s=p,f.style.height=n.element.clientHeight-1+"px",m.style.width="100%",l.style.cssText+="width: calc(100% + 1px);",this.viewPortHeight=f.clientHeight),r.chartHeight=i.options.enableRowVirtualization?s-u:s,r.viewPortHeight=this.viewPortHeight}return!sf.base.isNullOrUndefined(i)&&i.enableRTL&&(i.element.querySelector(".e-gridheader").style.paddingLeft="0"),this.adjustTable(i,t),sf.base.isNullOrUndefined(a)||n.updateTopPosition(),n.updateScrollTopPosition(),r},treegridHeaderAlign:function(e,t){if(t){sf.base.addClass(e.treeGridElement.querySelectorAll(".e-headercell"),"e-timeline-single-header-outer-div"),sf.base.addClass(e.treeGridElement.querySelectorAll(".e-columnheader"),"e-timeline-single-header-outer-div");var i=e.element.querySelector(".e-gridheader").offsetHeight;e.element.querySelector(".e-gridcontent").style.height="calc(100% - "+i+"px)"}else sf.base.removeClass(e.treeGridElement.querySelectorAll(".e-headercell"),"e-timeline-single-header-outer-div"),sf.base.removeClass(e.treeGridElement.querySelectorAll(".e-columnheader"),"e-timeline-single-header-outer-div")},getTreeGrid:function(e,t,i){var s=window.sfBlazor.getCompInstance(e);this.hideTreeGridScrollBar(s,i),sf.base.isNullOrUndefined(s.treeGridElement)||(this.instance.treeGrid=s.treeGridElement.blazor_instance),this.treegridHeaderAlign(s,t)},hideTreeGridScrollBar:function(e,i){e.treeGridElement=e.element.querySelector("#treeGrid"+e.element.id);var s=e.treeGridElement.querySelector(".e-content"),r=s.querySelector(".e-table"),n=e.treeGridElement.querySelector(".e-headercontent");s.style.cssText+="overflow: auto;",n.style.width="calc(100% + 1px)";i?(e.treeGridModule=new t(e,s),e.scrollWidth=this.getScrollbarWidth(e.element),s.clientHeight>r.clientHeight?s.style.cssText+="width: calc(100% + 1px);":s.clientHeight<r.clientHeight?(0==e.scrollWidth&&!sf.base.isNullOrUndefined(window.navigator.userAgent)&&window.navigator.userAgent.toLocaleLowerCase().includes("windows")&&"mozilla"===sf.base.Browser.info.name&&(e.scrollWidth=17),e.scrollWidth=e.isMobileDevice?16:e.scrollWidth,s.style.cssText+="width: calc(100% + "+(e.scrollWidth+1)+"px);"):s.classList.add("e-gantt-scroll-padding")):(s&&(s.style.height="100%",e.element.querySelector(".e-gridcontent").style.height="calc(100% - "+e.element.querySelector(".e-gridheader").offsetHeight+"px)",e.treeGridModule=new t(e,s)),e.scrollWidth=this.getScrollbarWidth(e.element),s.clientHeight>r.clientHeight?s.style.cssText+="width: calc(100% + 1px);":s.clientHeight<r.clientHeight?(0==e.scrollWidth&&!sf.base.isNullOrUndefined(window.navigator.userAgent)&&window.navigator.userAgent.toLocaleLowerCase().includes("windows")&&"mozilla"===sf.base.Browser.info.name&&(e.scrollWidth=17),e.scrollWidth=e.isMobileDevice?16:e.scrollWidth,s.style.cssText+="width: calc(100% + "+(e.scrollWidth+1)+"px);"):s.classList.add("e-gantt-scroll-padding"))},getScrollbarWidth:function(e){var t=e,i=document.createElement("div");i.style.visibility="hidden",i.style.overflow="scroll",i.style.msOverflowStyle="scrollbar";var s=document.createElement("div");i.appendChild(s),t.appendChild(i);var r=i.offsetWidth-s.offsetWidth;return i.parentNode.removeChild(i),r},getOffsetLeft:function(e){var t=this.instance.element.getBoundingClientRect(),i=window.pageXOffset||document.documentElement.scrollLeft||document.body.scrollLeft,s=document.documentElement.clientLeft||document.body.clientLeft||0,r=t.left+i-s;return Math.round(r)},tooltipOpened:function(e,t){this.instance.tooltipModule.tooltipOpened(t)},predecessorTooltipOpened:function(e,t){this.instance.tooltipModule.predecessorTooltipOpened(t)},setTooltipVisibility:function(){var e=document.getElementById(this.instance.element.id+"_editingtooltip_content");sf.base.isNullOrUndefined(e)||(e.style.visibility="visible")},removeEmptyRowElement:function(){sf.base.isNullOrUndefined(this.instance.taskbarEditModule.chartEmptyRowElement)||(sf.base.remove(this.instance.taskbarEditModule.chartEmptyRowElement),this.instance.taskbarEditModule.chartEmptyRowElement=null)},ChartInitialize:function(t,i,s,r){return this.instance.totalTimelineWidth=t,this.instance.isTaskbarEditingEventMapped=s,this.instance.isConnectorLineEventMapped=r,this.instance.keyboardModule=new h(this.instance),this.instance.options.allowTaskbarEditing&&(this.instance.taskbarEditModule=new a(this.instance),this.instance.taskbarEditModule.milestoneHeight=i),this.instance.tooltipModule=new o(this.instance),this.instance.chartScrollModule=new e(this.instance,this.instance.element),this.instance.spinnerShown=!1,{viewPortHeight:this.instance.element.querySelector(".e-gridcontent").querySelector(".e-content").clientHeight,chartWidth:this.instance.element.querySelector(".e-gantt-chart-pane").offsetWidth}},UpdateScroll:function(e,t){this.scrollBarElement=this.instance.element.querySelector(".e-chart-scroll-container"),this.instance.chartScrollModule.previousCount=-1;var i=this.scrollBarElement.scrollLeft;i=this.instance.enableRTL&&-1==Math.sign(i)?-i:i,(this.scrollBarElement.offsetWidth<t||i>t)&&(this.scrollBarElement.scrollLeft=this.instance.enableRTL?-t:t)},chartFocusOutHandler:function(e,t){var i=this.instance.element.querySelector(".e-chart-row-cell[data-uid="+e+"]"),r=i.querySelector("."+s)?i.querySelector("."+s).parentElement:null;r&&(r.querySelector(".e-label").classList.remove(s),r.blur())},focusOutHandler:function(e){var t=e.querySelector("."+s);sf.base.isNullOrUndefined(t)||t.classList.remove(s),sf.base.isNullOrUndefined(e.classList)||e.classList.remove(s)},resetPrivateProperties:function(e){this.instance.taskbarEditModule.tappedPoint=null},setActiveElement:function(e,t){this.instance.keyboardModule.activeElement=e,this.instance.keyboardModule.activeElementName=t},tabFocusHandler:function(e,t,r,n){var a=this.instance.element.querySelector(".e-chart-row-cell[data-uid="+e+"]"),o="isNextLeftLabel",l=a.querySelector("."+s)?a.querySelector("."+s).parentElement:null;if("ArrowDown"!=n&&"ArrowUp"!=n||null!=l){if(l){if(null==l&&null!=this.instance.keyboardModule.activeElement){if("Tab"==n||"ArrowRight"==n){switch(this.instance.keyboardModule.activeElementName){case"e-left-label-container":o=this.instance.keyboardModule.taskbarFocus(a);break;case i:o=this.instance.keyboardModule.RightLabelFocus(a);break;default:this.instance.keyboardModule.activeElement=null,this.instance.keyboardModule.activeElementName=null,o="isFirstGridCell"}return o}switch(this.instance.keyboardModule.activeElementName){case"e-left-label-container":this.instance.keyboardModule.activeElement=null,this.instance.keyboardModule.activeElementName=null,o="isLastGridCell";break;case i:o=this.instance.keyboardModule.LeftLabelFocus(a);break;default:o=this.instance.keyboardModule.taskbarFocus(a)}return o}return l.classList.contains("e-left-label-inner-div")?r?(l.querySelector(".e-label").classList.remove(s),l.blur(),this.instance.keyboardModule.activeElement=null,this.instance.keyboardModule.activeElementName=null,"isLastGridCell"):this.instance.keyboardModule.taskbarFocus(a):l.classList.length?l.classList.contains("e-right-label-inner-div")?r?this.instance.keyboardModule.taskbarFocus(a):"ArrowRight"==n||"Tab"==n?(this.instance.keyboardModule.activeElement=null,this.instance.keyboardModule.activeElementName=null,"isFirstGridCell"):(this.instance.keyboardModule.activeElement=null,this.instance.keyboardModule.activeElementName=null,"isLastGridCell"):"":r?a.querySelector(".e-left-label-inner-div")?this.instance.keyboardModule.LeftLabelFocus(a):(this.instance.keyboardModule.activeElement=null,this.instance.keyboardModule.activeElementName=null,"isLastGridCell"):a.querySelector(".e-right-label-inner-div")?this.instance.keyboardModule.RightLabelFocus(a):"ArrowRight"==n||"Tab"==n?(this.instance.keyboardModule.activeElement=null,this.instance.keyboardModule.activeElementName=null,"isFirstGridCell"):(this.instance.keyboardModule.activeElement=null,this.instance.keyboardModule.activeElementName=null,"isLastGridCell")}return o=!r&&"ArrowLeft"!=n&&a.querySelector(".e-left-label-inner-div")?this.instance.keyboardModule.LeftLabelFocus(a):r&&a.querySelector(".e-right-label-inner-div")?this.instance.keyboardModule.RightLabelFocus(a):this.instance.keyboardModule.taskbarFocus(a)}switch(this.focusOutHandler(this.instance.keyboardModule.activeElement),this.instance.keyboardModule.activeElementName){case i:this.instance.keyboardModule.taskbarFocus(a);break;case"e-left-label-container":this.instance.keyboardModule.LeftLabelFocus(a);break;default:this.instance.keyboardModule.RightLabelFocus(a)}return""},updateScrollLeft:function(e,t,i){var s=this.instance.element.querySelector(".e-chart-scroll-container");if(null!=i){var r=this.instance.element.querySelector(".e-row").offsetHeight,n=i*r;(s.offsetHeight-r/2+s.scrollTop<n||s.scrollTop>n)&&(s.scrollTop=n)}if(t=t>0?t:0,t=s.scrollWidth<=t?s.scrollWidth:t,s.offsetWidth+s.scrollLeft<t||s.scrollLeft>t){var a=t-50;s.scrollLeft=this.instance.enableRTL?-a:a}},destroy:function(e){var t=window.sfBlazor.getCompInstance(e);null!=t&&null!=t.dotNetRef&&t.destroy()}}}()}}]);(async()=>{await import(`${document.baseURI}_content/Syncfusion.Blazor/scripts/syncfusion-blazor-base.min.js?v=26.1.undefined`).then(()=>{sfBlazor.loadDependencies('sfgantt');})})();