@page "/ComRemit/PayeeList"
@rendermode @(new InteractiveServerRenderMode(prerender: false))
@inject ComRemitPayeeService PayeeService
@inject IJSRuntime JSRuntime
@inject NavigationManager Navigation
@using Intra2025.Services
@using Intra2025.Models.ComRemit
@using Intra2025.Components.Base
@using Microsoft.AspNetCore.Components.Web
@using System.Web
@inherits BasePageComponent

<PageTitle>收款人資料維護</PageTitle>

<!-- 統一的樣式覆蓋 -->
<style>
    .payee-page {
        width: 100%;
        margin: 0;
        padding: 20px;
        box-sizing: border-box;
    }
</style>

<div class="payee-page">
    <div class="card">
        <div class="card-header">
            <h4 style="color: #0033CC; font-weight: bold;">【匯款資料維護(step1)】</h4>
        </div>
        <div class="card-body" style="padding: 20px;">
            <!-- 搜尋區 -->
            <div style="margin-bottom: 20px;">
                <div style="display: flex; flex-wrap: wrap; align-items: center; gap: 15px;">
                    <div style="display: flex; align-items: center; gap: 10px;">
                        <label class="form-label" style="white-space: nowrap; margin: 0;">可搜尋【收款人戶名、金融局號、帳號】:</label>
                        <input type="text" class="form-control" @bind="searchText" @onkeypress="OnSearchKeyPress"
                               placeholder="輸入收款人戶名、金融局號或帳號" style="width: 300px;" />
                        <button class="btn btn-primary" @onclick="Search">搜尋</button>
                    </div>
                    <div style="margin-left: auto;">
                        <button class="btn btn-success" @onclick="AddNew">資料新增</button>
                        @if (!string.IsNullOrEmpty(errorMessage))
                        {
                            <button class="btn btn-info ms-2" @onclick="ReloadData">重新載入</button>
                        }
                    </div>
                </div>
            </div>

            <!-- 載入狀態 -->
            @if (isLoading)
            {
                <div style="text-align: center; padding: 50px 0;">
                    <div class="spinner-border" role="status">
                        <span class="visually-hidden">載入中...</span>
                    </div>
                    <p style="margin-top: 10px;">正在載入資料...</p>
                </div>
            }
            else if (!string.IsNullOrEmpty(errorMessage))
            {
                <div class="alert alert-danger">
                    <h5>發生錯誤:</h5>
                    <p>@errorMessage</p>
                    <button class="btn btn-warning" @onclick="ReloadData">重試</button>
                </div>
            }
            else
            {
                <!-- 資料表格 -->
                <div style="width: 100%; overflow-x: auto;">
                    <table class="table table-bordered table-striped" style="width: 100%; min-width: 1000px;">
                        <thead>
                            <tr>
                                <th style="background-color: #9C7EC6; color: white; padding: 12px; text-align: center;">收款人金融局號</th>
                                <th style="background-color: #9C7EC6; color: white; padding: 12px; text-align: center;">收款人帳號</th>
                                <th style="background-color: #9C7EC6; color: white; padding: 12px; text-align: center;">收款人戶名</th>
                                <th style="background-color: #9C7EC6; color: white; padding: 12px; text-align: center;">身分證字號(統編)</th>
                                <th style="background-color: #9C7EC6; color: white; padding: 12px; text-align: center;">電話</th>
                                <th style="background-color: #9C7EC6; color: white; padding: 12px; text-align: center;">郵遞區號</th>
                                <th style="background-color: #9C7EC6; color: white; padding: 12px; text-align: center;">住址</th>
                                <th style="background-color: #9C7EC6; color: white; padding: 12px; text-align: center;">資料是否提供單位共用</th>
                                <th style="background-color: #9C7EC6; color: white; padding: 12px; text-align: center; width: 120px;">功能</th>
                            </tr>
                        </thead>
                        <tbody>
                            @if (currentPageData != null && currentPageData.Any())
                            {
                                @foreach (var payee in currentPageData)
                                {
                                    <tr>
                                        <td style="padding: 8px;">@(payee.CollectNo ?? "N/A")</td>
                                        <td style="padding: 8px;">@(payee.CollecAcc ?? "N/A")</td>
                                        <td style="padding: 8px;">@(payee.CollectName ?? "N/A")</td>
                                        <td style="padding: 8px;">@(payee.CollectId ?? "N/A")</td>
                                        <td style="padding: 8px;">@(payee.Tel ?? "N/A")</td>
                                        <td style="padding: 8px;">@(payee.Zip ?? "N/A")</td>
                                        <td style="padding: 8px;">@(payee.Addr ?? "N/A")</td>
                                        <td style="padding: 8px; text-align: center;">
                                            @if (payee.Shared == "是")
                                            {
                                                <span class="badge bg-success">共用</span>
                                            }
                                            else
                                            {
                                                <span class="badge bg-warning">不共用</span>
                                            }
                                        </td>
                                        <td style="padding: 8px; text-align: center;">
                                            <button class="btn btn-link text-primary p-0 me-2" @onclick="() => Edit(payee.CollectNo ?? string.Empty, payee.CollecAcc ?? string.Empty)">編輯</button>
                                            <button class="btn btn-link text-danger p-0" @onclick="() => Delete(payee.CollectNo ?? string.Empty, payee.CollecAcc ?? string.Empty)">刪除</button>
                                        </td>
                                    </tr>
                                }
                            }
                            else
                            {
                                <tr>
                                    <td colspan="9" style="text-align: center; padding: 40px;">目前沒有資料</td>
                                </tr>
                            }
                        </tbody>
                    </table>
                </div>

                <!-- 分頁 -->
                @if (totalPages > 1)
                {
                    <div style="display: flex; justify-content: center; align-items: center; margin-top: 20px; gap: 10px;">
                        <!-- 第1頁 -->
                        <button class="btn @(currentPage == 1 ? "btn-secondary" : "btn-outline-primary") btn-sm" 
                                @onclick="@(async () => await GoToFirstPage())" 
                                disabled="@(currentPage == 1)">
                            第1頁
                        </button>
                        
                        <!-- 上1頁 -->
                        <button class="btn @(currentPage == 1 ? "btn-secondary" : "btn-outline-primary") btn-sm" 
                                @onclick="@(async () => await GoPreviousPage())" 
                                disabled="@(currentPage == 1)">
                            上1頁
                        </button>
                        
                        <!-- 目前第N頁 -->
                        <span class="badge bg-primary fs-6 px-3 py-2">
                            目前第 @currentPage 頁 (共 @totalPages 頁)
                        </span>
                        
                        <!-- 下1頁 -->
                        <button class="btn @(currentPage == totalPages ? "btn-secondary" : "btn-outline-primary") btn-sm" 
                                @onclick="@(async () => await GoNextPage())" 
                                disabled="@(currentPage == totalPages)">
                            下1頁
                        </button>
                        
                        <!-- 最後1頁 -->
                        <button class="btn @(currentPage == totalPages ? "btn-secondary" : "btn-outline-primary") btn-sm" 
                                @onclick="@(async () => await GoToLastPage())" 
                                disabled="@(currentPage == totalPages)">
                            最後1頁
                        </button>
                    </div>
                }

                <!-- 資料統計資訊 -->
                <div style="margin-top: 20px; color: #6c757d; font-size: 0.9rem;">
                    <p>共 @allPayees.Count 筆資料 @(filteredPayees.Count != allPayees.Count ? $"，搜尋結果: {filteredPayees.Count} 筆" : "")</p>
                </div>
            }
        </div>
    </div>
</div>

@code {
    private List<Payee> allPayees = new List<Payee>();
    private List<Payee> filteredPayees = new List<Payee>();
    private List<Payee> currentPageData = new List<Payee>();

    private string searchText = "";
    private int currentPage = 1;
    private int pageSize = 15;
    private int totalPages = 0;
    private bool isLoading = true;
    private string errorMessage = "";

    protected override async Task OnInitializedAsync()
    {
        // SSO 驗證已在 BasePageComponent 中自動執行
        await base.OnInitializedAsync();
        await LoadData();
    }

    private async Task LoadData()
    {
        try
        {
            isLoading = true;
            errorMessage = "";
            StateHasChanged();

            Logger.LogInformation("開始載入PayeeList資料...");

            // 先測試資料庫連接
            var connectionTest = await PayeeService.TestConnectionAsync();
            Logger.LogInformation($"資料庫連接測試結果: {connectionTest}");

            if (!connectionTest)
            {
                errorMessage = "資料庫連接失敗";
                Logger.LogError("資料庫連接失敗，停止載入資料");
                return;
            }

            // 檢查用戶是否已登入
            if (_userState == null || string.IsNullOrEmpty(_userState.Account))
            {
                errorMessage = "用戶未登入或帳號資訊不完整";
                Logger.LogError("用戶未登入，停止載入資料");
                return;
            }

            // 根據登入用戶帳號載入資料 (權限控制)
            Logger.LogInformation($"UserState 詳細資訊 - Account: {_userState.Account}, IsInitialized: {_userState.IsInitialized}, DepCode: {_userState.DepCode}, UnitName: {_userState.UnitName}");

            // 除錯：檢查資料庫中的資料
            var debugPayees = await PayeeService.DebugGetPayeesByAccountAsync(_userState.Account);
            Logger.LogInformation($"除錯查詢結果：找到 {debugPayees.Count} 筆符合帳號 {_userState.Account} 的資料");

            Logger.LogInformation($"開始執行 GetPayeesByUserAccountAsync，用戶帳號: {_userState.Account}");
            allPayees = await PayeeService.GetPayeesByUserAccountAsync(_userState.Account);
            Logger.LogInformation($"GetPayeesByUserAccountAsync 完成，共 {allPayees?.Count ?? 0} 筆資料");

            if (allPayees == null)
            {
                Logger.LogWarning("allPayees 為 null，初始化為空列表");
                allPayees = new List<Payee>();
            }

            filteredPayees = allPayees.ToList();
            Logger.LogInformation($"filteredPayees 設定完成，共 {filteredPayees.Count} 筆資料");

            UpdatePagination();
            Logger.LogInformation($"分頁更新完成，當前頁資料數: {currentPageData?.Count ?? 0}");

            Logger.LogInformation("PayeeList資料載入成功");
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "PayeeList載入錯誤: {Message}", ex.Message);
            errorMessage = $"載入資料時發生錯誤: {ex.Message}";
            if (ex.InnerException != null)
            {
                errorMessage += $"\n詳細錯誤: {ex.InnerException.Message}";
                Logger.LogError(ex.InnerException, "內部錯誤: {Message}", ex.InnerException.Message);
            }
        }
        finally
        {
            isLoading = false;
            Logger.LogInformation($"LoadData 完成，isLoading = {isLoading}, errorMessage = '{errorMessage}'");
            StateHasChanged();
        }
    }

    private async Task ReloadData()
    {
        await LoadData();
    }

    private async Task Search()
    {
        try
        {
            // 檢查用戶是否已登入
            if (_userState == null || string.IsNullOrEmpty(_userState.Account))
            {
                errorMessage = "用戶未登入或帳號資訊不完整";
                StateHasChanged();
                return;
            }

            if (string.IsNullOrWhiteSpace(searchText))
            {
                // 重新載入該用戶的所有資料
                allPayees = await PayeeService.GetPayeesByUserAccountAsync(_userState.Account);
                filteredPayees = allPayees.ToList();
            }
            else
            {
                // 使用多欄位搜尋方法（收款人戶名、金融局號、帳號）
                filteredPayees = await PayeeService.SearchPayeesByMultipleFieldsAndUserAccountAsync(searchText, _userState.Account);
            }

            currentPage = 1;
            UpdatePagination();
            StateHasChanged();
        }
        catch (Exception ex)
        {
            errorMessage = $"搜尋時發生錯誤: {ex.Message}";
            StateHasChanged();
        }
    }

    private async Task OnSearchKeyPress(KeyboardEventArgs e)
    {
        if (e.Key == "Enter")
        {
            await Search();
        }
    }

    private void UpdatePagination()
    {
        try
        {
            totalPages = (int)Math.Ceiling((double)filteredPayees.Count / pageSize);
            int skip = (currentPage - 1) * pageSize;
            currentPageData = filteredPayees.Skip(skip).Take(pageSize).ToList();
            Logger.LogInformation($"UpdatePagination: 總資料數={filteredPayees.Count}, 總頁數={totalPages}, 目前頁={currentPage}, 當前頁資料數={currentPageData.Count}");
        }
        catch (Exception ex)
        {
            errorMessage = $"更新分頁時發生錯誤: {ex.Message}";
            Logger.LogError(ex, "UpdatePagination 錯誤: {Message}", ex.Message);
        }
    }

    private void GoToPage(int page)
    {
        try
        {
            if (page >= 1 && page <= totalPages)
            {
                currentPage = page;
                UpdatePagination();
                StateHasChanged();
                Logger.LogInformation($"跳轉到第 {page} 頁，共 {totalPages} 頁，當前頁資料數: {currentPageData.Count}");
            }
        }
        catch (Exception ex)
        {
            errorMessage = $"換頁時發生錯誤: {ex.Message}";
            StateHasChanged();
        }
    }

    private async Task GoToFirstPage()
    {
        GoToPage(1);
        await Task.CompletedTask;
    }

    private async Task GoPreviousPage()
    {
        if (currentPage > 1)
        {
            GoToPage(currentPage - 1);
        }
        await Task.CompletedTask;
    }

    private async Task GoNextPage()
    {
        if (currentPage < totalPages)
        {
            GoToPage(currentPage + 1);
        }
        await Task.CompletedTask;
    }

    private async Task GoToLastPage()
    {
        GoToPage(totalPages);
        await Task.CompletedTask;
    }

    private void AddNew()
    {
        NavigationManager.NavigateTo($"{Environment.GetEnvironmentVariable("ASPNETCORE_APPL_PATH")?.TrimEnd('/')}/ComRemit/PayeeMt");
    }

    private void Edit(string collectNo, string collecAcc)
    {
        // 使用複合主鍵的編碼方式來傳遞參數
        var encodedParams = $"{collectNo}|{collecAcc}";
        NavigationManager.NavigateTo($"{Environment.GetEnvironmentVariable("ASPNETCORE_APPL_PATH")?.TrimEnd('/')}/ComRemit/PayeeMt/{System.Web.HttpUtility.UrlEncode(encodedParams)}");
    }

    private async Task Delete(string collectNo, string collecAcc)
    {
        try
        {
            var confirmed = await JS.InvokeAsync<bool>("confirm", "確定要刪除這筆資料嗎？");
            if (confirmed)
            {
                var success = await PayeeService.DeletePayeeAsync(collectNo, collecAcc);

                if (success)
                {
                    await JS.InvokeVoidAsync("alert", "刪除成功！");

                    // 重新載入資料以反映刪除的變更
                    await LoadData();
                }
                else
                {
                    await JS.InvokeVoidAsync("alert", "刪除失敗！");
                }
            }
        }
        catch (Exception ex)
        {
            await JS.InvokeVoidAsync("alert", $"刪除失敗: {ex.Message}");
        }
    }
}
