@using Intra2025.Components.Base
@using Intra2025.Services
@inject UserState _userState
@inject SsoService sso
@inject IJSRuntime JSRuntime
@inject ILogger<BaseLayoutComponent> Logger
@implements IDisposable

<!-- 基礎佈局組件，提供共同的初始化邏輯和結構 -->
<div class="page">
    <header class="top-header">
        <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
            <div class="container-fluid">
                <!-- 系統標題 -->
                <a class="navbar-brand" href="@(Environment.GetEnvironmentVariable("ASPNETCORE_APPL_PATH")?.TrimEnd('/'))/">
                    @SystemTitle
                </a>

                <!-- 響應式導航按鈕 -->
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav"
                    aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                    <span class="navbar-toggler-icon"></span>
                </button>

                <!-- 導航內容 -->
                <div class="collapse navbar-collapse" id="navbarNav">
                    @if (isInitialized)
                    {
                        <ul class="navbar-nav ms-auto">
                            @NavigationContent

                            <!-- 使用者資訊 -->
                            <UserInfoComponent />
                        </ul>
                    }
                </div>
            </div>
        </nav>
    </header>

    <!-- 主要內容區域 -->
    <main class="content">
        @if (isInitialized)
        {
            @ChildContent
        }
        else
        {
            <LoadingIndicatorComponent LoadingMessage="使用者驗證中，請稍候..." />
        }
    </main>
</div>

<!-- 錯誤處理 UI -->
<ErrorUIComponent />

@code {
    [Parameter] public string SystemTitle { get; set; } = "系統";
    [Parameter] public RenderFragment? NavigationContent { get; set; }
    [Parameter] public RenderFragment? ChildContent { get; set; }

    private bool isInitialized = false;

    protected override async Task OnInitializedAsync()
    {
        // 監聽 UserState 的變化，以便在狀態更新時觸發 UI 重新渲染
        _userState.OnStateChanged += OnStateChanged;

        // 執行驗證
        await sso.ValidateAndInitializeUserAsync();

        // 標記初始化完成，並觸發第一次手動渲染
        isInitialized = true;
        StateHasChanged();
    }

    private void OnStateChanged()
    {
        // 當 UserState 改變時，調用 StateHasChanged 來更新 UI
        InvokeAsync(StateHasChanged);
    }

    public void Dispose()
    {
        // 取消監聽，防止記憶體洩漏
        _userState.OnStateChanged -= OnStateChanged;
    }
}
