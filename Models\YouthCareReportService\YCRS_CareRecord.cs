﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Intra2025.Models
{
    // Youth & Child Care Reporting Service (YCRS)
    public class YCRS_CareRecord
    {
        [Key]
        public string? Id { get; set; } // 系統代碼
        public DateTime? BelongDate { get; set; } // 案件所屬月份

        [Required(ErrorMessage = "【照顧者】為必填欄位")]
        [MaxLength(50, ErrorMessage = "【照顧者】不可超過 50 個字元")]
        public string? Caregiver { get; set; } // 照顧者

        [Required(ErrorMessage = "【個案類型】為必填欄位")]
        [MaxLength(5, ErrorMessage = "【個案類型】不可超過 5 個字元")]
        public string? AttributesId { get; set; } // 個案類型

        public string? NumberOfCare { get; set; }  //照顧人數

        [MaxLength(6, ErrorMessage = "【個案所屬戶所】不可超過 6 個字元")]
        public string? CaseBelongId { get; set; } // 個案所屬戶所

        public bool Visitcare { get; set; } // 是否訪視關懷

        public bool FamilyMediation { get; set; } // 是否進行家事協調

        public bool IsChecked { get; set; } // 是否確認過此筆資料

        public bool IsMailed { get; set; } // 是否已MAIL通知過此筆資料給社會處承辦人

        [MaxLength(300, ErrorMessage = "【備註】不可超過 300 個字元")]
        public string? Remarks { get; set; } // 備註

        public DateTime? CreateDate { get; set; } // 案件新增日期

        public string? CreateName { get; set; } // 填表人
    }

}
