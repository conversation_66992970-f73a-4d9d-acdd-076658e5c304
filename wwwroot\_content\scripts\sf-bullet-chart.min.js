/*!*  filename: sf-bullet-chart.min.js 
*     version : 26.1.41 
*     Copyright Syncfusion Inc. 2001 - 2024. All rights reserved. 
*     Use of this code is subject to the terms of our license.
*     A copy of the current license can be obtained at any time by e-mailing 
*     <EMAIL>. Any infringement will be prosecuted under 
*     applicable laws. 
*/
(window.webpackJsonp=window.webpackJsonp||[]).push([[10],{"./bundles/sf-bullet-chart.js":function(e,t,n){"use strict";n.r(t);n("./modules/sf-bullet-chart.js")},"./modules/sf-bullet-chart.js":function(e,t){window.sfBlazor=window.sfBlazor||{},window.sfBlazor.BulletChart=function(){"use strict";var e=function(){function e(e,t,n,i){window.sfBlazor=window.sfBlazor,this.resizeTo=0,this.chartKeyUpRef=null,this.chartKeyDownRef=null,this.mouseleave=function(){this.dotNetRef&&this.dotNetRef.invokeMethodAsync("OnMouseLeave")},this.documentKeyHandler=function(e){e.altKey&&74===e.keyCode&&!sf.base.isNullOrUndefined(this.element)&&this.element.focus()},this.element=n,this.dotNetRef=i,this.id=t,this.dataId=e,this.currentLegendIndex=0,this.previousTargetId="",window.sfBlazor.setCompInstance(this)}return e.prototype.wireEvents=function(){sf.base.EventHandler.add(this.element,"click",this.click.bind(this),this),sf.base.EventHandler.add(this.element,"mousemove",this.mousemove.bind(this),this),sf.base.EventHandler.add(this.element,sf.base.Browser.touchMoveEvent,this.mousemove.bind(this),this),sf.base.EventHandler.add(this.element,sf.base.Browser.touchStartEvent,this.mousemove.bind(this),this),sf.base.EventHandler.add(this.element,sf.base.Browser.isPointer?"pointerleave":"mouseleave",this.mouseleave.bind(this),this),window.addEventListener(sf.base.Browser.isTouch&&"orientation"in window&&"onorientationchange"in window?"orientationchange":"resize",this.resize.bind(this)),new sf.base.KeyboardEvents(this.element,{keyAction:this.keyActionHandler.bind(this),keyConfigs:{enter:"enter"},eventName:"keydown"}),this.chartKeyUpRef=this.chartOnKeyUp.bind(this,this.dotNetRef,this.id),this.chartKeyDownRef=this.chartOnKeyDown.bind(this,this.dotNetRef,this.id),sf.base.EventHandler.add(this.element,"mousedown",this.mouseDown.bind(this),this),sf.base.EventHandler.add(document.body,"keydown",this.documentKeyHandler,this),sf.base.EventHandler.add(this.element,"keyup",this.chartKeyUpRef),sf.base.EventHandler.add(this.element,"keydown",this.chartKeyDownRef)},e.prototype.unWireEvents=function(){sf.base.EventHandler.remove(this.element,"mousemove",this.mousemove),sf.base.EventHandler.remove(this.element,"click",this.click),sf.base.EventHandler.remove(this.element,sf.base.Browser.touchMoveEvent,this.mousemove),sf.base.EventHandler.remove(this.element,sf.base.Browser.touchStartEvent,this.mousemove),sf.base.EventHandler.remove(this.element,sf.base.Browser.isPointer?"pointerleave":"mouseleave",this.mouseleave),window.removeEventListener(sf.base.Browser.isTouch&&"orientation"in window&&"onorientationchange"in window?"orientationchange":"resize",this.resize.bind(this)),sf.base.EventHandler.remove(this.element,"mousedown",this.mouseDown),sf.base.EventHandler.remove(document.body,"keydown",this.documentKeyHandler),sf.base.EventHandler.remove(this.element,"keyup",this.chartKeyUpRef),sf.base.EventHandler.remove(this.element,"keydown",this.chartKeyDownRef);var e=sf.base.getInstance(this.element,this.keyActionHandler);e&&e.destroy(),this.element=null,this.dotNetRef=null},e.prototype.chartOnKeyDown=function(e,t,n){return this.dotNetRef=e,this.id=t,(n.code.indexOf("Arrow")>-1||"Space"==n.code||"Enter"==n.code)&&n.preventDefault(),!1},e.prototype.chartOnKeyUp=function(e,n,i){this.dotNetRef=e,this.id=n;var s=i.target.id,o=document.getElementById(this.element.id+"_chart_legend_translate_g");if(o){var r=o.firstElementChild,a=r.getAttribute("class");a&&-1===a.indexOf("e-chart-focused")?a+=" e-chart-focused":a="e-chart-focused",r.setAttribute("class",a)}if("Tab"==i.code||"ShiftLeft"==i.code)""!=this.previousTargetId&&(this.previousTargetId.indexOf("_chart_legend_")>-1&&-1==s.indexOf("chart_legend_g_")&&t.setTabIndex(o.children[this.currentLegendIndex],o.firstElementChild),this.previousTargetId.indexOf("_chart_legend_")>-1&&-1==s.indexOf("_chart_legend_")&&(this.currentLegendIndex=0)),this.previousTargetId=s;else if(i.code.indexOf("Arrow")>-1&&(i.preventDefault(),s.indexOf("_chart_legend_")>-1)){i.target.removeAttribute("tabindex"),this.currentLegendIndex+="ArrowUp"==i.code||"ArrowRight"==i.code?1:-1,this.currentLegendIndex=t.getActualIndex(this.currentLegendIndex,o.children.length);var d=o.children[this.currentLegendIndex];t.focusTarget(d),this.previousTargetId=s=d.lastElementChild.id}return!1},e.prototype.mouseDown=function(e){e.preventDefault()},e.prototype.keyActionHandler=function(e){"enter"===e.action&&this.clickProcess(e)},e.prototype.resize=function(){var e=this;this.dotNetRef&&(this.resizeTo&&clearTimeout(this.resizeTo),this.resizeTo=window.setTimeout((function(){e.dotNetRef.invokeMethodAsync("TriggerResize")}),500))},e.prototype.click=function(e){this.clickProcess(e)},e.prototype.clickProcess=function(e){this.dotNetRef&&this.dotNetRef.invokeMethodAsync("TriggerClick",e.target.id)},e.prototype.mousemove=function(e){var t,n,i;this.element&&(t=this.element.getBoundingClientRect());var s=document.getElementById(this.element.id+"_svg");s&&(n=s.getBoundingClientRect()),e.type.indexOf("touch")>-1&&(i=e);var o=(sf.base.isNullOrUndefined(i)?e.clientX:i.changedTouches[0].clientX)-t.left-Math.max(n.left-t.left,0),r=(sf.base.isNullOrUndefined(i)?e.clientY:i.changedTouches[0].clientY)-t.top-Math.max(n.top-t.top,0);this.dotNetRef&&((sf.base.Browser.isDevice||e.type.indexOf("touch")>-1)&&setTimeout((function(e){e.invokeMethodAsync("OnMouseLeave")}),1e3,this.dotNetRef),this.dotNetRef.invokeMethodAsync("TriggerMouseMove",e.target.id,o,r))},e.prototype.barAnimation=function(e,t,n,i,s){new sf.base.Animation({}).animate(t,{duration:e.duration<=0?1e3:e.duration,delay:e.delay,progress:function(o){if(o.timeStamp>=o.delay){t.style.visibility="visible";var r=-1*Math.cos((o.timeStamp-o.delay)/o.duration*(Math.PI/2))+1;"targetBarAnimation"===n?t.setAttribute("transform","translate("+i.toString()+" "+s.toString()+") scale(1, "+(r/1).toString()+") translate("+-i.toString()+" "+-s.toString()+")"):t.setAttribute("transform","translate("+i.toString()+" "+e.valueY.toString()+") scale("+(r/1).toString()+", 1) translate("+-i.toString()+" "+-e.valueY.toString()+")")}},end:function(){t.setAttribute("transform","translate(0,0)"),t.style.visibility="visible"}})},e}(),t={initialize:function(t,n,i,s,o){if(n)return new e(t,n.id,n,o).wireEvents(),this.getElementSize(n,i,s)},getElementSize:function(e,t,n){var i,s;return e&&(e.style.height=t,e.style.width=n,i=e.clientWidth||e.offsetWidth,s=e.clientHeight),{width:i,height:s}},updateElementOpacity:function(e,t,n){var i=document.getElementById(e);if(i){if(!sf.base.isNullOrUndefined(n)&&""!==n)document.getElementById(n).setAttribute("opacity","1");!0===t?i.setAttribute("opacity","1"):i.setAttribute("opacity","0.6")}},focusTarget:function(e){var t=e.getAttribute("class");return e.setAttribute("tabindex","0"),t&&-1===t.indexOf("e-chart-focused")?t+=" e-chart-focused":t||(t="e-chart-focused"),e.setAttribute("tabindex","0"),e.setAttribute("class",t),e.focus(),e.id},getActualIndex:function(e,t){return e>t-1?0:e<0?t-1:e},setTabIndex:function(e,t){e.removeAttribute("tabindex"),t.setAttribute("tabindex","0")},doBarAnimation:function(e,t,n,i,s,o){for(var r=0;r<n.length;r++){var a=document.getElementById(n[r]),d=window.sfBlazor.getCompInstance(e);a&&("valueBarAnimation"===i?d.barAnimation(t,a,i,s[r]):d.barAnimation(t,a,i,s[r],o[r]))}},destroy:function(e){var t=window.sfBlazor.getCompInstance(e);e&&t&&t.unWireEvents()}};return t}()}}]);(async()=>{await import(`${document.baseURI}_content/Syncfusion.Blazor/scripts/syncfusion-blazor-base.min.js?v=26.1.undefined`).then(()=>{sfBlazor.loadDependencies('sfbulletchart');})})();