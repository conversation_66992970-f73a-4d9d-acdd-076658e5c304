using Intra2025.Data;
using Microsoft.EntityFrameworkCore;

namespace Intra2025.Services
{
    /// <summary>
    /// 一次性服務，用於將舊的檔案路徑遷移到新的安全路徑。
    /// </summary>
    public class FileMigrationService
    {
        private readonly ReportDbContext _reportContext;
        private readonly YCRSDbContext _ycrsContext;
        private readonly IWebHostEnvironment _environment;
        private readonly ILogger<FileMigrationService> _logger;

        public FileMigrationService(
            ReportDbContext reportContext,
            YCRSDbContext ycrsContext,
            IWebHostEnvironment environment,
            ILogger<FileMigrationService> logger)
        {
            _reportContext = reportContext;
            _ycrsContext = ycrsContext;
            _environment = environment;
            _logger = logger;
        }

        /// <summary>
        /// 檢查檔案遷移狀態
        /// </summary>
        public async Task<MigrationStatusResult> CheckMigrationStatusAsync()
        {
            try
            {
                // 檢查報告檔案
                var reportFiles = await _reportContext.ELS_REPORT_FILES.ToListAsync();
                var totalReportFiles = reportFiles.Count;
                var migratedReportFiles = reportFiles.Count(f => f.Filepath?.StartsWith("/secure-uploads/") == true);

                // 檢查 YCRS 檔案
                var ycrsFiles = await _ycrsContext.YCRS_Files.ToListAsync();
                var totalYCRSFiles = ycrsFiles.Count;
                var migratedYCRSFiles = ycrsFiles.Count(f => f.Filepath?.StartsWith("/secure-uploads/") == true);

                // 檢查舊目錄
                var uploadsPath = Path.Combine(_environment.WebRootPath, "uploads");
                var remainingFilesInOldDirectory = 0;
                if (Directory.Exists(uploadsPath))
                {
                    remainingFilesInOldDirectory = Directory.GetFiles(uploadsPath, "*", SearchOption.AllDirectories).Length;
                }

                return new MigrationStatusResult
                {
                    TotalReportFiles = totalReportFiles,
                    MigratedReportFiles = migratedReportFiles,
                    TotalYCRSFiles = totalYCRSFiles,
                    MigratedYCRSFiles = migratedYCRSFiles,
                    RemainingFilesInOldDirectory = remainingFilesInOldDirectory,
                    IsFullyMigrated = migratedReportFiles == totalReportFiles &&
                                    migratedYCRSFiles == totalYCRSFiles &&
                                    remainingFilesInOldDirectory == 0
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "檢查遷移狀態時發生錯誤");
                throw;
            }
        }

        /// <summary>
        /// 執行所有檔案的遷移
        /// </summary>
        public async Task<MigrationResult> MigrateAllFilesAsync()
        {
            var result = new MigrationResult();

            try
            {
                _logger.LogInformation("開始執行檔案遷移...");

                // 遷移報告檔案
                var reportResult = await MigrateReportFilesAsync();
                result.ReportFilesMigrated = reportResult;

                // 遷移 YCRS 檔案
                var ycrsResult = await MigrateYCRSFilesAsync();
                result.YCRSFilesMigrated = ycrsResult;

                result.TotalFilesMigrated = result.ReportFilesMigrated + result.YCRSFilesMigrated;
                result.IsSuccess = true;

                _logger.LogInformation($"檔案遷移完成，總共遷移了 {result.TotalFilesMigrated} 個檔案");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "檔案遷移過程中發生錯誤");
                result.IsSuccess = false;
                result.ErrorMessage = ex.Message;
            }

            return result;
        }

        /// <summary>
        /// 遷移報告檔案
        /// </summary>
        private async Task<int> MigrateReportFilesAsync()
        {
            _logger.LogInformation("開始進行報告檔案路徑遷移...");

            var oldFilePathPattern = "uploads/Reports/";
            var newFilePathPrefix = "/secure-uploads/Reports/";

            var filesToMigrate = await _reportContext.ELS_REPORT_FILES
                .Where(f => f.Filepath != null && f.Filepath.Contains(oldFilePathPattern))
                .ToListAsync();

            if (!filesToMigrate.Any())
            {
                _logger.LogInformation("沒有找到需要遷移的報告檔案。");
                return 0;
            }

            _logger.LogInformation($"找到 {filesToMigrate.Count} 個報告檔案需要遷移。");

            foreach (var file in filesToMigrate)
            {
                var oldPath = file.Filepath;
                var fileName = Path.GetFileName(oldPath);
                var newPath = $"{newFilePathPrefix}{fileName}";

                file.Filepath = newPath;
                _logger.LogInformation($"準備將 ID: {file.Sno} 的路徑從 '{oldPath}' 更新為 '{newPath}'");
            }

            await _reportContext.SaveChangesAsync();
            _logger.LogInformation("報告檔案路徑遷移成功！");

            return filesToMigrate.Count;
        }

        /// <summary>
        /// 遷移 YCRS 檔案
        /// </summary>
        private async Task<int> MigrateYCRSFilesAsync()
        {
            _logger.LogInformation("開始進行 YCRS 檔案路徑遷移...");

            var oldFilePathPattern = "uploads/YCRS/";
            var newFilePathPrefix = "/secure-uploads/YCRS/";

            var filesToMigrate = await _ycrsContext.YCRS_Files
                .Where(f => f.Filepath != null && f.Filepath.Contains(oldFilePathPattern))
                .ToListAsync();

            if (!filesToMigrate.Any())
            {
                _logger.LogInformation("沒有找到需要遷移的 YCRS 檔案。");
                return 0;
            }

            _logger.LogInformation($"找到 {filesToMigrate.Count} 個 YCRS 檔案需要遷移。");

            foreach (var file in filesToMigrate)
            {
                var oldPath = file.Filepath;
                var fileName = Path.GetFileName(oldPath);
                var newPath = $"{newFilePathPrefix}{fileName}";

                file.Filepath = newPath;
                _logger.LogInformation($"準備將 ID: {file.Sno} 的路徑從 '{oldPath}' 更新為 '{newPath}'");
            }

            await _ycrsContext.SaveChangesAsync();
            _logger.LogInformation("YCRS 檔案路徑遷移成功！");

            return filesToMigrate.Count;
        }
    }

    /// <summary>
    /// 遷移狀態結果
    /// </summary>
    public class MigrationStatusResult
    {
        public int TotalReportFiles { get; set; }
        public int MigratedReportFiles { get; set; }
        public int TotalYCRSFiles { get; set; }
        public int MigratedYCRSFiles { get; set; }
        public int RemainingFilesInOldDirectory { get; set; }
        public bool IsFullyMigrated { get; set; }
    }

    /// <summary>
    /// 遷移結果
    /// </summary>
    public class MigrationResult
    {
        public bool IsSuccess { get; set; }
        public string ErrorMessage { get; set; } = string.Empty;
        public int TotalFilesMigrated { get; set; }
        public int ReportFilesMigrated { get; set; }
        public int YCRSFilesMigrated { get; set; }
        public List<string> ReportFilesErrors { get; set; } = new List<string>();
        public List<string> YCRSFilesErrors { get; set; } = new List<string>();
    }
}