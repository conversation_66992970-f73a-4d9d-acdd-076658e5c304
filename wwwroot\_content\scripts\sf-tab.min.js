/*!*  filename: sf-tab.min.js 
*     version : 26.1.41 
*     Copyright Syncfusion Inc. 2001 - 2024. All rights reserved. 
*     Use of this code is subject to the terms of our license.
*     A copy of the current license can be obtained at any time by e-mailing 
*     <EMAIL>. Any infringement will be prosecuted under 
*     applicable laws. 
*/
(window.webpackJsonp=window.webpackJsonp||[]).push([[63],{"./bundles/sf-tab.js":function(e,t,s){"use strict";s.r(t);s("./modules/sf-tab.js")},"./modules/sf-tab.js":function(e,t){window.sfBlazor=window.sfBlazor||{},window.sfBlazor.Tab=function(){"use strict";var e="e-tab-header",t="e-tab-wrap",s="e-toolbar-items",i="e-toolbar-item",n="e-toolbar-popup",o=function(){function o(e,t,s,i){this.show={},this.hide={},this.draggableItems=[{}],this.resizeContext=this.refreshActElePosition.bind(this),window.sfBlazor=window.sfBlazor,this.keyConfigs={tab:"tab",home:"home",end:"end",enter:"enter",space:"space",delete:"delete",moveLeft:"leftarrow",moveRight:"rightarrow",moveUp:"uparrow",moveDown:"downarrow"},this.element=t,this.dotNetRef=i,this.options=s,this.dataId=e,window.sfBlazor.setCompInstance(this)}return o.prototype.render=function(){var e=sf.base.closest(this.element,".e-content");this.prevIndex=0,this.isNested=!1,this.isPopup=!1,this.initRender=!0,this.isSwiped=!1,sf.base.isNullOrUndefined(e)||(e.parentElement.classList.add("e-nested"),this.isNested=!0);var t=sf.base.Browser.info.name,s="msie"===t?"e-ie":"edge"===t?"e-edge":"safari"===t?"e-safari":"";sf.base.setStyleAttribute(this.element,{width:sf.base.formatUnit(this.options.width),height:sf.base.formatUnit(this.options.height)}),sf.base.attributes(this.element,{"aria-disabled":"false"}),this.setCssClass(this.element,s,!0),this.updatePopAnimationConfig(),this.tabId=this.element.id.length>0?"-"+this.element.id:sf.base.getRandomId(),this.wireEvents(),this.initRender=!1},o.prototype.serverItemsChanged=function(e){if(this.enableAnimation=!1,this.setActive(this.options.selectedItem,e),"Dynamic"!==this.options.loadOn&&!sf.base.isNullOrUndefined(this.cntEle)){var t=[].slice.call(this.cntEle.children),s="e-content"+this.tabId+"_"+this.options.selectedItem;t.forEach((function(e){e.classList.contains("e-active")&&e.id!==s&&e.classList.remove("e-active"),e.id===s&&e.classList.add("e-active")})),this.prevIndex=this.options.selectedItem,this.triggerAnimation("e-item"+this.tabId+"_"+this.options.selectedItem,!1)}"Disable"!==sf.base.animationMode&&(this.enableAnimation=!0)},o.prototype.headerReady=function(){if(this.initRender=!0,this.hdrEle=this.getTabHeader(),this.setOrientation(this.options.headerPlacement,this.hdrEle),this.tbItems=sf.base.select("."+e+" ."+s,this.element),sf.base.isNullOrUndefined(this.tbItems)||sf.base.rippleEffect(this.tbItems,{selector:".e-tab-wrap"}),sf.base.selectAll("."+i,this.element).length>0){this.bdrLine=sf.base.select(".e-indicator.e-ignore",this.element);var t=sf.base.select("."+this.scrCntClass,this.tbItems);sf.base.isNullOrUndefined(t)?this.tbItems.insertBefore(this.bdrLine,this.tbItems.firstElementChild):t.insertBefore(this.bdrLine,t.firstElementChild),this.select(this.options.selectedItem)}if(this.cntEle=sf.base.select(".e-tab > .e-content",this.element),sf.base.isNullOrUndefined(this.cntEle)||(sf.base.isNullOrUndefined(this.touchModule)&&(this.touchModule=new sf.base.Touch(this.cntEle,{swipe:this.swipeHandler.bind(this)})),"auto"===this.options.height||this.isVertical()||(this.cntEle.style.height="calc(100% - "+this.hdrEle.offsetHeight+"px)")),"Demand"===this.options.loadOn){var n=this.setActiveContent();this.triggerAnimation(n,!1)}this.initRender=!1},o.prototype.setActiveContent=function(){var e="e-item"+this.tabId+"_"+this.options.selectedItem,t=this.getTrgContent(this.cntEle,this.extIndex(e));return sf.base.isNullOrUndefined(t)||t.classList.add("e-active"),e},o.prototype.removeActiveClass=function(){var e=this.getTabHeader();if(e){var t=sf.base.selectAll("."+i+".e-active",e);sf.base.removeClass(t,"e-active"),[].slice.call(t).forEach((function(e){return e.firstElementChild.setAttribute("aria-selected","false")}))}},o.prototype.checkPopupOverflow=function(e){this.tbPop=sf.base.select(".e-toolbar-pop",this.element);var t=sf.base.select(".e-hor-nav",this.element),i=sf.base.select("."+s,this.element),o=i.lastChild;return(!this.isVertical()&&(this.options.enableRtl&&t.offsetLeft+t.offsetWidth>i.offsetLeft||!this.options.enableRtl&&t.offsetLeft<i.offsetWidth)||this.isVertical()&&t.offsetTop<o.offsetTop+o.offsetHeight)&&(e.classList.add(n),this.tbPop.insertBefore(e,sf.base.selectAll("."+n,this.tbPop)[0])),!0},o.prototype.popupHandler=function(e){var o=e.querySelector(".e-ripple-element");sf.base.isNullOrUndefined(o)||(o.outerHTML="",e.querySelector("."+t).classList.remove("e-ripple")),this.tbItem=sf.base.selectAll("."+s+" ."+i,this.hdrEle);var a=this.tbItem[this.tbItem.length-1];if(0!==this.tbItem.length){if(e.classList.remove(n),e.removeAttribute("style"),this.tbItems.appendChild(e),this.checkPopupOverflow(a)){var l=this.tbItems.lastChild.previousElementSibling;this.checkPopupOverflow(l)}this.isPopup=!0}return sf.base.selectAll("."+i,this.tbItems).length-1},o.prototype.previousContentAnimation=function(e,t){return this.isPopup||e<=t?"SlideLeftIn"===this.options.animation.previous.effect?{name:"SlideLeftOut",duration:this.options.animation.previous.duration,timingFunction:this.options.animation.previous.easing}:null:"SlideRightIn"===this.options.animation.next.effect?{name:"SlideRightOut",duration:this.options.animation.next.duration,timingFunction:this.options.animation.next.easing}:null},o.prototype.triggerPreviousAnimation=function(e,t){var s=this.previousContentAnimation(t,this.options.selectedItem);sf.base.isNullOrUndefined(s)?e.classList.remove("e-active"):(s.begin=function(){sf.base.setStyleAttribute(e,{position:"absolute"}),sf.base.addClass([e],["e-progress","e-view"])},s.end=function(){e.style.display="none",sf.base.removeClass([e],["e-active","e-progress","e-view"]),sf.base.setStyleAttribute(e,{display:"",position:""}),0===e.childNodes.length&&sf.base.detach(e)},new sf.base.Animation(s).animate(e))},o.prototype.triggerAnimation=function(e,t){var s,i,n=this,o=this.prevIndex;if("Dynamic"!==this.options.loadOn){[].slice.call(this.element.querySelector(".e-content").children).forEach((function(e){e.id===n.prevActiveEle&&(s=e)}));var a=this.tbItem[o];if(i=this.getTrgContent(this.cntEle,this.extIndex(e)),sf.base.isNullOrUndefined(s)&&!sf.base.isNullOrUndefined(a)){var l=this.extIndex(a.id);s=this.getTrgContent(this.cntEle,l)}}else i=this.cntEle.firstElementChild;if(sf.base.isNullOrUndefined(i)||(this.prevActiveEle=i.id),this.initRender||!1===t||this.options.animation==={}||sf.base.isNullOrUndefined(this.options.animation))s&&s!==i&&s.classList.remove("e-active");else{var r,d=sf.base.select(".e-content",this.element);if(this.prevIndex>this.options.selectedItem&&!this.isPopup){var h="None"===this.options.animation.previous.effect&&"Enable"===sf.base.animationMode?"SlideLeftIn":this.options.animation.previous.effect;r={name:"None"===h?"":"SlideLeftIn"!==h?h:"SlideLeftIn",duration:this.options.animation.previous.duration,timingFunction:this.options.animation.previous.easing}}else if(this.isPopup||this.prevIndex<this.options.selectedItem||this.prevIndex===this.options.selectedItem){var f="None"===this.options.animation.next.effect&&"Enable"===sf.base.animationMode?"SlideRightIn":this.options.animation.next.effect;r={name:"None"===f?"":"SlideRightIn"!==f?f:"SlideRightIn",duration:this.options.animation.next.duration,timingFunction:this.options.animation.next.easing}}r.progress=function(){d.classList.add("e-progress"),n.setActiveBorder()},r.end=function(){d.classList.remove("e-progress"),i.classList.add("e-active")},this.initRender||sf.base.isNullOrUndefined(s)||this.triggerPreviousAnimation(s,o),this.isPopup=!1,""===r.name?i.classList.add("e-active"):new sf.base.Animation(r).animate(i)}},o.prototype.keyPressed=function(t){var s=sf.base.closest(t,"."+e+" ."+i),n=this.getEleIndex(s);!sf.base.isNullOrUndefined(this.popEle)&&t.classList.contains("e-hor-nav")?this.popEle.classList.contains("e-popup-open")?this.popObj.hide(this.hide):this.popObj.show(this.show):t.classList.contains("e-scroll-nav")?t.click():sf.base.isNullOrUndefined(s)||!1!==s.classList.contains("e-active")||(this.select(n,!0),sf.base.isNullOrUndefined(this.popEle)||this.popObj.hide(this.hide))},o.prototype.getTabHeader=function(){var t=[].slice.call(this.element.children).filter((function(t){return t.classList.contains(e)}));if(t.length>0)return t[0];var s=[].slice.call(this.element.children).filter((function(e){return!e.classList.contains("blazor-template")}))[0];return s?[].slice.call(s.children).filter((function(t){return t.classList.contains(e)}))[0]:void 0},o.prototype.getEleIndex=function(e){return Array.prototype.indexOf.call(sf.base.selectAll("."+i,this.getTabHeader()),e)},o.prototype.extIndex=function(e){return e.replace("e-item"+this.tabId+"_","")},o.prototype.getTrgContent=function(e,t){return this.element.classList.contains("e-nested")?sf.base.select(".e-nested> .e-content > #e-content"+this.tabId+"_"+t,this.element):this.findEle(e.children,"e-content"+this.tabId+"_"+t)},o.prototype.findEle=function(e,t){for(var s,i=0;i<e.length;i++)if(e[i].id===t){s=e[i];break}return s},o.prototype.isVertical=function(){var e="Left"===this.options.headerPlacement||"Right"===this.options.headerPlacement;return this.scrCntClass=e?"e-vscroll-content":"e-hscroll-content",e},o.prototype.updatePopAnimationConfig=function(){this.show={name:this.isVertical()?"FadeIn":"SlideDown",duration:100},this.hide={name:this.isVertical()?"FadeOut":"SlideUp",duration:100}},o.prototype.focusItem=function(e,t){void 0===t&&(t=!1);var s=sf.base.select(" #e-item"+this.tabId+"_"+this.options.selectedItem,this.hdrEle);sf.base.isNullOrUndefined(s)||t||s.firstElementChild.focus({preventScroll:e})},o.prototype.serverChangeOrientation=function(e,t,s,i){if(this.setOrientation(e,this.hdrEle),sf.base.removeClass([this.element],["e-vertical-tab","e-vertical-left","e-vertical-right","e-horizontal-bottom"]),i&&this.changeToolbarOrientation(t,s),"Bottom"===this.options.headerPlacement&&sf.base.addClass([this.element],"e-horizontal-bottom"),this.isVertical()){var n="Left"===this.options.headerPlacement?"e-vertical-left":"e-vertical-right";this.element.classList.contains("e-nested")?sf.base.addClass([this.hdrEle],["e-vertical-tab",n]):sf.base.addClass([this.element],["e-vertical-tab",n])}this.setActiveBorder(),this.focusItem(!0,!0)},o.prototype.changeToolbarOrientation=function(e,t){var s=window.sfBlazor.getCompInstance(e);sf.base.isNullOrUndefined(s)||sf.base.isNullOrUndefined(s.element)||(s.options.width=t?"auto":"100%",s.options.height=t?"100%":"auto",s.options.isVertical=t,s.changeOrientation()),this.updatePopAnimationConfig()},o.prototype.setOrientation=function(e,t){var s=Array.prototype.indexOf.call(this.element.children,t),i=Array.prototype.indexOf.call(this.element.children,this.element.querySelector(".e-content"));"Bottom"===e&&i>s?this.element.appendChild(t):"Bottom"!==e&&(sf.base.removeClass([t],["e-horizontal-bottom"]),this.element.insertBefore(t,sf.base.select(".e-content",this.element)))},o.prototype.setCssClass=function(e,t,s){""!==t&&(s?sf.base.addClass([e],t.split(" ")):sf.base.removeClass([e],t.split(" ")))},o.prototype.setActiveBorder=function(){var e=this.getTabHeader(),t=sf.base.select("."+i+".e-active",e);if(!sf.base.isNullOrUndefined(t)){this.options.reorderActiveTab?e.classList.remove("e-reorder-active-item"):(t.classList.contains(n)&&!this.bdrLine.classList.contains("e-hidden")&&this.bdrLine.classList.add("e-hidden"),e.classList.contains("e-reorder-active-item")||e.classList.add("e-reorder-active-item")),t.classList.contains(n)&&this.options.reorderActiveTab&&this.popupHandler(t);var o=sf.base.closest(t,".e-tab");if(this.element===o){this.tbItems=sf.base.select("."+s,e);var a=sf.base.select(".e-indicator",e),l=sf.base.select("."+s+" ."+this.scrCntClass,e);if(this.isVertical()){sf.base.setStyleAttribute(a,{left:"",right:""}),0!==(sf.base.isNullOrUndefined(l)?this.tbItems.offsetHeight:l.offsetHeight)?sf.base.setStyleAttribute(a,{top:t.offsetTop+"px",height:t.offsetHeight+"px"}):sf.base.setStyleAttribute(a,{top:0,height:0})}else{if("MultiRow"===this.options.overflowMode){var r="Bottom"===this.options.headerPlacement?t.offsetTop:t.offsetHeight+t.offsetTop;sf.base.setStyleAttribute(a,{top:r+"px",height:""})}else sf.base.setStyleAttribute(a,{top:"",height:""});var d=sf.base.isNullOrUndefined(l)?this.tbItems.offsetWidth:l.offsetWidth;0!==d?sf.base.setStyleAttribute(a,{left:t.offsetLeft+"px",right:d-(t.offsetLeft+t.offsetWidth)+"px"}):sf.base.setStyleAttribute(a,{left:"auto",right:"auto"})}sf.base.isNullOrUndefined(this.bdrLine)||t.classList.contains(n)||this.bdrLine.classList.remove("e-hidden")}}},o.prototype.setActive=function(e,t){void 0===t&&(t=!1),this.tbItem=sf.base.selectAll("."+i,this.getTabHeader()),sf.base.isNullOrUndefined(this.hdrEle)&&(this.hdrEle=this.getTabHeader());var s=this.hdrEle.querySelector("."+i+'[data-index="'+e+'"]');if(!(!s||e<0||isNaN(e)||0===this.tbItem.length))if(this.options.selectedItem=e,s.classList.contains("e-active"))this.setActiveBorder();else{var n=this.tbItem[this.prevIndex];sf.base.isNullOrUndefined(n)||n.firstElementChild.removeAttribute("aria-controls"),sf.base.attributes(s.firstElementChild,{"aria-controls":"e-content"+this.tabId+"_"+e});var o=s.id;this.removeActiveClass(),s.classList.add("e-active"),s.firstElementChild.setAttribute("aria-selected","true");var a=Number(this.extIndex(o));if(sf.base.isNullOrUndefined(this.prevActiveEle)&&(this.prevActiveEle="e-content"+this.tabId+"_"+a),"Init"===this.options.loadOn){this.cntEle=sf.base.select(".e-tab > .e-content",this.element);var l=this.getTrgContent(this.cntEle,this.extIndex(o));sf.base.isNullOrUndefined(l)||l.classList.add("e-active"),this.triggerAnimation(o,this.enableAnimation)}this.setActiveBorder(),this.refreshItemVisibility(s),this.initRender||t||s.firstElementChild.focus()}},o.prototype.contentReady=function(){var e=this.setActiveContent();this.triggerAnimation(e,this.enableAnimation)},o.prototype.setRTL=function(e){this.setCssClass(this.element,"e-rtl",e),this.refreshActiveBorder()},o.prototype.refreshActiveBorder=function(){sf.base.isNullOrUndefined(this.bdrLine)||this.bdrLine.classList.add("e-hidden"),this.setActiveBorder()},o.prototype.setDragAndDrop=function(e){e?this.bindDraggable():this.draggableItems&&this.draggableItems.forEach((function(e){e&&0!==Object.keys(e).length&&e.destroy()}))},o.prototype.showPopup=function(e){var t=sf.base.select(".e-popup.e-toolbar-pop",this.hdrEle);if(t&&t.classList.contains("e-popup-close")){var s=t&&t.ej2_instances[0];s.position.X="Left"===this.options.headerPlacement?"left":"right",s.dataBind(),s.show(e)}},o.prototype.initializeDrag=function(s){var n=this;this.options.dragArea=sf.base.isNullOrUndefined(this.options.dragArea)?"#"+this.element.id+" ."+e:this.options.dragArea;var o=new sf.base.Draggable(s,{dragArea:this.options.dragArea,dragTarget:"."+i,clone:!0,distance:5,helper:this.helper.bind(this),dragStart:this.itemDragStart.bind(this),drag:function(s){var o,a=n.getEleIndex(n.dragItem);if(sf.base.isNullOrUndefined(s.target.closest(".e-tab"))||s.target.closest(".e-tab").isEqualNode(n.element)||n.options.dragArea==="."+e){if(s.target.closest(n.options.dragArea)||"Popup"===n.options.overflowMode?(document.body.style.cursor="",n.dragItem.querySelector("."+t).style.visibility="hidden",n.cloneElement.classList.contains("e-hidden")&&sf.base.removeClass([n.cloneElement],"e-hidden")):(document.body.style.cursor="not-allowed",sf.base.addClass([n.cloneElement],"e-hidden"),n.dragItem.classList.contains("e-hidden")&&sf.base.removeClass([n.dragItem],"e-hidden"),n.dragItem.querySelector("."+t).style.visibility="visible"),"Scrollable"===n.options.overflowMode&&!sf.base.isNullOrUndefined(n.element.querySelector(".e-hscroll"))){var l=n.element.querySelector(".e-scroll-right-nav"),r=n.element.querySelector(".e-scroll-left-nav"),d=n.element.querySelector(".e-hscroll-bar");!sf.base.isNullOrUndefined(l)&&Math.abs(l.offsetWidth/2+l.offsetLeft)>n.cloneElement.offsetLeft+n.cloneElement.offsetWidth&&(d.scrollLeft-=10),!sf.base.isNullOrUndefined(r)&&Math.abs(r.offsetLeft+r.offsetWidth-n.cloneElement.offsetLeft)>r.offsetWidth/2&&(d.scrollLeft+=10)}n.cloneElement.style.pointerEvents="none";var h=n.cloneElement.getBoundingClientRect().left,f=n.cloneElement.getBoundingClientRect().top,c=document.elementFromPoint(h,f),p=sf.base.closest(c,"."+e+" ."+i),b=0;"Scrollable"!==n.options.overflowMode||sf.base.isNullOrUndefined(n.element.querySelector(".e-hscroll"))||(b=n.element.querySelector(".e-hscroll-content").offsetWidth),null!=p&&!p.isSameNode(n.dragItem)&&p.closest(".e-tab").isSameNode(n.dragItem.closest(".e-tab"))&&((o=n.getEleIndex(p))<a&&Math.abs(p.offsetLeft+p.offsetWidth-n.cloneElement.offsetLeft)>p.offsetWidth/2&&n.dragAction(p,a,o),o>a&&Math.abs(p.offsetWidth/2)+p.offsetLeft-b<n.cloneElement.offsetLeft+n.cloneElement.offsetWidth&&n.dragAction(p,a,o)),n.droppedIndex=n.getEleIndex(n.dragItem)}},dragStop:this.itemDragStop.bind(this)});this.draggableItems.push(o)},o.prototype.helper=function(e){return e.element&&(this.cloneElement=e.element.cloneNode(!0),sf.base.addClass([this.cloneElement],["e-tab-clone-element","e-tab"]),sf.base.removeClass([this.cloneElement.querySelector("."+t)],"e-ripple"),sf.base.isNullOrUndefined(this.cloneElement.querySelector(".e-ripple-element"))||sf.base.remove(this.cloneElement.querySelector(".e-ripple-element")),document.body.appendChild(this.cloneElement)),this.cloneElement},o.prototype.itemDragStart=function(e){var s=this;this.dragItem=e.element,this.dragStartIndex=this.getEleIndex(this.dragItem);this.element.querySelector(".e-toolbar");this.dotNetRef.invokeMethodAsync("OnDragStart",this.dragStartIndex).then((function(i){i?sf.base.detach(s.cloneElement):(s.removeActiveClass(),sf.base.addClass([s.tbItems.querySelector(".e-indicator")],"e-hidden"),s.dragItem.querySelector("."+t).style.visibility="hidden",e.bindEvents(sf.base.getElement(e.dragElement)))}))},o.prototype.dragAction=function(e,t,s){if("MultiRow"===this.options.overflowMode&&e.parentNode.insertBefore(this.dragItem,e.nextElementSibling),t>s)if(this.dragItem.parentElement.isSameNode(e.parentElement))this.dragItem.parentNode.insertBefore(this.dragItem,e);else if("Extended"===this.options.overflowMode)if(e.isSameNode(e.parentElement.lastChild)){var i=this.dragItem.parentNode;e.parentNode.insertBefore(this.dragItem,e),i.insertBefore(e.parentElement.lastChild,i.childNodes[0])}else this.dragItem.parentNode.insertBefore(e.parentElement.lastChild,this.dragItem.parentElement.childNodes[0]),e.parentNode.insertBefore(this.dragItem,e);else{var n=e.parentElement.lastChild;if(e.isSameNode(n)){i=this.dragItem.parentNode;e.parentNode.insertBefore(this.dragItem,e),i.insertBefore(n,i.childNodes[0])}else this.dragItem.parentNode.insertBefore(e.parentElement.lastChild,this.dragItem.parentElement.childNodes[0]),e.parentNode.insertBefore(this.dragItem,e)}t<s&&(this.dragItem.parentElement.isSameNode(e.parentElement)?this.dragItem.parentNode.insertBefore(e,this.dragItem):"Extended"===this.options.overflowMode?(this.dragItem.parentElement.appendChild(e.parentElement.firstElementChild),e.parentNode.insertBefore(this.dragItem,e.nextSibling)):(this.dragItem.parentNode.insertBefore(e.parentElement.lastChild,this.dragItem.parentElement.childNodes[0]),e.parentNode.insertBefore(this.dragItem,e)))},o.prototype.itemDragStop=function(e){sf.base.detach(this.cloneElement);var s=this.element.querySelector(".e-toolbar");this.dragItem.querySelector("."+t).style.visibility="visible",sf.base.addClass([s],"e-drag-action"),document.body.style.cursor="";var i=this.getXYValue(e.event,"X"),n=this.getXYValue(e.event,"Y");this.dotNetRef.invokeMethodAsync("Dragged",this.droppedIndex,this.dragStartIndex,i,n)},o.prototype.getXYValue=function(e,t){var s,i=e.changedTouches;return s="X"===t?i?i[0].clientX:e.clientX:i?i[0].clientY:e.clientY,Math.ceil(s)},o.prototype.bindDraggable=function(){var t=this;this.options.allowDragAndDrop&&this.element.querySelectorAll("."+e+" ."+i).forEach((function(s){sf.base.isNullOrUndefined(t.options.dragArea)&&(t.options.dragArea="#"+t.element.id+" ."+e),s.classList.contains("e-draggable")||t.initializeDrag(s)}))},o.prototype.wireEvents=function(){this.bindDraggable(),window.addEventListener("resize",this.resizeContext),sf.base.EventHandler.add(this.element,"keydown",this.spaceKeyDown,this),sf.base.isNullOrUndefined(this.cntEle)||(this.touchModule=new sf.base.Touch(this.cntEle,{swipe:this.swipeHandler.bind(this)})),this.keyModule=new sf.base.KeyboardEvents(this.element,{keyAction:this.keyHandler.bind(this),keyConfigs:this.keyConfigs}),this.tabKeyModule=new sf.base.KeyboardEvents(this.element,{keyAction:this.keyHandler.bind(this),keyConfigs:{openPopup:"shift+f10",tab:"tab",shiftTab:"shift+tab"},eventName:"keydown"})},o.prototype.unWireEvents=function(){this.keyModule.destroy(),this.tabKeyModule.destroy(),sf.base.isNullOrUndefined(this.cntEle)||this.touchModule.destroy(),window.removeEventListener("resize",this.resizeContext),sf.base.EventHandler.remove(this.element,"keydown",this.spaceKeyDown),sf.base.removeClass([this.element],["e-rtl","e-focused"])},o.prototype.swipeHandler=function(e){if(!(e.velocity<3&&sf.base.isNullOrUndefined(e.originalEvent.changedTouches))){this.isNested&&this.element.setAttribute("data-swipe","true");var t=this.element.querySelector('[data-swipe="true"]');if(t)t.removeAttribute("data-swipe");else{if(this.isSwiped=!0,"Right"===e.swipeDirection&&0!==this.options.selectedItem){for(var s=this.options.selectedItem-1;s>=0;s--)if(!this.tbItem[s].classList.contains("e-hidden")&&!this.tbItem[s].classList.contains("e-disable")){this.select(s,!0);break}}else if("Left"===e.swipeDirection&&this.options.selectedItem!==sf.base.selectAll("."+i,this.element).length-1)for(var n=this.options.selectedItem+1;n<this.tbItem.length;n++)if(!this.tbItem[n].classList.contains("e-hidden")&&!this.tbItem[n].classList.contains("e-disable")){this.select(n,!0);break}this.isSwiped=!1}}},o.prototype.spaceKeyDown=function(t){if(32===t.keyCode&&32===t.which||35===t.keyCode&&35===t.which){var s=sf.base.closest(t.target,"."+e);sf.base.isNullOrUndefined(s)||t.preventDefault()}},o.prototype.keyHandler=function(e){if(!this.element.classList.contains("e-disable")){this.element.classList.add("e-focused");var s,n,o=e.target,a=this.getTabHeader(),l=sf.base.select(".e-active",a);switch(this.popEle=sf.base.select(".e-toolbar-pop",a),sf.base.isNullOrUndefined(this.popEle)||(this.popObj=this.popEle.ej2_instances[0]),e.action){case"space":case"enter":if(o.parentElement.classList.contains("e-disable"))return;if("enter"===e.action&&o.classList.contains("e-hor-nav")){this.showPopup(this.show);break}this.keyPressed(o);break;case"tab":case"shiftTab":o.classList.contains(t)&&!1===sf.base.closest(o,"."+i).classList.contains("e-active")&&o.setAttribute("tabindex",o.getAttribute("data-tabindex")),this.popObj&&sf.base.isVisible(this.popObj.element)&&this.popObj.hide(this.hide),sf.base.isNullOrUndefined(l)||"-1"!==l.children.item(0).getAttribute("tabindex")||l.children.item(0).setAttribute("tabindex","0");break;case"moveLeft":case"moveRight":s=sf.base.closest(document.activeElement,"."+i),sf.base.isNullOrUndefined(s)||this.refreshItemVisibility(s);break;case"openPopup":e.preventDefault(),!sf.base.isNullOrUndefined(this.popEle)&&this.popEle.classList.contains("e-popup-close")&&this.popObj.show(this.show);break;case"delete":if(n=sf.base.closest(o,"."+i),!0===this.options.showCloseButton&&!sf.base.isNullOrUndefined(n)){if(-1===this.getEleIndex(n))return;var r=n.nextElementSibling;!sf.base.isNullOrUndefined(r)&&r.classList.contains(i)&&r.firstElementChild.focus(),this.dotNetRef.invokeMethodAsync("RemoveTab",parseInt(n.getAttribute("data-index"),10))}this.setActiveBorder()}}},o.prototype.refreshActElePosition=function(){var e=sf.base.select("."+i+"."+n+".e-active",this.element);!sf.base.isNullOrUndefined(e)&&this.options.reorderActiveTab&&this.select(this.getEleIndex(e)),this.refreshActiveBorder()},o.prototype.refreshItemVisibility=function(e){var t=sf.base.select("."+this.scrCntClass,this.tbItems);if(!this.isVertical()&&!sf.base.isNullOrUndefined(t)){var s=sf.base.select(".e-hscroll-bar",this.tbItems),i=s.scrollLeft,n=i+(this.options.enableRtl?-s.offsetWidth:s.offsetWidth),o=e.offsetWidth,a=this.options.enableRtl?-(t.scrollWidth-e.offsetLeft):e.offsetLeft+e.offsetWidth,l=this.options.enableRtl?a+o:e.offsetLeft;if(i<l&&n<a){var r=this.options.enableRtl?-(a-i):n-l;s.scrollLeft=i+(o-r)}else if(i>l&&n>a){r=this.options.enableRtl?-(n-l):a-i;s.scrollLeft=i-(o-r)}}},o.prototype.enableTab=function(e,t){var s=sf.base.selectAll("."+i,this.element)[e];sf.base.isNullOrUndefined(s)||(!0===t?(s.classList.remove("e-disable","e-overlay"),s.firstElementChild.setAttribute("tabindex",s.firstElementChild.getAttribute("data-tabindex"))):(s.classList.add("e-disable","e-overlay"),s.firstElementChild.removeAttribute("tabindex"),s.classList.contains("e-active")&&this.select(e+1)),sf.base.isNullOrUndefined(s.firstElementChild)||s.firstElementChild.setAttribute("aria-disabled",!0===t?"false":"true"))},o.prototype.hideTab=function(e,t){var s;void 0===t&&(t=!0);var n=sf.base.selectAll("."+i,this.element)[e];if(!sf.base.isNullOrUndefined(n)){if(this.bdrLine.classList.add("e-hidden"),t)if(n.classList.add("e-hidden"),0!==(s=sf.base.selectAll("."+i+":not(.e-hidden)",this.tbItems)).length&&n.classList.contains("e-active")){if(0!==e)for(var o=e-1;o>=0;o--){if(!this.tbItem[o].classList.contains("e-hidden")){this.select(o);break}if(0===o)for(var a=e+1;a<this.tbItem.length;a++)if(!this.tbItem[a].classList.contains("e-hidden")){this.select(a);break}}else for(a=e+1;a<this.tbItem.length;a++)if(!this.tbItem[a].classList.contains("e-hidden")){this.select(a);break}}else 0===s.length&&this.element.classList.add("e-hidden");else this.element.classList.remove("e-hidden"),s=sf.base.selectAll("."+i+":not(.e-hidden)",this.tbItems),n.classList.remove("e-hidden"),0===s.length&&this.select(e);this.setActiveBorder(),sf.base.isNullOrUndefined(n.firstElementChild)||n.firstElementChild.setAttribute("aria-hidden",""+t)}},o.prototype.select=function(e,t){void 0===t&&(t=!1);var n,o=this.getTabHeader();this.tbItems=sf.base.select("."+s,o),this.tbItem=sf.base.selectAll("."+i,o),this.prevItem=this.tbItem[this.prevIndex];var a=this.options.selectedItem;(sf.base.isNullOrUndefined(a)||a<0||this.tbItem.length<=a||isNaN(a))&&(this.options.selectedItem=0);var l=this.tbItem[e];if(sf.base.isNullOrUndefined(this.prevItem)||this.prevItem.classList.contains("e-disable")||this.prevItem.children.item(0).setAttribute("tabindex",this.prevItem.firstElementChild.getAttribute("tabindex")),this.initRender)this.selectingContent(e);else{l&&(n=parseInt(l.getAttribute("data-index"),10));var r={previousItem:null,previousIndex:this.prevIndex,selectedItem:null,selectedIndex:this.options.selectedItem,selectedContent:null,selectingItem:null,selectingIndex:n,selectingContent:null,isSwiped:this.isSwiped,isInteracted:t,cancel:!1};this.dotNetRef.invokeMethodAsync("SelectingEvent",r,n)}},o.prototype.setPersistence=function(e,t){this.options.enablePersistence&&window.localStorage.setItem(e,t)},o.prototype.selectingContent=function(e,t){if(void 0===t&&(t=!1),this.tbItem=sf.base.selectAll("."+i,this.hdrEle),this.tbItem.length>e&&e>=0&&!isNaN(e)){this.prevIndex=this.options.selectedItem;var s=this.hdrEle.querySelector("."+i+'[data-index="'+e+'"]');s&&s.classList.contains(n)&&this.options.reorderActiveTab&&this.popupHandler(s),this.setActive(e,t)}else this.setActive(0,t)},o.prototype.disable=function(e){this.setCssClass(this.element,"e-disable",e),this.element.setAttribute("aria-disabled",""+e)},o.prototype.headerItemsUpdate=function(e){var t=this.getTabHeader();this.tbItems=sf.base.select("."+s,t),this.tbItem=sf.base.selectAll("."+i,t),this.prevItem=this.tbItem[this.prevIndex],sf.base.isNullOrUndefined(this.prevItem)||this.prevItem.classList.contains("e-disable")||this.prevItem.children.item(0).setAttribute("tabindex",this.prevItem.firstElementChild.getAttribute("tabindex")),this.selectingContent(e)},o.prototype.destroy=function(){this.unWireEvents(),this.element.removeAttribute("aria-disabled")},o.prototype.getContentElement=function(e){return sf.base.select(".e-content #e-content"+this.tabId+"_"+e,this.element)},o}();return{initialize:function(e,t,s,i){if(t&&e){var n=new o(e,t,s,i);n.render(),n.headerReady()}},headerReady:function(e,t){var s=window.sfBlazor.getCompInstance(e);sf.base.isNullOrUndefined(s)||sf.base.isNullOrUndefined(s.element)||(s.headerReady(),t||s.isDestroyed||s.dotNetRef.invokeMethodAsync("CreatedEvent",null))},contentReady:function(t,n){var o=window.sfBlazor.getCompInstance(t);if(!sf.base.isNullOrUndefined(o)&&!sf.base.isNullOrUndefined(o.element)){var a={isPopupElement:!1};o.element.classList.remove("e-focused"),o.isPopup=!1;var l=o.element.querySelector("."+e+" ."+i+'[data-index="'+n+'"]');sf.base.isNullOrUndefined(l)||(a.isPopupElement=!sf.base.isNullOrUndefined(sf.base.closest(l,".e-toolbar-pop")));var r=sf.base.selectAll("."+e+" ."+s+" ."+i,o.element);return a.toolbarItemIndex=r.length-1,o.headerItemsUpdate(n),o.setPersistence("tab"+o.element.id,n.toString()),"Init"!==o.options.loadOn&&o.contentReady(),JSON.stringify(a)}return null},selectingContent:function(e,t){var s=window.sfBlazor.getCompInstance(e);sf.base.isNullOrUndefined(s)||sf.base.isNullOrUndefined(s.element)||(s.selectingContent(t,!0),s.setPersistence("tab"+s.element.id,t.toString()),"Init"!==s.options.loadOn&&s.contentReady())},serverItemsChanged:function(e,t,s,i,n){var o=window.sfBlazor.getCompInstance(e);sf.base.isNullOrUndefined(o)||sf.base.isNullOrUndefined(o.element)||(o.options.selectedItem=t,o.options.animation=s,sf.base.isNullOrUndefined(o.element.querySelector(".e-toolbar"))||(sf.base.removeClass([o.element.querySelector(".e-toolbar")],"e-drag-action"),sf.base.removeClass([o.element.querySelector(".e-indicator")],"e-hidden")),i?sf.base.addClass([o.element],"e-vertical-icon"):sf.base.removeClass([o.element],"e-vertical-icon"),o.serverItemsChanged(n),o.options.allowDragAndDrop&&o.bindDraggable())},enableTab:function(e,t,s){var i=window.sfBlazor.getCompInstance(e);sf.base.isNullOrUndefined(i)||sf.base.isNullOrUndefined(i.element)||i.enableTab(t,s)},hideTab:function(e,t,s){var i=window.sfBlazor.getCompInstance(e);sf.base.isNullOrUndefined(i)||sf.base.isNullOrUndefined(i.element)||i.hideTab(t,s)},select:function(e,t){var s=window.sfBlazor.getCompInstance(e);sf.base.isNullOrUndefined(s)||sf.base.isNullOrUndefined(s.element)||s.select(t)},disable:function(e,t){var s=window.sfBlazor.getCompInstance(e);sf.base.isNullOrUndefined(s)||sf.base.isNullOrUndefined(s.element)||s.disable(t)},setCssClass:function(e,t){var s=window.sfBlazor.getCompInstance(e);sf.base.isNullOrUndefined(s)||sf.base.isNullOrUndefined(s.element)||(""!==s.options.cssClass&&s.setCssClass(s.element,s.options.cssClass,!1),s.setCssClass(s.element,t,!0),s.options.cssClass=t)},showCloseButton:function(e,t){var s=window.sfBlazor.getCompInstance(e);sf.base.isNullOrUndefined(s)||sf.base.isNullOrUndefined(s.element)||(s.options.showCloseButton=t,s.refreshActElePosition())},headerPlacement:function(e,t,s,i,n,o,a){var l=window.sfBlazor.getCompInstance(e);if(!sf.base.isNullOrUndefined(l)&&!sf.base.isNullOrUndefined(l.element)){l.options.headerPlacement=t,l.options.selectedItem=s;var r=window.sfBlazor.getCompInstance(i);sf.base.isNullOrUndefined(r.element)||r.setCssClass(n),l.serverChangeOrientation(t,i,o,a)}},enableRtl:function(e,t){var s=window.sfBlazor.getCompInstance(e);sf.base.isNullOrUndefined(s)||sf.base.isNullOrUndefined(s.element)||(s.options.enableRtl=t,s.setRTL(t))},overflowMode:function(e,t){var s=window.sfBlazor.getCompInstance(e);sf.base.isNullOrUndefined(s)||sf.base.isNullOrUndefined(s.element)||(s.options.overflowMode=t,s.refreshActElePosition())},allowDragAndDrop:function(e,t){var s=window.sfBlazor.getCompInstance(e);sf.base.isNullOrUndefined(s)||sf.base.isNullOrUndefined(s.element)||(s.options.allowDragAndDrop=t,s.setDragAndDrop(t))},refresh:function(e){var t=window.sfBlazor.getCompInstance(e);sf.base.isNullOrUndefined(t)||sf.base.isNullOrUndefined(t.element)||t.refreshActiveBorder()},destroy:function(e,t,s){var i=window.sfBlazor.getCompInstance(e);sf.base.isNullOrUndefined(i)||sf.base.isNullOrUndefined(i.element)||(i.isDestroyed=!0,i.setPersistence(t,s),i.destroy())},getTabItem:function(e,t){if(!sf.base.isNullOrUndefined(e)){var s=e.querySelector("."+i+'[data-index="'+t+'"]');if(s)return JSON.stringify(window.sfBlazor.getDomObject("tabitem",s))}return null},getTabContent:function(e,t){var s=window.sfBlazor.getCompInstance(e);if(!sf.base.isNullOrUndefined(s)&&!sf.base.isNullOrUndefined(s.element)){var i=s.getContentElement(t);if(i)return JSON.stringify(window.sfBlazor.getDomObject("tabcontent",i))}return null},focusSelectedTab:function(e,t){var s=window.sfBlazor.getCompInstance(e);sf.base.isNullOrUndefined(s)||sf.base.isNullOrUndefined(s.element)||s.focusItem(!1,t)}}}()}}]);(async()=>{await import(`${document.baseURI}_content/Syncfusion.Blazor/scripts/syncfusion-blazor-base.min.js?v=26.1.undefined`).then(()=>{sfBlazor.loadDependencies('sftab');})})();