/*!*  filename: sf-signature.min.js 
*     version : 26.1.41 
*     Copyright Syncfusion Inc. 2001 - 2024. All rights reserved. 
*     Use of this code is subject to the terms of our license.
*     A copy of the current license can be obtained at any time by e-mailing 
*     <EMAIL>. Any infringement will be prosecuted under 
*     applicable laws. 
*/
(window.webpackJsonp=window.webpackJsonp||[]).push([[53],{"./bundles/sf-signature.js":function(t,e,n){"use strict";n.r(e);n("./modules/sf-signature.js")},"./modules/sf-signature.js":function(t,e){window.sfBlazor=window.sfBlazor||{},window.sfBlazor.Signature=function(){"use strict";var t,e,n=(t=function(e,n){return(t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n])})(e,n)},function(e,n){function i(){this.constructor=e}t(e,n),e.prototype=null===n?Object.create(n):(i.prototype=n.prototype,new i)}),i=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.minDistance=5,e.previous=0,e.interval=30,e.timeout=null,e.isSignatureEmpty=!0,e.backgroundLoaded=null,e.isBlazor=!1,e.isResponsive=!1,e.signPointsColl=[],e.signRatioPointsColl=[],e.canRedraw=!0,e}return n(e,t),e.prototype.initialize=function(t,e){this.clearArray=[],this.element=t,this.canvasContext=this.element.getContext("2d"),this.canvasContext.canvas.tabIndex=-1,e&&(this.dotnetRef=e,this.isBlazor=!0,this.signatureValue&&this.loadPersistedSignature()),this.setHTMLProperties(),sf.base.isNullOrUndefined(this.signatureValue)&&this.updateSnapCollection(!0),this.wireEvents(),this.isBlazor||this.trigger("created",null)},e.prototype.wireEvents=function(){!sf.base.isNullOrUndefined(this.pointColl)||this.isReadOnly||this.disabled?this.pointColl&&(sf.base.EventHandler.add(this.canvasContext.canvas,"mousemove touchmove",this.mouseMoveHandler,this),sf.base.EventHandler.add(this.canvasContext.canvas,"mouseup touchend",this.mouseUpHandler,this),sf.base.EventHandler.add(document,"mouseup",this.mouseUpHandler,this)):(sf.base.EventHandler.add(this.canvasContext.canvas,"mousedown touchstart",this.mouseDownHandler,this),sf.base.EventHandler.add(this.canvasContext.canvas,"keydown",this.keyboardHandler,this),window.addEventListener("resize",this.resizeHandler.bind(this)))},e.prototype.unwireEvents=function(t){"mouseup"===t||"touchend"===t?(sf.base.EventHandler.remove(this.canvasContext.canvas,"mousemove touchmove",this.mouseMoveHandler),sf.base.EventHandler.remove(this.canvasContext.canvas,"mouseup touchend",this.mouseUpHandler),sf.base.EventHandler.remove(document,"mouseup",this.mouseUpHandler)):(sf.base.EventHandler.remove(this.canvasContext.canvas,"mousedown touchstart",this.mouseDownHandler),sf.base.EventHandler.remove(this.canvasContext.canvas,"keydown",this.keyboardHandler),window.removeEventListener("resize",this.resizeHandler))},e.prototype.setHTMLProperties=function(){150===this.element.height&&300===this.element.width&&0!==this.element.offsetHeight&&0!==this.element.offsetWidth?(this.element.height=this.element.offsetHeight,this.element.width=this.element.offsetWidth,this.isResponsive=!0):this.element.height===this.element.offsetHeight-1&&this.element.width===this.element.offsetWidth-1||0===this.element.offsetHeight||0===this.element.offsetWidth||(this.element.height=this.element.offsetHeight,this.element.width=this.element.offsetWidth),this.canvasContext.fillStyle=this.strokeColor,this.tempCanvas=this.createElement("canvas",{className:"e-"+this.getModuleName()+"-temp"}),this.tempContext=this.tempCanvas.getContext("2d"),this.tempCanvas.width=this.element.width,this.tempCanvas.height=this.element.height,this.backgroundImage?(this.canvasContext.canvas.style.backgroundImage="url("+this.backgroundImage+")",this.canvasContext.canvas.style.backgroundRepeat="no-repeat",this.saveWithBackground&&this.setBackgroundImage(this.backgroundImage,"temp")):this.backgroundColor&&(this.canvasContext.canvas.style.backgroundColor=this.backgroundColor)},e.prototype.mouseDownHandler=function(t){1!==t.buttons&&2!==t.buttons&&"touchstart"!==t.type||("touchstart"===t.type&&(t.preventDefault(),t.stopPropagation()),this.beginStroke(t),this.wireEvents())},e.prototype.mouseMoveHandler=function(t){1!==t.buttons&&2!==t.buttons&&"touchmove"!==t.type||("touchmove"===t.type&&(t.preventDefault(),t.stopPropagation()),this.interval?this.updateStrokeWithThrottle(t):this.updateStroke(t))},e.prototype.mouseUpHandler=function(t){"touchstart"===t.type&&(t.preventDefault(),t.stopPropagation()),this.endDraw(),this.updateSnapCollection(),this.unwireEvents(t.type),this.isBlazor?this.dotnetRef.invokeMethodAsync("TriggerEventAsync","mouseUp"):this.trigger("change",{actionName:"strokeUpdate"}),this.signatureValue=this.snapColl[this.incStep];for(var e=0;e<this.signPointsColl.length;e++)this.signRatioPointsColl.push({x:this.signPointsColl[e].x/this.canvasContext.canvas.width,y:this.signPointsColl[e].y/this.canvasContext.canvas.height});this.signPointsColl=[]},e.prototype.keyboardHandler=function(t){var e=this,n={fileName:"Signature",type:"Png",cancel:!1};switch(t.key){case"Delete":this.clear();break;case t.ctrlKey&&"s":this.isBlazor?this.dotnetRef.invokeMethodAsync("TriggerEventAsync","beforeSave"):this.trigger("beforeSave",n,(function(t){n.cancel||e.save(t.type,t.fileName)})),t.preventDefault(),t.stopImmediatePropagation();break;case t.ctrlKey&&"z":this.undo();break;case t.ctrlKey&&"y":this.redo()}},e.prototype.resizeHandler=function(){var t=this;if(this.isResponsive&&this.canRedraw){this.canvasContext.canvas.width=this.element.offsetWidth,this.canvasContext.canvas.height=this.element.offsetHeight,this.canvasContext.scale(1,1);for(var e=(this.minStrokeWidth+this.maxStrokeWidth)/2,n=0;n<this.signRatioPointsColl.length;n++)this.arcDraw(this.signRatioPointsColl[n].x*this.canvasContext.canvas.width,this.signRatioPointsColl[n].y*this.canvasContext.canvas.height,e);this.signPointsColl=[],this.canvasContext.closePath(),this.canvasContext.fill()}else{var i=new Image;i.src=this.snapColl[this.incStep],i.onload=function(){t.canvasContext.clearRect(0,0,t.element.width,t.element.height),t.canvasContext.drawImage(i,0,0,t.element.width,t.element.height)}}},e.prototype.beginStroke=function(t){this.internalRefresh(),this.updateStroke(t)},e.prototype.updateStroke=function(t){var e=this.createPoint(t);this.addPoint(e)},e.prototype.updateStrokeWithThrottle=function(t){var e=Date.now(),n=this.interval-(e-this.previous);this.storedArgs=t,n<=0||n>this.interval?(this.timeout&&(clearTimeout(this.timeout),this.timeout=null),this.previous=e,this.updateStroke(this.storedArgs),this.timeout||(this.storedArgs=null)):this.timeout||(this.timeout=window.setTimeout(this.delay.bind(this),n))},e.prototype.delay=function(){this.previous=Date.now(),this.timeout=null,this.updateStroke(this.storedArgs),this.timeout||(this.storedArgs=null)},e.prototype.createPoint=function(t){var e=this.canvasContext.canvas.getBoundingClientRect();return"mousedown"===t.type||"mousemove"===t.type?this.point(t.clientX-e.left,t.clientY-e.top,(new Date).getTime()):this.point(t.touches[0].clientX-e.left,t.touches[0].clientY-e.top,(new Date).getTime())},e.prototype.point=function(t,e,n){return this.pointX=t,this.pointY=e,this.time=n||(new Date).getTime(),{x:this.pointX,y:this.pointY,time:this.time}},e.prototype.addPoint=function(t){var e,n,i=this.pointColl,s=i.length>0&&i[i.length-1],a=!!s&&this.distanceTo(s)<=this.minDistance;s&&s&&a||(i.push(t),i.length>2&&(3===i.length&&i.unshift(i[0]),e=this.calculateCurveControlPoints(i[0],i[1],i[2]).controlPoint2,n=this.calculateCurveControlPoints(i[1],i[2],i[3]).controlPoint1,this.startPoint=i[1],this.controlPoint1=e,this.controlPoint2=n,this.endPoint=i[2],this.startDraw(),i.shift()))},e.prototype.startDraw=function(){var t;t=this.pointVelocityCalc(this.startPoint),t=this.velocity*t+(1-this.velocity)*this.lastVelocity;var e=Math.max(this.maxStrokeWidth/(t+1),this.minStrokeWidth);this.curveDraw(this.lastWidth,e),this.lastVelocity=t,this.lastWidth=e},e.prototype.endDraw=function(){if(!sf.base.isNullOrUndefined(this.pointColl)){var t=this.pointColl.length>2,e=this.pointColl[0];!t&&e&&this.strokeDraw(e)}},e.prototype.curveDraw=function(t,e){var n,i,s,a,o,r,h,c,l,u,d=this.canvasContext,p=e-t,g=this.bezierLengthCalc(),v=2*Math.ceil(g);for(d.beginPath(),i=0;i<v;i++)o=(a=(s=i/v)*s)*s,l=(c=(h=(r=1-s)*r)*r)*this.startPoint.x,l+=3*h*s*this.controlPoint1.x,l+=3*r*a*this.controlPoint2.x,l+=o*this.endPoint.x,u=c*this.startPoint.y,u+=3*h*s*this.controlPoint1.y,u+=3*r*a*this.controlPoint2.y,u+=o*this.endPoint.y,n=Math.min(t+o*p,this.maxStrokeWidth),this.arcDraw(l,u,n);d.closePath(),d.fill(),this.isSignatureEmpty=!1},e.prototype.strokeDraw=function(t){var e=this.canvasContext,n=(this.minStrokeWidth+this.maxStrokeWidth)/2;e.beginPath(),this.arcDraw(t.x,t.y,n),e.closePath(),e.fill(),this.isSignatureEmpty=!1},e.prototype.arcDraw=function(t,e,n){this.signPointsColl.push({x:t,y:e});var i=this.canvasContext;i.moveTo(t,e),i.arc(t,e,n,0,2*Math.PI,!1)},e.prototype.calculateCurveControlPoints=function(t,e,n){var i=t.x-e.x,s=t.y-e.y,a=e.x-n.x,o=e.y-n.y,r=(t.x+e.x)/2,h=(t.y+e.y)/2,c=(e.x+n.x)/2,l=(e.y+n.y)/2,u=Math.sqrt(i*i+s*s),d=Math.sqrt(a*a+o*o),p=d/(u+d),g=c+(r-c)*p,v=l+(h-l)*p,m=e.x-g,f=e.y-v;return{controlPoint1:this.point(r+m,h+f,0),controlPoint2:this.point(c+m,l+f,0)}},e.prototype.bezierLengthCalc=function(){var t,e,n,i,s,a,o,r,h=0;for(t=0;t<=10;t++)e=t/10,n=this.bezierPointCalc(e,this.startPoint.x,this.controlPoint1.x,this.controlPoint2.x,this.endPoint.x),i=this.bezierPointCalc(e,this.startPoint.y,this.controlPoint1.y,this.controlPoint2.y,this.endPoint.y),t>0&&(o=n-s,r=i-a,h+=Math.sqrt(o*o+r*r)),s=n,a=i;return h},e.prototype.bezierPointCalc=function(t,e,n,i,s){return e*(1-t)*(1-t)*(1-t)+3*n*(1-t)*(1-t)*t+3*i*(1-t)*t*t+s*t*t*t},e.prototype.pointVelocityCalc=function(t){return this.time!==t.time?this.distanceTo(t)/(this.time-t.time):0},e.prototype.distanceTo=function(t){return Math.sqrt(Math.pow(this.pointX-t.x,2)+Math.pow(this.pointY-t.y,2))},e.prototype.isRead=function(t){t?sf.base.EventHandler.remove(this.canvasContext.canvas,"mousedown touchstart",this.mouseDownHandler):this.disabled||sf.base.EventHandler.add(this.canvasContext.canvas,"mousedown touchstart",this.mouseDownHandler,this)},e.prototype.enableOrDisable=function(t){this.disabled=t,t?(this.canvasContext.canvas.style.filter="opacity(0.5)",this.isRead(!0)):(this.canvasContext.canvas.style.filter="",this.isRead(!1))},e.prototype.updateSnapCollection=function(t){if(sf.base.isNullOrUndefined(this.incStep)?(this.incStep=-1,this.incStep++,this.snapColl=[],this.clearArray=[]):this.incStep++,this.incStep<this.snapColl.length&&(this.snapColl.length=this.incStep),this.incStep>0){var e=this.createElement("canvas",{className:"e-"+this.getModuleName()+"-wrapper"}),n=e.getContext("2d");e.width=this.canvasContext.canvas.width,e.height=this.canvasContext.canvas.height,n.drawImage(this.canvasContext.canvas,0,0,e.width,e.height),this.snapColl.push(e.toDataURL())}else this.snapColl.push(this.canvasContext.canvas.toDataURL());t&&this.clearArray.push(this.incStep)},e.prototype.setBackgroundImage=function(t,e){var n=this,i=new Image;i.crossOrigin="anonymous",i.src=t,"temp"==e?i.onload=function(){n.tempContext.globalCompositeOperation="source-over",n.tempContext.drawImage(i,0,0,n.element.width,n.element.height)}:(i.onload=function(){n.canvasContext.globalCompositeOperation="source-over",n.canvasContext.drawImage(i,0,0,n.element.width,n.element.height),n.updateSnapCollection(),n.saveBackground(!0)},this.canvasContext.clearRect(0,0,this.canvasContext.canvas.width,this.canvasContext.canvas.height))},e.prototype.setBackgroundColor=function(t){var e,n,i=this.canvasContext;for(i.strokeStyle=t,e=1;e<=i.canvas.width;e++)for(n=1;n<=i.canvas.height;n++)i.strokeRect(0,0,e,n);this.updateSnapCollection()},e.prototype.loadPersistedSignature=function(){if(!sf.base.isNullOrUndefined(this.signatureValue)){var t=this,e=new Image;e.src=this.signatureValue,e.onload=function(){t.canvasContext.clearRect(0,0,t.element.width,t.element.height),t.canvasContext.drawImage(e,0,0),t.updateSnapCollection()},this.isSignatureEmpty=!1}},e.prototype.getBlob=function(t){for(var e=t.split(","),n=e[0].match(/:(.*?);/)[1],i=atob(e[1]),s=i.length,a=new Uint8Array(s);s--;)a[s]=i.charCodeAt(s);return new Blob([a],{type:n})},e.prototype.download=function(t,e){var n=URL.createObjectURL(t),i=document.createElement("a");i.href=n,i.target="_parent",i.download=e,(document.body||document.documentElement).appendChild(i),i.click(),i.parentNode.removeChild(i)},e.prototype.internalRefresh=function(){this.pointColl=[],this.lastVelocity=0,this.lastWidth=(this.minStrokeWidth+this.maxStrokeWidth)/2},e.prototype.refresh=function(){this.isResponsive=!1,this.setHTMLProperties(),this.resizeHandler(),this.internalRefresh()},e.prototype.clear=function(){this.canvasContext.clearRect(0,0,this.canvasContext.canvas.width,this.canvasContext.canvas.height),this.tempContext.clearRect(0,0,this.tempContext.canvas.width,this.tempContext.canvas.height),this.saveWithBackground&&this.backgroundImage&&this.setBackgroundImage(this.backgroundImage,"temp"),this.internalRefresh(),this.signRatioPointsColl=[],this.updateSnapCollection(!0),this.isSignatureEmpty=this.canRedraw=!0,this.isBlazor?this.dotnetRef.invokeMethodAsync("TriggerEventAsync","Clear"):this.trigger("change",{actionName:"clear"})},e.prototype.undo=function(){var t=this;if(this.incStep>0){this.incStep--;var e=new Image;e.src=this.snapColl[this.incStep],e.onload=function(){t.canvasContext.clearRect(0,0,t.element.width,t.element.height),t.canvasContext.drawImage(e,0,0,t.element.width,t.element.height)}}this.isClear(),this.isBlazor?this.dotnetRef.invokeMethodAsync("TriggerEventAsync","Undo"):this.trigger("change",{actionName:"undo"})},e.prototype.redo=function(){var t=this;if(this.incStep<this.snapColl.length-1){this.incStep++;var e=new Image;e.src=this.snapColl[this.incStep],e.onload=function(){t.canvasContext.clearRect(0,0,t.element.width,t.element.height),t.canvasContext.drawImage(e,0,0,t.element.width,t.element.height)}}this.isClear(),this.isBlazor?this.dotnetRef.invokeMethodAsync("TriggerEventAsync","Redo"):this.trigger("change",{actionName:"redo"})},e.prototype.isClear=function(){if(this.clearArray){for(var t=!1,e=0;e<this.clearArray.length;e++)this.clearArray[e]===this.incStep&&(this.isSignatureEmpty=!0,t=!0);t||(this.isSignatureEmpty=!1)}},e.prototype.isEmpty=function(){return this.isSignatureEmpty},e.prototype.canUndo=function(){return this.incStep>0},e.prototype.canRedo=function(){return this.incStep<this.snapColl.length-1},e.prototype.draw=function(t,e,n,i,s){this.canvasContext.clearRect(0,0,this.canvasContext.canvas.width,this.canvasContext.canvas.height),e=e||"Arial",n=n||30,this.canvasContext.font=n+"px "+e;var a=this.element.width/2,o=this.element.height/2;sf.base.isNullOrUndefined(i)&&sf.base.isNullOrUndefined(s)?(this.canvasContext.textAlign="center",this.canvasContext.textBaseline="middle"):(a=sf.base.isNullOrUndefined(i)?a:i,o=sf.base.isNullOrUndefined(s)?o+n/2:s+n/2),this.canvasContext.fillText(t,a,o),this.updateSnapCollection(),this.isSignatureEmpty=!1,this.trigger("change",{actionName:"draw-text"})},e.prototype.load=function(t,e,n){n=n||this.element.height,e=e||this.element.width,this.canvasContext.clearRect(0,0,this.canvasContext.canvas.width,this.canvasContext.canvas.height);var i=this,s=new Image;s.src=t,"data"!==t.slice(0,4)&&(s.crossOrigin="anonymous"),s.onload=function(){Promise.all([createImageBitmap(s,0,0,e,n)]).then((function(s){var a=document.createElement("canvas");a.width=e,a.height=n,a.getContext("2d").drawImage(s[0],0,0),"data"!==t.slice(0,4)&&(i.canvasContext.globalCompositeOperation="source-over"),i.canvasContext.drawImage(a,0,0,e,n,0,0,i.element.width,i.element.height),i.updateSnapCollection()}))},this.isSignatureEmpty=this.canRedraw=!1},e.prototype.saveBackground=function(t){var e;if(e=t&&this.backgroundImage?this.snapColl[this.incStep-1]:this.snapColl[this.incStep],t||(this.canvasContext.clearRect(0,0,this.canvasContext.canvas.width,this.canvasContext.canvas.height),this.backgroundImage?this.setBackgroundImage(this.backgroundImage):this.backgroundColor&&(this.setBackgroundColor(this.backgroundColor),t=!0)),t){var n=this,i=new Image;i.crossOrigin="anonymous",i.src=e,i.onload=function(){n.backgroundLoaded=!0,n.canvasContext.globalCompositeOperation="source-over",n.canvasContext.drawImage(i,0,0,n.element.width,n.element.height),n.save(n.fileType,n.fileName)}}},e.prototype.save=function(t,e){if(this.saveWithBackground&&null==this.backgroundLoaded&&(this.backgroundImage||this.backgroundColor))this.backgroundLoaded=!1,this.fileType=t,this.fileName=e,this.saveBackground(!1);else if("Svg"===t)e=e||"Signature",this.toSVG(e);else if("Jpeg"===t)if(e=e||"Signature",!this.saveWithBackground||this.saveWithBackground&&!this.backgroundImage&&!this.backgroundColor)this.toJPEG(e);else{var n=this.canvasContext.canvas.toDataURL("image/jpeg");this.download(this.getBlob(n),e+".jpeg")}else{e=e||"Signature";n=this.canvasContext.canvas.toDataURL("image/png");this.download(this.getBlob(n),e+".png")}this.saveWithBackground&&this.backgroundLoaded&&this.resetSnap()},e.prototype.resetSnap=function(){this.canvasContext.clearRect(0,0,this.canvasContext.canvas.width,this.canvasContext.canvas.height);var t=this,e=new Image;e.src=this.snapColl[this.incStep-1],e.onload=function(){t.canvasContext.drawImage(e,0,0,t.element.width,t.element.height),t.updateSnapCollection()},this.backgroundLoaded=null,this.snapColl.pop(),this.incStep--,this.snapColl.pop(),this.incStep--},e.prototype.toJPEG=function(t){var e=this,n=this.snapColl[this.incStep];this.setBackgroundColor("#ffffff");var i=this,s=new Image;s.crossOrigin="anonymous",s.src=n,s.onload=function(){i.canvasContext.globalCompositeOperation="source-over",i.canvasContext.drawImage(s,0,0,i.element.width,i.element.height);var n=i.canvasContext.canvas.toDataURL("image/jpeg");i.download(i.getBlob(n),t+".jpeg"),i.canvasContext.clearRect(0,0,i.canvasContext.canvas.width,i.canvasContext.canvas.height),e.resizeHandler()},this.snapColl.pop(),this.incStep--},e.prototype.toSVG=function(t,e){var n=document.createElementNS("http://www.w3.org/2000/svg","svg");n.setAttribute("width",this.canvasContext.canvas.width.toString()),n.setAttribute("height",this.canvasContext.canvas.height.toString());var i=document.createElementNS("http://www.w3.org/2000/svg","image");i.setAttributeNS(null,"height",this.canvasContext.canvas.height.toString()),i.setAttributeNS(null,"width",this.canvasContext.canvas.width.toString()),i.setAttributeNS("http://www.w3.org/1999/xlink","xlink:href",e),n.appendChild(i);var s='<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="'+this.canvasContext.canvas.width+'" height="'+this.canvasContext.canvas.height+'">',a=n.innerHTML,o="data:image/svg+xml;base64,"+btoa(s+a+"</svg>");return null==t?o:(this.download(this.getBlob(o),t+".svg"),null)},e.prototype.saveAsBlob=function(){return this.getBlob(this.canvasContext.canvas.toDataURL("image/png"))},e.prototype.getSignature=function(t){return this.saveWithBackground&&this.backgroundColor&&!this.backgroundImage?(this.tempContext.fillStyle=this.backgroundColor,this.tempContext.fillRect(0,0,this.canvasContext.canvas.width,this.canvasContext.canvas.height)):this.backgroundColor||this.backgroundImage||"Jpeg"!==t||(this.tempContext.fillStyle="#fff",this.tempContext.fillRect(0,0,this.canvasContext.canvas.width,this.canvasContext.canvas.height)),this.tempContext.drawImage(this.element,0,0),"Jpeg"===t?this.tempCanvas.toDataURL("image/jpeg"):"Svg"===t?this.toSVG(null,this.tempCanvas.toDataURL("image/png")):this.tempCanvas.toDataURL("image/png")},e.prototype.getModuleName=function(){return"signature"},e.prototype.getPersistData=function(){return this.signatureValue=this.snapColl[this.incStep],this.addOnPersist(["signatureValue"])},e.prototype.destroy=function(){"image-editor"!==this.getModuleName()&&(this.unwireEvents(null),sf.base.removeClass([this.element],"e-"+this.getModuleName()),this.element.removeAttribute("tabindex"),this.pointColl=null),t.prototype.destroy.call(this)},e.prototype.propertyChanged=function(t,e){var n=this.canvasContext;switch(t){case"backgroundColor":n.canvas.style.backgroundColor=e,this.backgroundColor=e;break;case"backgroundImage":n.canvas.style.backgroundImage="url("+e+")",this.backgroundImage=e,this.saveWithBackground&&this.setBackgroundImage(this.backgroundImage,"temp");break;case"strokeColor":n.fillStyle=e,this.strokeColor=e;break;case"saveWithBackground":this.saveWithBackground=e;break;case"maxStrokeWidth":this.maxStrokeWidth=e;break;case"minStrokeWidth":this.minStrokeWidth=e;break;case"velocity":this.velocity=e;break;case"isReadOnly":this.isRead(e);break;case"disabled":this.enableOrDisable(e)}},e}(sf.base.Component),s=(e=function(t,n){return(e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n])})(t,n)},function(t,n){function i(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(i.prototype=n.prototype,new i)}),a=function(){function t(t,e,n,i){this.dataId=t,this.element=e,window.sfBlazor.setCompInstance(this),this.dotnetRef=i,this.signatureBase=new o,this.signatureBase.backgroundColor=n.backgroundColor,this.signatureBase.backgroundImage=n.backgroundImage,this.signatureBase.strokeColor=n.strokeColor||"#000000",this.signatureBase.isReadOnly=n.isReadOnly,this.signatureBase.disabled=n.disabled,this.signatureBase.saveWithBackground=n.saveWithBackground,this.signatureBase.signatureValue=n.signatureValue,this.signatureBase.minStrokeWidth=n.minStrokeWidth||.5,this.signatureBase.maxStrokeWidth=n.maxStrokeWidth||2,this.signatureBase.velocity=n.velocity||.7}return t.prototype.saveAsBlob=function(t){var e=t.getContext("2d").canvas.toDataURL().split(",");return atob(e[1]).length},t}(),o=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return s(e,t),e.prototype.onPropertyChanged=function(){},e.prototype.preRender=function(){},e.prototype.render=function(){},e}(i);return{initialize:function(t,e,n,i){sf.base.isNullOrUndefined(e)||(new a(t,e,n,i),window.sfBlazor.getCompInstance(t).signatureBase.initialize(e,i))},clear:function(t){window.sfBlazor.getCompInstance(t).signatureBase.clear()},undo:function(t){window.sfBlazor.getCompInstance(t).signatureBase.undo()},redo:function(t){window.sfBlazor.getCompInstance(t).signatureBase.redo()},save:function(t,e,n){var i=window.sfBlazor.getCompInstance(t);switch(e){case 0:i.signatureBase.save("Png",n);break;case 1:i.signatureBase.save("Jpeg",n);break;case 2:i.signatureBase.save("Svg",n)}},draw:function(t,e,n,i,s,a){window.sfBlazor.getCompInstance(t).signatureBase.draw(e,n,i,s,a)},load:function(t,e,n,i){window.sfBlazor.getCompInstance(t).signatureBase.load(e,n,i)},getSignature:function(t,e){var n=window.sfBlazor.getCompInstance(t);switch(e){case 0:return n.signatureBase.getSignature("Png");case 1:return n.signatureBase.getSignature("Jpeg");case 2:return n.signatureBase.getSignature("Svg")}return null},saveAsBlob:function(t,e){return window.sfBlazor.getCompInstance(t).saveAsBlob(e)},onPropertyChanged:function(t,e,n){window.sfBlazor.getCompInstance(t).signatureBase.propertyChanged(e,n)},isEmpty:function(t){return window.sfBlazor.getCompInstance(t).signatureBase.isEmpty()},canUndo:function(t){return window.sfBlazor.getCompInstance(t).signatureBase.canUndo()},canRedo:function(t){return window.sfBlazor.getCompInstance(t).signatureBase.canRedo()},destroy:function(t){window.sfBlazor.getCompInstance(t).signatureBase.destroy()},refresh:function(t){window.sfBlazor.getCompInstance(t).signatureBase.refresh()}}}()}}]);(async()=>{await import(`${document.baseURI}_content/Syncfusion.Blazor/scripts/syncfusion-blazor-base.min.js?v=26.1.undefined`).then(()=>{sfBlazor.loadDependencies('sfsignature');})})();