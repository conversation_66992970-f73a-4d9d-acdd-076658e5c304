# YCRS_Files Sno 欄位修復說明

## 問題描述

在檔案上傳過程中遇到以下錯誤：
```
無法插入 NULL 值到資料行 'Sno'，資料表 'YMDB.dbo.YCRS_Files'; 資料行不得有 Null。INSERT 失敗。
```

## 問題原因

YCRS_Files 表的 Sno 欄位（主鍵）在資料庫中沒有設定為 IDENTITY（自動遞增），但 Entity Framework 模型中期望它是自動生成的。

## 解決方案

### 1. 模型修改
在 `Models/YouthCareReportService/YCRS_Files.cs` 中添加了 `DatabaseGenerated` 屬性：

```csharp
[Key]
[DatabaseGenerated(DatabaseGeneratedOption.Identity)]
public int Sno { get; set; } //系統流水號
```

### 2. DbContext 配置
在 `Data/YCRSDbContext.cs` 中添加了 OnModelCreating 配置：

```csharp
protected override void OnModelCreating(ModelBuilder modelBuilder)
{
    base.OnModelCreating(modelBuilder);

    // 設定 YCRS_Files 的 Sno 欄位為自動遞增
    modelBuilder.Entity<YCRS_Files>()
        .Property(f => f.Sno)
        .UseIdentityColumn();

    // 設定 YCRS_AccessLog 的 Id 欄位為自動遞增
    modelBuilder.Entity<YCRS_AccessLog>()
        .Property(a => a.Id)
        .UseIdentityColumn();
}
```

### 3. 程式碼修復
在 `SecureFileUploadService.cs` 中添加了專門的方法來處理 YCRS_Files 記錄創建：

```csharp
/// <summary>
/// 創建 YCRS_Files 記錄並儲存到資料庫
/// </summary>
public async Task<bool> CreateYCRSFileRecordAsync(string recordId, string originalFileName, string filePath)
{
    try
    {
        // 取得下一個 Sno 值
        var maxSno = await _ycrsContext.YCRS_Files.MaxAsync(f => (int?)f.Sno) ?? 0;
        
        var fileRecord = new Models.YCRS_Files
        {
            Sno = maxSno + 1,
            Id = recordId,
            FileName = originalFileName,
            Filepath = filePath,
            Createdate = DateTime.Now
        };

        _ycrsContext.YCRS_Files.Add(fileRecord);
        await _ycrsContext.SaveChangesAsync();

        return true;
    }
    catch (Exception ex)
    {
        _logger.LogError(ex, "創建 YCRS 檔案記錄失敗");
        return false;
    }
}
```

### 4. 使用方式更新
在 `CareCaseNewEdit.razor` 中更新了檔案上傳邏輯：

```csharp
// 使用安全檔案上傳服務創建檔案記錄
var fileRecordCreated = await SecureUploadService.CreateYCRSFileRecordAsync(
    careRecord.Id ?? "", 
    file.Name, 
    uploadResult.FilePath);

if (!fileRecordCreated)
{
    await JS.InvokeAsync<object>("alert", $"檔案記錄創建失敗: {file.Name}");
    return;
}
```

## 資料庫修復腳本

如果需要修復現有資料庫中的 Sno 欄位設定，可以執行 `Scripts/Fix_YCRS_Files_Identity.sql` 腳本。

**注意：執行前請先備份資料庫！**

## 臨時解決方案

目前的實作使用手動計算下一個 Sno 值的方式來避免 NULL 插入錯誤。這是一個臨時解決方案，理想情況下應該：

1. 執行資料庫修復腳本，將 Sno 欄位設定為 IDENTITY
2. 移除手動設定 Sno 值的程式碼
3. 讓資料庫自動生成 Sno 值

## 測試建議

1. 測試檔案上傳功能是否正常運作
2. 檢查 YCRS_Files 表中的記錄是否正確創建
3. 驗證 Sno 值是否按順序遞增
4. 確認檔案下載功能仍然正常

## 後續改進

1. **資料庫遷移**：創建 Entity Framework 遷移來正確設定 IDENTITY 欄位
2. **並發處理**：目前的 MaxAsync + 1 方式在高並發情況下可能產生重複值
3. **事務處理**：確保檔案上傳和資料庫記錄創建在同一個事務中
4. **錯誤恢復**：如果資料庫插入失敗，應該清理已上傳的檔案

## 相關檔案

- `Models/YouthCareReportService/YCRS_Files.cs`
- `Data/YCRSDbContext.cs`
- `Services/SecureFileUploadService.cs`
- `Components/Pages/YCRS/CareCaseNewEdit.razor`
- `Scripts/Fix_YCRS_Files_Identity.sql`
