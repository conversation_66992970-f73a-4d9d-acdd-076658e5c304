﻿using Intra2025.Models.Report;
using Intra2025.Models;
using Microsoft.EntityFrameworkCore;

namespace Intra2025.Data
{
    public class AppDbContext : DbContext
    {
        public DbSet<Report> Reports { get; set; } = null!;
        public DbSet<ProjectProgressModel> ProjectProgressModels { get; set; } = null!;

        public AppDbContext(DbContextOptions<AppDbContext> options) : base(options)
        {
        }
        //public DbSet<ReportsFile> ReportsFiles { get; set; }
    }
}
