# 安全檔案上傳系統使用說明

## 概述

本系統實作了完整的安全檔案上傳和下載機制，解決了 Problem #8.4 - 無限制檔案上傳漏洞。

## 主要功能

### 1. 安全檔案上傳 (SecureFileUploadService)
- **檔案類型驗證**：白名單檔案類型 + MIME 類型 + 檔案簽名檢查
- **檔案大小限制**：可配置的檔案大小限制
- **內容安全掃描**：檢測可執行內容和惡意腳本
- **安全檔案命名**：防止路徑遍歷和檔案名稱攻擊
- **安全儲存**：檔案儲存在非 Web 可存取的 `App_Data/SecureUploads/` 目錄

### 2. 檔案上傳配置 (FileUploadConfigService)
- **分類別管理**：YCRS_Care、Reports、ComRemit 等不同類別
- **靈活配置**：檔案大小、類型、數量限制可獨立設定
- **集中化管理**：統一的配置管理介面

### 3. 安全檔案下載 (SecureFileDownloadService)
- **權限控制**：基於使用者權限和資料關聯性
- **路徑驗證**：防止路徑遍歷攻擊
- **資料庫驗證**：確保檔案存在於系統記錄中
- **安全標頭**：設定適當的 HTTP 安全標頭

### 4. 檔案遷移 (FileMigrationService)
- **自動遷移**：將現有不安全檔案移動到安全目錄
- **資料庫同步**：自動更新檔案路徑記錄
- **狀態追蹤**：提供詳細的遷移進度和結果

## 檔案類別配置

### YCRS_Care (溫馨關懷表)
- **最大檔案大小**：20MB
- **允許格式**：.pdf, .zip, .7z
- **最大檔案數**：1個
- **儲存位置**：`App_Data/SecureUploads/YCRS_Care/`

### Reports (規劃報告書)
- **最大檔案大小**：200MB
- **允許格式**：.pdf, .odt, .ods, .odp, .zip, .7z
- **最大檔案數**：5個
- **儲存位置**：`App_Data/SecureUploads/Reports/`

### ComRemit (支付作業)
- **最大檔案大小**：5MB
- **允許格式**：.csv, .txt
- **最大檔案數**：1個
- **儲存位置**：`App_Data/SecureUploads/ComRemit/`

## API 端點

### 安全下載端點
```
GET /api/secure-download?filePath={encodedFilePath}
```
- 替代不安全的 `/api/downloadfile` 端點
- 完整的權限檢查和路徑驗證
- 安全的檔案傳輸機制

### 管理介面
```
GET /Admin/FileSecurityManagement
```
- 檔案安全管理頁面（僅管理員可存取）
- 遷移狀態監控和執行
- 配置檢視和管理

## 使用方式

### 1. 檔案上傳
```csharp
// 在 Razor 頁面中注入服務
@inject SecureFileUploadService SecureUploadService
@inject FileUploadConfigService UploadConfig

// 上傳檔案
var uploadResult = await SecureUploadService.UploadFileAsync(file, "YCRS_Care", userId);
if (uploadResult.IsSuccess)
{
    // 儲存檔案資訊到資料庫
    var fileRecord = new FileRecord
    {
        FileName = file.Name,
        FilePath = uploadResult.FilePath,
        FileHash = uploadResult.FileHash
    };
}
```

### 2. 檔案下載
```html
<!-- 使用安全下載連結 -->
<a href="/api/secure-download?filePath=@(System.Net.WebUtility.UrlEncode(file.Filepath))">
    @file.FileName
</a>
```

### 3. 檔案遷移
```csharp
// 在管理介面中執行遷移
@inject FileMigrationService MigrationService

var migrationResult = await MigrationService.MigrateAllFilesAsync();
```

## 安全特性

### 檔案驗證
- ✅ 檔案簽名檢查（Magic Numbers）
- ✅ MIME 類型驗證
- ✅ 檔案大小限制
- ✅ 檔案名稱安全檢查
- ✅ 惡意內容掃描

### 存取控制
- ✅ 使用者權限驗證
- ✅ 資料關聯性檢查
- ✅ 路徑遍歷防護
- ✅ 資料庫記錄驗證

### 稽核和監控
- ✅ 完整的操作日誌
- ✅ 檔案雜湊值記錄
- ✅ 遷移狀態追蹤
- ✅ 錯誤處理和報告

## 部署注意事項

### 1. 目錄權限
確保 `App_Data/SecureUploads/` 目錄具有適當的讀寫權限，但不可透過 Web 直接存取。

### 2. 檔案遷移
首次部署後，使用管理介面執行檔案遷移，將現有檔案移動到安全目錄。

### 3. 舊端點
舊的 `/api/downloadfile` 端點仍保留以維持向後相容性，但會記錄警告日誌。建議逐步更新所有下載連結使用新的安全端點。

### 4. 監控
定期檢查檔案上傳活動和稽核日誌，確保系統安全運作。

## 故障排除

### 檔案上傳失敗
1. 檢查檔案格式是否在允許清單中
2. 確認檔案大小未超過限制
3. 檢查 `App_Data/SecureUploads/` 目錄權限
4. 查看應用程式日誌中的詳細錯誤訊息

### 檔案下載失敗
1. 確認使用者有權限存取該檔案
2. 檢查檔案是否存在於資料庫記錄中
3. 驗證檔案實際存在於檔案系統中
4. 檢查檔案路徑格式是否正確

### 遷移問題
1. 確保有足夠的磁碟空間
2. 檢查檔案權限設定
3. 查看遷移日誌中的詳細錯誤訊息
4. 必要時可以分批進行遷移

## 聯絡資訊

如有任何問題或建議，請聯絡系統管理員。
