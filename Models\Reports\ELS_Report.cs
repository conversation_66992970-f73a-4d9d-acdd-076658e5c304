﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Intra2025.Models.Reports.Report
{
    public class ELS_REPORTC
    {
        [Key]
        public int Sno { get; set; }
        public int? Typesno { get; set; }

        [Required(ErrorMessage = "【計畫名稱】為必填欄位")]
        public string? Topic { get; set; } = string.Empty;
        public string? Tel { get; set; } = string.Empty;
        public string? Content { get; set; } = string.Empty;
        public string? Todepartids { get; set; } = string.Empty;
        public int? Readcount { get; set; }
        public DateTime? Begindate { get; set; }
        public DateTime? Enddate { get; set; }
        public string? Postdepartid { get; set; } = string.Empty;
        public string? Postuserid { get; set; } = string.Empty;
        public DateTime? Postdate { get; set; }

        [Column("contact_name")]
        public string? ContactName { get; set; } = string.Empty;

        public string? Ownuserid { get; set; } = string.Empty;
        public string? Owndepartid { get; set; } = string.Empty;
        public byte? State { get; set; }
        public bool? Enabled { get; set; }
        public DateTime? Createdate { get; set; }
        public string? Createuserid { get; set; } = string.Empty;
        public DateTime? Modifydate { get; set; }
        public string? Modifyuserid { get; set; } = string.Empty;

        [Column("power_depart")]
        [Required(ErrorMessage = "【權責單位】為必填欄位")]
        public string? PowerDepart { get; set; } = string.Empty;

        public string? Consign { get; set; } = string.Empty;
        public string? Fund { get; set; } = string.Empty;

        [Column("user_keyword")]
        public string? UserKeyword { get; set; } = string.Empty;

        [Column("paper_location")]
        public string? PaperLocation { get; set; } = string.Empty;

        [Column("plan_year")]
        [Required(ErrorMessage = "【年度】為必填欄位")]
        public int? PlanYear { get; set; }

        [Column("contact_depart")]
        public string? ContactDepart { get; set; } = string.Empty;

        //public ICollection<ELS_REPORT_FILEC> ?ReportFiles { get; set; }
    }
}
