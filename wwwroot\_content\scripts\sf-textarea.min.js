/*!*  filename: sf-textarea.min.js 
*     version : 26.1.41 
*     Copyright Syncfusion Inc. 2001 - 2024. All rights reserved. 
*     Use of this code is subject to the terms of our license.
*     A copy of the current license can be obtained at any time by e-mailing 
*     <EMAIL>. Any infringement will be prosecuted under 
*     applicable laws. 
*/
(window.webpackJsonp=window.webpackJsonp||[]).push([[64],{"./bundles/sf-textarea.js":function(t,e,n){"use strict";n.r(e);n("./modules/sf-textarea.js")},"./modules/sf-textarea.js":function(t,e){window.sfBlazor=window.sfBlazor||{},window.sfBlazor.TextArea=function(){"use strict";var t=function(){function t(t,e,n){this.element=t,this.container=n,this.element.blazor_input_instance=this,this.dotNetRef=e,this.isDestroyed=!1}return t.prototype.calculateWidth=function(){var t=document.getElementsByClassName("e-float-text");if(null!==t)for(var e=0;e<t.length;e++)if(this.container.classList.contains("e-outline")&&this.container.classList.contains("e-prepend")&&t[e].classList.contains("e-label-top")){var n=this.container.clientWidth-this.element.clientWidth;t[e].style.left=-n.toString()+"px",t[e].style.width="auto"}else t[e].style.left="0px"},t}();return{calculateWidth:function(e,n,i){e&&new t(e,n,i),e&&e.blazor_input_instance&&e.blazor_input_instance.calculateWidth()},focusOut:function(t){t.blur()}}}()}}]);(async()=>{await import(`${document.baseURI}_content/Syncfusion.Blazor/scripts/syncfusion-blazor-base.min.js?v=26.1.undefined`).then(()=>{sfBlazor.loadDependencies('sftextarea');})})();