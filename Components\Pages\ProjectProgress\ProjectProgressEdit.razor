@page "/ProjectProgressEdit"
@page "/ProjectProgressEdit/{Id:int?}"
@using Microsoft.AspNetCore.Components.Forms
@using Microsoft.JSInterop
@using System.ComponentModel.DataAnnotations
@using Intra2025.Services
@using Intra2025.Models
@inject ProjectProgressPresentationService PresentationService
@inject IJSRuntime JS
@inject NavigationManager NavigationManager

<script src="~/js/projectprogress.js"></script>

<title>計畫進度管理</title>
<h3>計畫進度管理</h3>

<EditForm Model="@model" OnValidSubmit="HandleValidSubmit">
    <DataAnnotationsValidator />
    
    <div class="container-fluid">
        <!-- 主要資訊表格 -->
        <table class="table table-bordered">
            <thead style="background-color: #404346; color: white;">
                <tr>
                    <th rowspan="2">主管機關(單位)</th>
                    <th colspan="4">計畫屬性(請打V)</th>
                    <th rowspan="2">總計畫/工程<br/>期程</th>
                    <th rowspan="2">總計畫/工程<br/>經費(千元)</th>
                    <th rowspan="2">本年可支用預算(元)</th>
                </tr>
                <tr>
                    <th style="background-color: #404346;">規劃設計</th>
                    <th style="background-color: #404346;">工程</th>
                    <th style="background-color: #404346;">一者<br/>均有</th>
                    <th style="background-color: #404346;">其他</th>
                </tr>
            </thead>
            <tbody>
                <tr style="background-color: #ffebee;">
                    <td>
                        <InputText @bind-Value="model.MainAgency" class="form-control" placeholder="農業處(海洋所)" />
                    </td>
                    <td class="text-center">
                        <InputCheckbox @bind-Value="model.IsPlanningDesign" />
                        @if (model.IsPlanningDesign) { <span style="color: red;">V</span> }
                    </td>
                    <td class="text-center">
                        <InputCheckbox @bind-Value="model.IsEngineering" />
                        @if (model.IsEngineering) { <span style="color: red;">V</span> }
                    </td>
                    <td class="text-center">
                        <InputCheckbox @bind-Value="model.IsBoth" />
                        @if (model.IsBoth) { <span style="color: red;">V</span> }
                    </td>
                    <td class="text-center">
                        <InputCheckbox @bind-Value="model.IsOther" />
                        @if (model.IsOther) { <span style="color: red;">V</span> }
                    </td>
                    <td>
                        <InputText @bind-Value="model.ProjectDuration" class="form-control" placeholder="120日曆天" />
                    </td>
                    <td>
                        <InputNumber @bind-Value="model.TotalBudget" class="form-control" placeholder="16,700" />
                    </td>
                    <td>
                        <InputNumber @bind-Value="model.CurrentYearBudget" class="form-control" placeholder="16,700" />
                    </td>
                </tr>
            </tbody>
        </table>

        <!-- 工程累計進度表格 -->
        <table class="table table-bordered mt-3">
            <thead style="background-color: #404346; color: white;">
                <tr>
                    <th rowspan="2">工程累計進度</th>
                    <th rowspan="2">預定</th>
                    <th rowspan="2">實際</th>
                    <th rowspan="2">比較</th>
                    <th colspan="3">本年累計可支用預算執行進度(千元)</th>
                </tr>
                <tr>
                    <th style="background-color: #404346;">預定支用數(A)<br/>(千元)</th>
                    <th style="background-color: #404346;">預算執行數(B)<br/>(千元)</th>
                    <th style="background-color: #404346;">執行率(%)<br/>(B)/(A)</th>
                </tr>
            </thead>
            <tbody>
                <tr style="background-color: #ffebee;">
                    <td>年累計</td>
                    <td>
                        <InputNumber @bind-Value="model.YearPlannedProgress" class="form-control" />%
                    </td>
                    <td>
                        <InputNumber @bind-Value="model.YearActualProgress" class="form-control" />%
                    </td>
                    <td>
                        <span class="text-danger">@(model.YearActualProgress - model.YearPlannedProgress)%</span>
                    </td>
                    <td>
                        <InputNumber @bind-Value="model.PlannedExpenditure" class="form-control" />
                    </td>
                    <td>
                        <InputNumber @bind-Value="model.ActualExpenditure" class="form-control" />
                    </td>
                    <td>
                        <span>@(model.PlannedExpenditure > 0 ? Math.Round((decimal)model.ActualExpenditure / model.PlannedExpenditure * 100, 1) : 0)%</span>
                    </td>
                </tr>
                <tr style="background-color: #ffebee;">
                    <td>總累計</td>
                    <td>
                        <InputNumber @bind-Value="model.TotalPlannedProgress" class="form-control" />%
                    </td>
                    <td>
                        <InputNumber @bind-Value="model.TotalActualProgress" class="form-control" />%
                    </td>
                    <td>
                        <span class="text-danger">@(model.TotalActualProgress - model.TotalPlannedProgress)%</span>
                    </td>
                    <td>
                        <InputNumber @bind-Value="model.TotalPlannedExpenditure" class="form-control" placeholder="8,110" />
                    </td>
                    <td>
                        <InputNumber @bind-Value="model.TotalActualExpenditure" class="form-control" placeholder="203" />
                    </td>
                    <td>
                        <span>@(model.TotalPlannedExpenditure > 0 ? Math.Round((decimal)model.TotalActualExpenditure / model.TotalPlannedExpenditure * 100, 1) : 0)%</span>
                    </td>
                </tr>
            </tbody>
        </table>

        <!-- 落後/停工原因說明 -->
        <div class="mt-3">
            <table class="table table-bordered">
                <tr>
                    <td class="table-info" style="width: 100px; background-color: #d1ecf1;">
                        <div><strong>落後/停工</strong></div>
                        <div><strong>原因</strong></div>
                    </td>
                    <td>
                        <div class="mb-2">
                            <label class="form-label" style="color: blue;"><strong>上期</strong></label>
                            <InputTextArea @bind-Value="model.PreviousPeriodReason" class="form-control" rows="4" 
                                placeholder="原計畫辦理大溪漁港疏浚，因漁民於114年1月反映梯坊淤積嚴重，114年2月年後經測量評估後改辦理梯坊疏浚(暫緩大溪)，受設計期程所有延誤。細部設計預算審圖於114年4月24日核定通過，已於114年4月25日上網，114年5月9日因未達3家而流標，業於114年5月12日上網辦理第二次招標，預計於5月20日開標。因廠商有低於底價80%現場保留決標，預計於20日發文函請廠商說明，廠商於5月26日回函提出說明表示機具人員及設備充足，且表示其設備之工廠力廠商超越豐富，惟本所實際審查仍有待釐清事項，已於6月4日函請廠商進一步補充說明，並於6月18日漁二字第1140006618號函請廠商提供英達營造有限公司於114年6月24日內繳納差額保證金新臺幣83萬2,000元後辦理決標事宜。" />
                        </div>
                    </td>
                </tr>
                <tr>
                    <td class="table-info" style="background-color: #d1ecf1;">
                        <div><strong>本期</strong></div>
                    </td>
                    <td>
                        <InputTextArea @bind-Value="model.CurrentPeriodReason" class="form-control" rows="2" 
                            placeholder="海洋所已依規定決標給英達營造有限公司，限期於114年7月9日前製作合約8份及補履約保證金差額至本所。" />
                    </td>
                </tr>
                <tr>
                    <td class="table-info" style="background-color: #d1ecf1;">
                        <div><strong>具體因應</strong></div>
                        <div><strong>對策</strong></div>
                    </td>
                    <td>
                        <InputTextArea @bind-Value="model.CountermeasureReason" class="form-control" rows="2" 
                            placeholder="督速辦理後續作業。(計畫處：經過減、廠商依限於7月9日製作合約及補履約保證金。)" />
                    </td>
                </tr>
            </table>
        </div>

        <!-- 按鈕區域 -->
        <div class="mt-3">
            <button type="submit" class="btn btn-primary" disabled="@isGeneratingPresentation">
                @if (isGeneratingPresentation)
                {
                    <span class="spinner-border spinner-border-sm me-2" role="status"></span>
                    <span>產生簡報中...</span>
                }
                else
                {
                    <span>儲存</span>
                }
            </button>
            <button type="button" class="btn btn-secondary ms-2" @onclick="Cancel" disabled="@isGeneratingPresentation">取消</button>
        </div>
    </div>
</EditForm>

@code {
    [Parameter] public int? Id { get; set; }
    
    private ProjectProgressModel model = new();
    private bool isGeneratingPresentation = false;

    protected override async Task OnInitializedAsync()
    {
        if (Id.HasValue)
        {
            await LoadData(Id.Value);
        }
    }

    private async Task HandleValidSubmit()
    {
        await SaveData();
    }

    private void Cancel()
    {
        // 取消邏輯
    }

    private async Task LoadData(int id)
    {
        var loadedModel = await PresentationService.GetProjectProgressByIdAsync(id);
        if (loadedModel == null)
        {
            // 如果找不到資料或沒有權限，導向到列表頁面或顯示錯誤訊息
            await JS.InvokeVoidAsync("alert", "您沒有權限存取此資料或資料不存在。");
            NavigationManager.NavigateTo("/ProjectProgress"); // 導向到列表頁面
        }
        else
        {
            model = loadedModel;
        }
    }

    private async Task SaveData()
    {
        try
        {
            // 現有的儲存邏輯...
            
            // 詢問是否要產生簡報檔案
            var shouldGenerate = await JS.InvokeAsync<bool>("confirm", 
                "資料已成功儲存！是否要產生 LibreOffice Impress 簡報檔案？");

            if (shouldGenerate)
            {
                await GeneratePresentation();
            }
        }
        catch (Exception ex)
        {
            await JS.InvokeAsync<object>("alert", $"儲存失敗：{ex.Message}");
        }
    }

    private async Task GeneratePresentation()
    {
        try
        {
            isGeneratingPresentation = true;
            StateHasChanged();

            var fileName = await PresentationService.GeneratePresentationAsync(model);
            
            // 下載檔案
            await JS.InvokeVoidAsync("downloadFile", fileName, $"/temp/{fileName}");
            
            await JS.InvokeAsync<object>("alert", 
                "簡報檔案已產生完成！\n\n使用說明：\n1. 下載完成後，請上傳到您的 Google Drive\n2. 在 Google Drive 中雙擊檔案即可用 Google Slides 開啟編輯\n3. 所有資料已自動填入，您可以進一步調整格式和內容");
        }
        catch (Exception ex)
        {
            await JS.InvokeAsync<object>("alert", $"產生簡報檔案時發生錯誤：{ex.Message}");
        }
        finally
        {
            isGeneratingPresentation = false;
            StateHasChanged();
        }
    }


}
