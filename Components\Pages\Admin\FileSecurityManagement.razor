@page "/Admin/FileSecurityManagement"
@using Intra2025.Components.Base
@using Intra2025.Services
@inherits BasePageComponent
@inject SecureFileUploadService UploadService
@inject FileMigrationService MigrationService
@inject FileUploadConfigService ConfigService

<PageTitle>檔案安全管理</PageTitle>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <h2 class="mb-4">檔案安全管理</h2>
            
            @if (!_userState.IsAdmin)
            {
                <div class="alert alert-danger">
                    <h4>存取被拒絕</h4>
                    <p>您沒有權限存取此頁面。只有管理員可以使用檔案安全管理功能。</p>
                </div>
                return;
            }

            <!-- 遷移狀態卡片 -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">檔案遷移狀態</h5>
                </div>
                <div class="card-body">
                    @if (migrationStatus != null)
                    {
                        <div class="row">
                            <div class="col-md-6">
                                <h6>報告檔案</h6>
                                <div class="progress mb-2">
                                    <div class="progress-bar @(migrationStatus.MigratedReportFiles == migrationStatus.TotalReportFiles ? "bg-success" : "bg-warning")" 
                                         style="width: @(migrationStatus.TotalReportFiles > 0 ? (migrationStatus.MigratedReportFiles * 100 / migrationStatus.TotalReportFiles) : 0)%">
                                        @migrationStatus.MigratedReportFiles / @migrationStatus.TotalReportFiles
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <h6>YCRS 檔案</h6>
                                <div class="progress mb-2">
                                    <div class="progress-bar @(migrationStatus.MigratedYCRSFiles == migrationStatus.TotalYCRSFiles ? "bg-success" : "bg-warning")" 
                                         style="width: @(migrationStatus.TotalYCRSFiles > 0 ? (migrationStatus.MigratedYCRSFiles * 100 / migrationStatus.TotalYCRSFiles) : 0)%">
                                        @migrationStatus.MigratedYCRSFiles / @migrationStatus.TotalYCRSFiles
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        @if (migrationStatus.RemainingFilesInOldDirectory > 0)
                        {
                            <div class="alert alert-warning">
                                <strong>警告：</strong>舊上傳目錄中仍有 @migrationStatus.RemainingFilesInOldDirectory 個檔案未遷移。
                            </div>
                        }
                        
                        @if (migrationStatus.IsFullyMigrated)
                        {
                            <div class="alert alert-success">
                                <strong>完成：</strong>所有檔案已成功遷移到安全目錄。
                            </div>
                        }
                    }
                    
                    <button class="btn btn-primary me-2" @onclick="CheckMigrationStatus" disabled="@isLoading">
                        @if (isLoading)
                        {
                            <span class="spinner-border spinner-border-sm me-2"></span>
                        }
                        檢查遷移狀態
                    </button>
                    
                    <button class="btn btn-warning" @onclick="StartMigration" disabled="@(isLoading || isMigrating)">
                        @if (isMigrating)
                        {
                            <span class="spinner-border spinner-border-sm me-2"></span>
                        }
                        開始檔案遷移
                    </button>
                </div>
            </div>

            <!-- 檔案上傳配置 -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">檔案上傳配置</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        @foreach (var category in ConfigService.GetAvailableCategories())
                        {
                            var config = ConfigService.GetCategoryConfig(category);
                            <div class="col-md-4 mb-3">
                                <div class="card">
                                    <div class="card-body">
                                        <h6 class="card-title">@config?.Name</h6>
                                        <p class="card-text small">
                                            <strong>最大檔案大小：</strong>@ConfigService.GetFileSizeDisplay(category)<br>
                                            <strong>允許格式：</strong>@ConfigService.GetAllowedExtensionsDisplay(category)<br>
                                            <strong>最大檔案數：</strong>@ConfigService.GetMaxFilesPerUpload(category)
                                        </p>
                                    </div>
                                </div>
                            </div>
                        }
                    </div>
                </div>
            </div>

            <!-- 遷移結果 -->
            @if (migrationResult != null)
            {
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">遷移結果</h5>
                    </div>
                    <div class="card-body">
                        @if (migrationResult.IsSuccess)
                        {
                            <div class="alert alert-success">
                                <h6>遷移成功完成！</h6>
                                <p>總共遷移了 @migrationResult.TotalFilesMigrated 個檔案</p>
                                <ul>
                                    <li>報告檔案：@migrationResult.ReportFilesMigrated 個</li>
                                    <li>YCRS 檔案：@migrationResult.YCRSFilesMigrated 個</li>
                                </ul>
                            </div>
                        }
                        else
                        {
                            <div class="alert alert-danger">
                                <h6>遷移失敗</h6>
                                <p>@migrationResult.ErrorMessage</p>
                            </div>
                        }
                        
                        @if (migrationResult.ReportFilesErrors.Any() || migrationResult.YCRSFilesErrors.Any())
                        {
                            <div class="alert alert-warning">
                                <h6>遷移過程中的錯誤：</h6>
                                @foreach (var error in migrationResult.ReportFilesErrors.Concat(migrationResult.YCRSFilesErrors))
                                {
                                    <p class="mb-1">• @error</p>
                                }
                            </div>
                        }
                    </div>
                </div>
            }

            <!-- 安全指南 -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">檔案安全指南</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>安全措施</h6>
                            <ul>
                                <li>檔案儲存在非 Web 可存取的目錄中</li>
                                <li>嚴格的檔案類型驗證</li>
                                <li>檔案大小限制</li>
                                <li>檔案內容掃描</li>
                                <li>權限控制的下載機制</li>
                                <li>完整的稽核日誌</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6>建議操作</h6>
                            <ul>
                                <li>定期檢查遷移狀態</li>
                                <li>監控檔案上傳活動</li>
                                <li>審查稽核日誌</li>
                                <li>更新安全配置</li>
                                <li>清理過期檔案</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@code {
    private MigrationStatusResult? migrationStatus;
    private MigrationResult? migrationResult;
    private bool isLoading = false;
    private bool isMigrating = false;

    protected override async Task OnInitializedAsync()
    {
        await base.OnInitializedAsync();
        
        // 檢查管理員權限
        if (!_userState.IsAdmin)
        {
            return;
        }
        
        await CheckMigrationStatus();
    }

    private async Task CheckMigrationStatus()
    {
        isLoading = true;
        StateHasChanged();

        try
        {
            migrationStatus = await MigrationService.CheckMigrationStatusAsync();
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "檢查遷移狀態時發生錯誤");
            // 可以添加錯誤訊息顯示
        }
        finally
        {
            isLoading = false;
            StateHasChanged();
        }
    }

    private async Task StartMigration()
    {
        isMigrating = true;
        StateHasChanged();

        try
        {
            migrationResult = await MigrationService.MigrateAllFilesAsync();
            
            // 遷移完成後重新檢查狀態
            await CheckMigrationStatus();
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "檔案遷移時發生錯誤");
            migrationResult = new MigrationResult
            {
                IsSuccess = false,
                ErrorMessage = "檔案遷移過程中發生未預期的錯誤。"
            };
        }
        finally
        {
            isMigrating = false;
            StateHasChanged();
        }
    }
}
