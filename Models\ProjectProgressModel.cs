using System.ComponentModel.DataAnnotations;

namespace Intra2025.Models
{
    public class ProjectProgressModel
    {
        [Required(ErrorMessage = "主管機關為必填")]
        public string MainAgency { get; set; } = "";
        
        public bool IsPlanningDesign { get; set; }
        public bool IsEngineering { get; set; }
        public bool IsBoth { get; set; }
        public bool IsOther { get; set; }
        
        public string ProjectDuration { get; set; } = "";
        public decimal TotalBudget { get; set; }
        public decimal CurrentYearBudget { get; set; }
        
        public decimal YearPlannedProgress { get; set; }
        public decimal YearActualProgress { get; set; }
        public decimal TotalPlannedProgress { get; set; }
        public decimal TotalActualProgress { get; set; }
        
        public decimal PlannedExpenditure { get; set; }
        public decimal ActualExpenditure { get; set; }
        public decimal TotalPlannedExpenditure { get; set; }
        public decimal TotalActualExpenditure { get; set; }
        
        public string PreviousPeriodReason { get; set; } = "";
        public string CurrentPeriodReason { get; set; } = "";
        public string CountermeasureReason { get; set; } = "";
        public string? CreatedBy { get; set; }
    }
}
