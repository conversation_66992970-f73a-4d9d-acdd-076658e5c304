/*!*  filename: sf-treegrid.min.js 
*     version : 26.1.41 
*     Copyright Syncfusion Inc. 2001 - 2024. All rights reserved. 
*     Use of this code is subject to the terms of our license.
*     A copy of the current license can be obtained at any time by e-mailing 
*     <EMAIL>. Any infringement will be prosecuted under 
*     applicable laws. 
*/
(window.webpackJsonp=window.webpackJsonp||[]).push([[70],{"./bundles/sf-treegrid.js":function(e,t,r){"use strict";r.r(t);r("./modules/sf-treegrid.js")},"./modules/sf-treegrid.js":function(e,t){window.sfBlazor=window.sfBlazor||{},window.sfBlazor.TreeGrid=function(){"use strict";var e=function(){function e(e){this.treeCopyContent="",this.copiedUIdCollection=[],this.parent=e,this.gridClipboard=this.parent.grid.clipboardModule}return e.prototype.copy=function(e){document.queryCommandSupported("copy")&&(this.setCopyData(e),document.execCommand("copy"),this.gridClipboard.clipBoardTextArea.blur()),this.gridClipboard.isSelect&&(window.getSelection().removeAllRanges(),this.gridClipboard.isSelect=!1)},e.prototype.setCopyData=function(e){if(""===window.getSelection().toString()){this.gridClipboard.clipBoardTextArea.value=this.gridClipboard.copyContent="";var t=this.parent.grid.getRows();if("Cell"!==this.parent.grid.options.selectionMode){for(var r=this.parent.grid.getSelectedRowIndexes().sort((function(e,t){return e-t})),o=0;o<r.length;o++)if(o>0&&(this.treeCopyContent+="\n"),!t[r[o]].classList.contains("e-summaryrow")){var i=[].slice.call(t[r[o]].querySelectorAll(".e-rowcell")),s=t[r[o]].getAttribute("data-uid");-1===this.copiedUIdCollection.indexOf(s)&&("Parent"!==this.parent.options.copyHierarchyMode&&"Both"!==this.parent.options.copyHierarchyMode||this.parentContentData(r[o],t,e,o),this.gridClipboard.getCopyData(i,!1,"\t",e),this.treeCopyContent+=this.gridClipboard.copyContent,this.copiedUIdCollection.push(s),this.gridClipboard.copyContent="","Child"!==this.parent.options.copyHierarchyMode&&"Both"!==this.parent.options.copyHierarchyMode||this.childContentData(r[o],t,e))}if(e){var n=[];for(o=0;o<this.parent.grid.getVisibleColumns().length;o++)n[o]=this.parent.grid.getVisibleColumns()[o].headerText;this.gridClipboard.getCopyData(n,!1,"\t",e),this.treeCopyContent=this.gridClipboard.copyContent+"\n"+this.treeCopyContent}var a=document.activeElement;this.gridClipboard.clipBoardTextArea.value=this.gridClipboard.copyContent=this.treeCopyContent,sf.base.Browser.userAgent.match(/ipad|ipod|iphone/i)?this.gridClipboard.clipBoardTextArea.setSelectionRange(0,this.gridClipboard.clipBoardTextArea.value.length):(window.getSelection().removeAllRanges(),this.gridClipboard.clipBoardTextArea.select()),a.focus(),this.gridClipboard.isSelect=!0,this.copiedUIdCollection=[],this.treeCopyContent=""}}},e.prototype.parentContentData=function(e,t,r,o){for(var i,s=this.parent.options.allowRowDragAndDrop&&sf.base.isNullOrUndefined(this.parent.options.rowDropTargetID)?this.parent.options.treeColumnIndex+1:this.parent.options.treeColumnIndex,n=/index(\d+)|Level(\d+)/g,a=t[e].cells[s].className.match(n),d=parseInt(a[1].match(/\d+/)[0]),l=0;l<d;l++){var p=sf.base.isNullOrUndefined(i)?a[0].match(/\d+/)[0]:i.cells[s].className.match(n)[0].match(/\d+/)[0];i=t[p];var c=parseInt(i.cells[s].className.match(n)[1].match(/\d+/)[0]);if(!sf.base.isNullOrUndefined(i)&&i.rowIndex!=e)for(var g=c,h=i.getAttribute("data-uid"),u=0;u<g+1;u++)for(var f=0;f<t.length;f++){var m=t[f].getAttribute("data-uid");if(m===h){e=f;var w=[].slice.call(t[e].querySelectorAll(".e-rowcell"));if(-1===this.copiedUIdCollection.indexOf(m)){this.gridClipboard.getCopyData(w,!1,"\t",r),this.treeCopyContent=o>0?this.treeCopyContent+this.gridClipboard.copyContent+"\n":this.gridClipboard.copyContent+"\n"+this.treeCopyContent,this.copiedUIdCollection.push(m),this.gridClipboard.copyContent="";break}}}}},e.prototype.childContentData=function(e,t,r){var o=this.parent.options.allowRowDragAndDrop&&sf.base.isNullOrUndefined(this.parent.options.rowDropTargetID)?this.parent.options.treeColumnIndex+1:this.parent.options.treeColumnIndex,i=t[e],s=i.cells[o].className.match(/index(\d+)|Level(\d+)/g),n=i.rowIndex,a=+s[1].match(/\d+/)[0]+1,d=t.filter((function(e){return e.cells[o].classList.contains("e-gridrowindex"+n+"Level"+a)}));if(d.length)for(var l=0;l<d.length;l++)for(var p=0;p<t.length;p++)if(!sf.base.isNullOrUndefined(d[l].getAttribute("data-uid"))&&t[p].getAttribute("data-uid")===d[l].getAttribute("data-uid")){if(!sf.base.isNullOrUndefined(t[p])&&!t[p].classList.contains("e-summaryrow")){var c=[].slice.call(t[p].querySelectorAll(".e-rowcell")),g=t[p].getAttribute("data-uid");-1===this.copiedUIdCollection.indexOf(g)&&(this.gridClipboard.getCopyData(c,!1,"\t",r),this.treeCopyContent+="\n"+this.gridClipboard.copyContent,this.gridClipboard.copyContent="",this.copiedUIdCollection.push(g),this.childContentData(p,t,r))}break}},e}();function t(e,t,r){for(var o=e;o&&!(r?o.id===t:o.classList.contains(t));)o=o.parentElement;return o}var r=function(){function e(e){var r=this;this.draggedRows=[],this.selectedRows=[],this.canDrop=!0,this.treegridRows=[],this.rowDragStart=function(e){r.parent.options.hasDetailTemplate&&(r.treegridRows=r.parent.grid.getRows().filter((function(e){return e.classList.contains("e-disable-gridhover")}))),r.startedRow=r.parent.grid.rowDragAndDropModule.startedRow,r.parent.options.hasDetailTemplate&&(r.startedRow.dataset.rowindex=r.treegridRows.indexOf(r.treegridRows.filter((function(e){return e.getAttribute("data-uid")===r.startedRow.getAttribute("data-uid")}))[0]).toString());var t=r.parent.grid.getSelectedRowIndexes();if(r.parent.options.hasDetailTemplate&&r.parent.grid.getSelectedRows().length){t=[];for(var o=0;o<r.parent.grid.getSelectedRows().length;o++)t.push(r.treegridRows.indexOf(r.parent.grid.getSelectedRows()[o]))}r.parent.options.enableVirtualization?r.parent.grid.getSelectedRowIndexes().length?(t=r.parent.grid.getSelectedRowIndexes(),r.selectedRows=r.parent.grid.getSelectedRows()):(t.push(sf.base.closest(e.target,"tr").rowIndex),r.startedRow.dataset.rowindex=(sf.base.closest(e.target,"tr").rowIndex-r.parent.options.pageSize).toString(),r.selectedRows.push(sf.base.closest(e.target,"tr"))):r.selectedRows=r.parent.grid.getSelectedRows(),r.gridDragStart.apply(r.parent.grid,[e]);var i=parseInt(r.startedRow.getAttribute("data-rowindex"),10);r.parent.options.hasDetailTemplate&&(i=r.treegridRows.indexOf(r.treegridRows.filter((function(e){return e.getAttribute("data-uid")===r.startedRow.getAttribute("data-uid")}))[0])),0!==r.selectedRows.length&&0!==t.length||(r.selectedRows.push(r.startedRow),t.push(i));for(o=0;o<t.length;o++)r.parent.options.hasDetailTemplate?r.draggedRows.push(r.treegridRows[t[o]]):r.draggedRows.push(r.parent.grid.getRows()[t[o]]);if(r.parent.options.enableVirtualization){var s=parseInt(r.startedRow.getAttribute("data-rowindex"),10);r.parent.dotNetRef.invokeMethodAsync("DragRows",s)}},this.rowDraging=function(e){r.parent.options.hasDetailTemplate&&(r.startedRow.dataset.rowindex=r.treegridRows.indexOf(r.treegridRows.filter((function(e){return e.getAttribute("data-uid")===r.startedRow.getAttribute("data-uid")}))[0]).toString());var o=r.parent.grid.element.querySelector(".e-dropitemscount");r.parent.grid.getSelectedRows().length>1&&o&&(o.style.left=r.parent.grid.element.querySelector(".e-cloneproperties table").offsetWidth-5+"px"),r.gridDrag.apply(r.parent.grid,[e]),r.dragTarget=r.parent.grid.rowDragAndDropModule.dragTarget;var i=r.parent,s=r.parent.element.querySelector(".e-cloneproperties");s.style.cursor="";var n=e.target?sf.base.closest(e.target,"tr"):null,a=-1;a=i.options.hasDetailTemplate?n?r.treegridRows.indexOf(n):-1:n?n.rowIndex:-1,!sf.base.isNullOrUndefined(r.startedRow)&&r.selectedRows.length&&1===r.selectedRows.length&&parseInt(r.startedRow.getAttribute("data-rowindex"),10)!==parseInt(r.selectedRows[0].getAttribute("data-rowindex"),10)&&(r.draggedRows=i.options.hasDetailTemplate?[r.treegridRows[parseInt(r.startedRow.getAttribute("data-rowindex"),10)]]:[n]);var d,l=i.grid.getRows()[a];(i.options.hasDetailTemplate&&(l=r.treegridRows[a],-1===a&&r.updateRowDrop()),r.removeErrorElem(),r.canDrop=!0,-1!==a?r.ensuredropPosition(r.draggedRows,l):(r.canDrop=!1,r.addErrorElem()),!i.options.rowDropTargetID&&r.canDrop&&i.rowDragAndDropModule.updateIcon(r.selectedRows,a,e),i.options.rowDropTargetID)&&((d=t(e.target,"e-treegrid"))&&d.id===r.parent.options.rowDropTargetID&&d.blazor_instance.rowDragAndDropModule.updateIcon(r.selectedRows,a,e));e.target&&sf.base.closest(e.target,"#"+i.options.rowDropTargetID)&&((d=t(e.target,"e-treegrid"))||(s.style.cursor="default"))},this.rowDropped=function(e){r.parent.options.hasDetailTemplate&&!sf.base.isNullOrUndefined(e.target.parentElement)&&(e.target.classList.contains("e-treecolumn-container")||e.target.parentElement.classList.contains("e-treecell")||e.target.classList.contains("e-treecell")||e.target.classList.contains("e-treegridexpand")||e.target.classList.contains("e-treegridcollapse")?r.dragTarget=r.treegridRows.indexOf(e.target.offsetParent.parentElement):r.dragTarget=r.treegridRows.indexOf(e.target.parentElement.classList.contains("e-rowdragdrop")?e.target.parentElement.closest("tr"):e.target.parentElement.classList.contains("e-templatecell")?e.target.offsetParent.parentElement:e.target.parentElement));var o=r.getElementFromPosition(e.helper,e.event),i=o&&!o.classList.contains("e-dlg-overlay")?o:e.target;r.parent.grid.element.classList.remove("e-rowdrag");var s=document.getElementById(r.parent.options.rowDropTargetID);if(r.parent.options.allowRowDragAndDrop&&r.parent.options.rowDropTargetID&&!t(i,"e-treegrid")){var n=i.classList.value,a=i.id,d=parseInt(r.startedRow.getAttribute("data-rowindex"),10);r.parent.options.hasDetailTemplate&&(d=r.treegridRows.indexOf(r.treegridRows.filter((function(e){return e.getAttribute("data-uid")===r.startedRow.getAttribute("data-uid")}))[0]));var l="topSegment"===r.dropPosition?"Above":"bottomSegment"===r.dropPosition?"Below":"middleSegment"===r.dropPosition?"Child":"Invalid";r.parent.dotNetRef.invokeMethodAsync("ReorderRows",d,0,"add",!1,n,a,null,!0,l,null)}if(r.parent.options.rowDropTargetID&&s&&s.blazor_instance&&s.blazor_instance.getContent().classList.remove("e-allowRowDrop"),!t(i,"e-gridcontent"))return r.dragTarget=null,sf.base.remove(e.helper),void r.updateRowDrop();var p=r.parent;p.options.rowDropTargetID?(e.target&&sf.base.closest(e.target,"#"+p.options.rowDropTargetID)||t(e.target,"e-treegrid")&&t(e.target,"e-treegrid").id===p.options.rowDropTargetID)&&sf.base.setValue("dropPosition",r.dropPosition,e):t(e.target,"e-content")&&(r.parent.element.querySelector(".e-errorelem")&&(r.dropPosition="Invalid"),sf.base.setValue("dropPosition",r.dropPosition,e),sf.base.isNullOrUndefined(p.getHeaderContent().querySelector(".e-firstrow-border"))||p.getHeaderContent().querySelector(".e-firstrow-border").remove()),r.updateRowDrop();var c=r.parent.grid.element.querySelector(".e-dragstartrow");if(sf.base.isNullOrUndefined(c)||c.classList.remove("e-dragstartrow"),r.parent.options.allowRowDragAndDrop&&!r.parent.options.rowDropTargetID){r.parent.grid.rowDragAndDropModule.stopTimer(),r.parent.grid.getContent().classList.remove("e-grid-relative"),r.parent.grid.rowDragAndDropModule.removeBorder(o);var g=r.dragTarget;r.parent.options.enableVirtualization&&(g-=r.parent.options.pageSize);var h=i.classList.value,u=i.id,f=parseInt(r.startedRow.getAttribute("data-rowindex"),10);r.parent.options.hasDetailTemplate&&(f=r.treegridRows.indexOf(r.treegridRows.filter((function(e){return e.getAttribute("data-uid")===r.startedRow.getAttribute("data-uid")}))[0]));var m="topSegment"===r.dropPosition?"Above":"bottomSegment"===r.dropPosition?"Below":"middleSegment"===r.dropPosition?"Child":"Invalid";"Invalid"!==m&&r.parent.rowDragAndDropModule.canDrop&&setTimeout((function(){r.parent.dotNetRef.invokeMethodAsync("ReorderRows",f,g,"delete",!0,h,u,null,!1,m,null)}),10),r.dragTarget=null,r.draggedRows=[]}},this.drop=function(e){r.treeGridDrop({target:e.target,droppedElement:e.droppedElement}),sf.base.remove(e.droppedElement)},this.parent=e,this.parent.options.allowRowDragAndDrop&&(this.gridDrag=this.parent.grid.rowDragAndDropModule.drag,this.gridDragStart=this.parent.grid.rowDragAndDropModule.dragStart,this.parent.grid.rowDragAndDropModule.draggable.drag=this.rowDraging,this.parent.grid.rowDragAndDropModule.draggable.dragStop=this.rowDropped,this.parent.grid.rowDragAndDropModule.draggable.dragStart=this.rowDragStart,this.parent.grid.rowDragAndDropModule.droppable.drop=this.drop)}return e.prototype.updateRowDrop=function(){this.removetopOrBottomBorder(),this.removeChildBorder(),sf.base.isNullOrUndefined(this.parent.element.getElementsByClassName("e-firstrow-border")[0])?sf.base.isNullOrUndefined(this.parent.element.getElementsByClassName("e-lastrow-border")[0])||this.parent.element.getElementsByClassName("e-lastrow-border")[0].remove():this.parent.element.getElementsByClassName("e-firstrow-border")[0].remove()},e.prototype.removeFirstrowBorder=function(e){var t="bottomSegment"===this.dropPosition;this.parent.element.getElementsByClassName("e-firstrow-border").length>0&&e&&(0!==e.rowIndex||t)&&this.parent.element.getElementsByClassName("e-firstrow-border")[0].remove()},e.prototype.removeLastrowBorder=function(e){var t=e&&(e.classList.contains("e-emptyrow")||e.classList.contains("e-columnheader")),r=e&&!t&&this.parent.getRows()[this.parent.getRows().length-1].getAttribute("data-uid")!==e.getAttribute("data-uid"),o=r||"topSegment"===this.dropPosition;this.parent.element.getElementsByClassName("e-lastrow-border").length>0&&e&&(r||o)&&this.parent.element.getElementsByClassName("e-lastrow-border")[0].remove()},e.prototype.updateIcon=function(e,r,o){var i=o.target?sf.base.closest(o.target,"tr"):null;this.dropPosition=void 0;var s=0;if(this.removeFirstrowBorder(i),this.removeLastrowBorder(i),this.parent.options.enableVirtualization)if(this.parent.grid.getSelectedRows().length)for(var n=0;n<this.parent.grid.getSelectedRows().length;n++)(sf.base.isNullOrUndefined(i)||i.getAttribute("data-uid")!==this.parent.grid.getSelectedRows()[n].getAttribute("data-uid"))&&t(o.target,"e-gridcontent")||(this.dropPosition="Invalid",this.addErrorElem());else(sf.base.isNullOrUndefined(i)||i.getAttribute("data-uid")!==this.startedRow.getAttribute("data-uid"))&&t(o.target,"e-gridcontent")||(this.dropPosition="Invalid",this.addErrorElem());else if((sf.base.isNullOrUndefined(i)||sf.base.isNullOrUndefined(this.startedRow)||i.getAttribute("data-uid")!==this.startedRow.getAttribute("data-uid"))&&t(o.target,"e-gridcontent")||(this.dropPosition="Invalid",this.addErrorElem()),this.parent.grid.getSelectedRows().length&&this.parent.grid.getSelectedRows().length>1)for(n=0;n<this.selectedRows.length;n++)(sf.base.isNullOrUndefined(i)||i.getAttribute("data-uid")!==this.selectedRows[n].getAttribute("data-uid"))&&t(o.target,"e-gridcontent")||(this.dropPosition="Invalid",this.addErrorElem());var a=this.parent,d=0,l=this.parent.element.querySelector(".e-toolbar"),p=l?l.offsetHeight:0,c=this.getOffset(a.element),g=a.getHeaderContent().offsetHeight+c.top+p,h=a.grid.scrollModule.content.scrollTop;sf.base.isNullOrUndefined(i)||(s=i.offsetTop-h),d=a.options.allowTextWrap?e[0].offsetHeight:a.options.enableVirtualization?i.getBoundingClientRect().top:s+g+0;var u=((0!==e[0].offsetHeight&&sf.base.isNullOrUndefined(i)?d+e[0].offsetHeight:d+i.offsetHeight)-d)/3,f=d+u,m=f+u,w=m+u,b=(c.left,o.event),v=sf.base.isNullOrUndefined(b.pageY)?o.event.changedTouches[0].clientY:b.pageY,y=v<=f,C=v>f&&v<=m,R=v>m&&v<=w,D=!0;if(y||C||R){if(y&&"Invalid"!==this.dropPosition&&(this.removeChildBorder(),this.dropPosition="topSegment",this.removetopOrBottomBorder(),this.addFirstrowBorder(i),this.removeErrorElem(),this.removeLastrowBorder(i),D=this.updateBorderStatus(this.startedRow,r,this.draggedRows,this.treegridRows[r]),this.topOrBottomBorder(o.target,D)),C&&"Invalid"!==this.dropPosition){this.removetopOrBottomBorder();var S,A=sf.base.closest(o.target,"tr");(S=sf.base.isNullOrUndefined(A)?[]:[].slice.call(A.querySelectorAll(".e-rowcell,.e-rowdragdrop,.e-detailrowcollapse"))).length>0&&this.addRemoveClasses(S,!0,"e-childborder"),this.addLastRowborder(i),this.addFirstrowBorder(i),this.dropPosition="middleSegment"}R&&"Invalid"!==this.dropPosition&&(this.removeErrorElem(),this.removetopOrBottomBorder(),this.removeChildBorder(),this.dropPosition="bottomSegment",this.addLastRowborder(i),this.removeFirstrowBorder(i),D=this.updateBorderStatus(this.startedRow,r,this.draggedRows,this.treegridRows[r]),this.topOrBottomBorder(o.target,D))}return this.dropPosition},e.prototype.updateBorderStatus=function(e,t,r,o){var i=!0,s=this.parent.grid.getRows(),n=[];this.parent.options.hasDetailTemplate&&(s=this.parent.rowDragAndDropModule.treegridRows);var a=this.parent.options.hasDetailTemplate,d=this.parent.options.treeColumnIndex,l=this.parent.options.allowRowDragAndDrop?a?d+2:d+1:a?d+1:d,p=this.draggedRows,c=[s[t]];return"topSegment"==this.dropPosition&&(this.draggedRows.filter((function(r){if(sf.base.isNullOrUndefined(r)||sf.base.isNullOrUndefined(r.cells)||sf.base.isNullOrUndefined(c[0])||sf.base.isNullOrUndefined(c[0].cells))return!0;var o=/index(\d+)|Level(\d+)/g,a=null==r?void 0:r.cells[l].className.match(o),d=c[0].cells[l].className.match(o);if(void 0!==a){var g=+a[1].match(/\d+/)[0],h=+d[1].match(/\d+/)[0],u=!1;if(0!==g&&g!==h)return!0;for(var f=0;f<s.length;f++)if(s[f]==p[0]&&(u=!0),u&&s[f]!=p[0]){var m=+s[f].cells[l].className.match(o)[1].match(/\d+/)[0];if(!(m!=g&&m>g))break;n.push(s[f])}!sf.base.isNullOrUndefined(e)&&g===h&&(n.length>0&&parseInt(e.getAttribute("data-rowindex"),10)===t-(n.length+1)||0===n.length&&parseInt(e.getAttribute("data-rowindex"),10)===t-1)&&(i=!1)}})),i=!(!sf.base.isNullOrUndefined(e)&&0===n.length&&parseInt(e.getAttribute("data-rowindex"),10)===t-1&&sf.base.isNullOrUndefined(this.draggedRows[0]))&&i),"bottomSegment"==this.dropPosition&&(c.filter((function(r){if(sf.base.isNullOrUndefined(r)||sf.base.isNullOrUndefined(r.cells)||sf.base.isNullOrUndefined(p[0])||sf.base.isNullOrUndefined(p[0].cells))return!0;var o=/index(\d+)|Level(\d+)/g,a=null==r?void 0:r.cells[l].className.match(o),d=p[0].cells[l].className.match(o);if(void 0!==a){var g=+a[1].match(/\d+/)[0],h=+d[1].match(/\d+/)[0],u=!1;if(0!==g&&g!==h)return!0;for(var f=0;f<s.length;f++)if(s[f]==c[0]&&(u=!0),u&&s[f]!=c[0]){var m=+s[f].cells[l].className.match(o)[1].match(/\d+/)[0];if(!(m!=g&&m>g))break;n.push(s[f])}!sf.base.isNullOrUndefined(e)&&g===h&&(n.length>0&&parseInt(e.getAttribute("data-rowindex"),10)===t+(n.length+1)||0===n.length&&parseInt(e.getAttribute("data-rowindex"),10)===t+1)&&(i=!1)}})),i=!(!sf.base.isNullOrUndefined(e)&&0===n.length&&parseInt(e.getAttribute("data-rowindex"),10)===t+1&&sf.base.isNullOrUndefined(this.draggedRows[0]))&&i),this.parent.rowDragAndDropModule.canDrop=i,i},e.prototype.removeChildBorder=function(){var e;(e=[].slice.call(this.parent.element.querySelectorAll(".e-childborder"))).length>0&&this.addRemoveClasses(e,!1,"e-childborder")},e.prototype.addFirstrowBorder=function(e){var t=this.parent.element,r=this.parent;if(e&&0===e.rowIndex&&!e.classList.contains("e-emptyrow")){var o=sf.base.createElement("div",{className:"e-firstrow-border"}),i=this.parent.getHeaderContent(),s=0;r.options.toolbar&&(s=document.getElementById(r.element.id+"_gridcontrol_toolbarItems").offsetHeight);var n=!sf.base.isNullOrUndefined(this.parent.options.rowDropTargetID);n&&(o.style.top=this.parent.grid.element.getElementsByClassName("e-gridheader")[0].offsetHeight+s+"px"),o.style.width=n?t.offsetWidth+"px":t.offsetWidth-this.getScrollWidth()+"px",i.querySelectorAll(".e-firstrow-border").length||i.appendChild(o)}},e.prototype.addLastRowborder=function(e){var t=e&&(e.classList.contains("e-emptyrow")||e.classList.contains("e-columnheader"));if(e&&!t&&this.parent.getRows()[this.parent.getRows().length-1].getAttribute("data-uid")===e.getAttribute("data-uid")&&(!this.parent.options.enableVirtualization||this.parent.grid.rowDragAndDropModule.isOverflowBorder)){var r=sf.base.createElement("div",{className:"e-lastrow-border"}),o=this.parent.getContent();r.style.width=this.parent.element.offsetWidth-this.getScrollWidth()+"px",o.querySelectorAll(".e-lastrow-border").length||(o.classList.add("e-treegrid-relative"),o.appendChild(r),r.style.bottom=this.getScrollWidth()+"px")}},e.prototype.getScrollWidth=function(){var e=this.parent.getContent().firstElementChild;return e.scrollWidth>e.offsetWidth?this.parent.grid.Scroll&&this.parent.grid.Scroll.getScrollBarWidth():0},e.prototype.addErrorElem=function(){var e=document.getElementsByClassName("e-cloneproperties")[0];if(!e.querySelectorAll(".e-errorelem").length&&!this.parent.options.rowDropTargetID){var t=document.createElement("div");sf.base.classList(t,["e-errorcontainer"],[]),sf.base.classList(t,["e-icons","e-errorelem"],[]);var r=e.querySelector(".errorValue"),o=e.querySelector(".e-rowcell");r&&(o=r,r.parentNode.removeChild(r));var i=document.createElement("span");i.className="errorValue",i.style.paddingLeft="16px";var s=o.querySelector(".e-treecell");i.innerHTML=null==s?o.innerHTML:o.querySelector(".e-treecell").innerHTML,e.querySelector(".e-rowcell").innerHTML="",e.querySelector(".e-rowcell").appendChild(t),e.querySelector(".e-rowcell").appendChild(i)}},e.prototype.removeErrorElem=function(){var e=document.querySelector(".e-errorelem");e&&e.remove()},e.prototype.topOrBottomBorder=function(e,t){void 0===t&&(t=!0);var r,o=sf.base.closest(e,"tr");!(r=o?[].slice.call(o.querySelectorAll(".e-rowcell,.e-rowdragdrop,.e-detailrowcollapse")):[]).length||this.parent.options.enableVirtualization&&!this.parent.grid.rowDragAndDropModule.isOverflowBorder||("topSegment"===this.dropPosition&&(t?this.addRemoveClasses(r,!0,"e-droptop"):this.addRemoveClasses(r,!1,"e-dragborder"),this.parent.element.getElementsByClassName("e-lastrow-dragborder").length>0&&this.parent.element.getElementsByClassName("e-lastrow-dragborder")[0].remove()),"bottomSegment"===this.dropPosition&&(t?this.addRemoveClasses(r,!0,"e-dropbottom"):this.addRemoveClasses(r,!1,"e-dragborder")))},e.prototype.removetopOrBottomBorder=function(){var e;(e=[].slice.call(this.parent.element.querySelectorAll(".e-dropbottom, .e-droptop"))).length&&(this.addRemoveClasses(e,!1,"e-dropbottom"),this.addRemoveClasses(e,!1,"e-droptop"))},e.prototype.addRemoveClasses=function(e,t,r){for(var o=0,i=e.length;o<i;o++)t?e[o].classList.add(r):e[o].classList.remove(r)},e.prototype.getOffset=function(e){var t=e.getBoundingClientRect(),r=document.body,o=document.documentElement,i=window.pageYOffset||o.scrollTop||r.scrollTop,s=window.pageXOffset||o.scrollLeft||r.scrollLeft,n=o.clientTop||r.clientTop||0,a=o.clientLeft||r.clientLeft||0,d=t.top+i-n,l=t.left+s-a;return{top:Math.round(d),left:Math.round(l)}},e.prototype.treeGridDrop=function(e){var t,r,o=this.parent,i=sf.base.closest(e.target,"tr");if(!("true"!==e.droppedElement.querySelector("tr").getAttribute("single-dragrow")&&e.droppedElement.parentElement.parentElement.id===o.element.id||"true"===e.droppedElement.querySelector("tr").getAttribute("single-dragrow")&&e.droppedElement.parentElement.parentElement.id!==o.element.id)){e.droppedElement.parentElement.parentElement.id!==o.element.id&&(t=e.droppedElement.parentElement.parentElement.blazor_instance,sf.base.isNullOrUndefined(t)&&(t=e.droppedElement.parentElement.blazor__instance));var s=sf.base.isNullOrUndefined(t.options.rowDropTargetID)?t.options.rowDropTarget:t.options.rowDropTargetID;if((t.element.id===o.element.id||s===o.element.id)&&t.constructor.name===o.constructor.name){var n=this.getTargetIdx(i);isNaN(n)&&(n=0),o.options.allowPaging&&(n=n+o.options.currentPage*o.options.pageSize-o.options.pageSize);var a=t.content.querySelector(".e-dragstartrow");this.removetopOrBottomBorder(),this.removeChildBorder(),sf.base.isNullOrUndefined(a)||a.classList.remove("e-dragstartrow");var d=e.target.classList.value,l=e.target.id,p="topSegment"===this.dropPosition?"Above":"bottomSegment"===this.dropPosition?"Below":"middleSegment"===this.dropPosition?"Child":"Invalid";t.options.rowDropTargetID&&(r=parseInt(t.rowDragAndDropModule.startedRow.getAttribute("data-rowindex"),10)),o.dotNetRef.invokeMethodAsync("ReorderRows",0,n,"add",!1,d,l,t.dotNetRef,!1,p,r),t.dotNetRef.invokeMethodAsync("ReorderRows",0,n,"delete",!1,d,l,null,!1,p,r)}}},e.prototype.getTargetIdx=function(e){return e?parseInt(e.getAttribute("data-rowindex"),10):0},e.prototype.getElementFromPosition=function(e,t){var r=this.getPosition(t);e.style.display="none";var o=document.elementFromPoint(r.x,r.y);return e.style.display="",o},e.prototype.ensuredropPosition=function(e,t){var r=this,o=this.parent,i=o.grid.getRows();o.options.hasDetailTemplate&&(i=this.treegridRows);var s=this.parent.options.hasDetailTemplate,n=this.parent.options.treeColumnIndex,a=this.parent.options.allowRowDragAndDrop?s?n+2:n+1:s?n+1:n;e.filter((function(o){var s=/index(\d+)|Level(\d+)/g,n=null==o?void 0:o.cells[a].className.match(s);if(void 0!==n){for(var d=+n[1].match(/\d+/)[0],l=!1,p=new Array,c=0;c<i.length;c++)if(i[c]==e[0]&&(l=!0),l&&i[c]!=e[0]){var g=+i[c].cells[a].className.match(s)[1].match(/\d+/)[0];if(!(g!=d&&g>d))break;p.push(i[c])}if(p.length){if(-1!==p.map((function(e){return e.getAttribute("data-uid")})).indexOf(t.getAttribute("data-uid")))return r.dropPosition="Invalid",r.addErrorElem(),void(r.canDrop=!1);r.ensuredropPosition(p,t)}}}))},e.prototype.getPosition=function(e){var t={};return t.x=sf.base.isNullOrUndefined(e.clientX)?e.changedTouches[0].clientX:e.clientX,t.y=sf.base.isNullOrUndefined(e.clientY)?e.changedTouches[0].clientY:e.clientY,t},e}(),o=function(){function e(e,t){this.parent=t,this.dotNetRef=e}return e.prototype.renderHeaderCheckbox=function(e,t){var r=sf.base.createElement("div",{className:"e-checkbox-wrapper e-css e-hierarchycheckbox"}),o=sf.base.createElement("input",{className:"e-treeselectall",attrs:{type:"checkbox","aria-label":"Check all rows"}});r.appendChild(o);var i=sf.base.createElement("span",{className:"e-frame e-icons",styles:"width: 18px;"});r.appendChild(i),i.addEventListener("click",this.headerSelect.bind(this));var s=sf.base.createElement("span",{className:"e-label"});r.appendChild(s);var n=e.querySelectorAll(".e-headercontent th")[t];if(sf.base.isNullOrUndefined(n.querySelector(".e-checkbox-wrapper"))){var a=n.querySelector(".e-headercelldiv");a.insertBefore(r,a.firstChild)}},e.prototype.updateHeaderCheckbox=function(e,t){"intermediate"===t?(e.classList.add("e-stop"),e.classList.remove("e-check")):"check"===t?(e.classList.add("e-check"),e.classList.remove("e-stop")):(e.classList.remove("e-stop"),e.classList.remove("e-check"))},e.prototype.headerSelect=function(e){var t=e.currentTarget,r="uncheck";t.classList.contains("e-check")?t.classList.remove("e-check"):t.classList.contains("e-stop")?t.classList.remove("e-stop"):(t.classList.add("e-check"),r="check"),this.dotNetRef.invokeMethodAsync("HeaderSelectAll",r)},e}(),i=function(){function t(e,t,r,o){var i=this;if(this.columnModel=[],window.sfBlazor=window.sfBlazor,this.element=t,!sf.base.isNullOrUndefined(this.element))if(this.grid=t.querySelector("#"+t.id+"_gridcontrol").blazor__instance,this.dotNetRef=o,this.options=r,this.dataId=e,sf.base.isNullOrUndefined(this.element)||(this.element.blazor_instance=this,window.sfBlazor.setCompInstance(this)),this.header=this.element.querySelector(".e-headercontent"),this.content=this.element.querySelector(".e-content"),this.footer=this.element.querySelector(".e-summarycontent"),this.element.offsetWidth<=0){var s=setInterval((function(){i.element.offsetWidth>0&&(i.initModules(),clearInterval(s))}),500);sf.base.isNullOrUndefined(this.checkboxcolumnModule)&&this.initModules()}else this.initModules()}return t.prototype.initModules=function(){this.clipboardModule=new e(this),this.rowDragAndDropModule=new r(this),this.checkboxcolumnModule=new o(this.dotNetRef,this),this.wireEvents()},t.prototype.wireEvents=function(){sf.base.EventHandler.add(this.element,"keydown",this.KeyDownHandler,this)},t.prototype.getContent=function(){return this.content},t.prototype.getContentTable=function(){return this.content.querySelector(".e-table")},t.prototype.getHeaderContent=function(){return this.header},t.prototype.getHeaderTable=function(){return this.header.querySelector(".e-table")},t.prototype.getRows=function(){return[].slice.call(this.getContent().querySelectorAll(".e-row"))},t.prototype.KeyDownHandler=function(e){67===e.keyCode&&e.ctrlKey?this.clipboardModule.copy():72===e.keyCode&&e.ctrlKey&&e.shiftKey&&this.clipboardModule.copy(!0)},t.prototype.unWireEvents=function(){sf.base.EventHandler.remove(this.element,"keydown",this.KeyDownHandler)},t.prototype.destroy=function(){this.unWireEvents()},t}();return{initialize:function(e,t,r,o){sf.base.enableBlazorMode(),new i(e,t,r,o)},modelChanged:function(e,t){var r=window.sfBlazor.getCompInstance(e);if(!sf.base.isNullOrUndefined(r)&&(r.options=t,t.enableVirtualization&&t.isExpandCollapse)){r.grid.virtualContentModule.preventEvent=!0}},treeColumnIndexUpdate:function(e,t){window.sfBlazor.getCompInstance(e).options.treeColumnIndex=t},frozenHeight:function(e){var t=window.sfBlazor.getCompInstance(e);sf.base.isNullOrUndefined(t)||t.grid.freezeModule.refreshRowHeight()},headerCheckbox:function(e,t){var r=window.sfBlazor.getCompInstance(e);sf.base.isNullOrUndefined(r)||sf.base.isNullOrUndefined(r.element)||r.checkboxcolumnModule.renderHeaderCheckbox(r.element,t)},updateCheckbox:function(e,t,r){var o=window.sfBlazor.getCompInstance(e),i=o.element.querySelectorAll(".e-gridheader th")[t].querySelector("span.e-frame");o.checkboxcolumnModule.updateHeaderCheckbox(i,r)},copyToClipBoard:function(e,t){var r=window.sfBlazor.getCompInstance(e);"Cell"===r.grid.options.selectionMode?r.grid.clipboardModule.copy(t):r.clipboardModule.copy(t)},paste:function(e,t,r,o){window.sfBlazor.getCompInstance(e).grid.clipboardModule.paste(t,r,o)},maskRowUpdate:function(e,t,r){var o=window.sfBlazor.getCompInstance(e);document.getElementsByClassName("e-masked-row e-row").length>0&&document.getElementsByClassName("e-virtualtrack")[1].clientHeight>0&&setTimeout((function(){var e=o.getContent().scrollTop-t*document.getElementsByClassName("e-masked-row e-row")[0].clientHeight;0===parseInt(document.getElementsByClassName("e-virtualtable")[1].style.transform.split(", ")[1].split(")")[0])&&(document.getElementsByClassName("e-virtualtable")[1].style.transform="translate(0px, "+e+"px)")}),501)},destroy:function(e){window.sfBlazor.getCompInstance(e).destroy()}}}()}}]);(async()=>{await import(`${document.baseURI}_content/Syncfusion.Blazor/scripts/syncfusion-blazor-base.min.js?v=26.1.undefined`).then(()=>{sfBlazor.loadDependencies('sftreegrid');})})();