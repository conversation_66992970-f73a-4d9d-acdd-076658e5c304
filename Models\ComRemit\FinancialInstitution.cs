using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Intra2025.Models.ComRemit
{
    [Table("FinancialInstitution")]
    public class FinancialInstitution
    {
        [Key]
        public int Id { get; set; }
        
        [Required]
        [Display(Name = "金融機關名稱")]
        public string Name { get; set; } = string.Empty;
        
        [Required]
        [Display(Name = "金融機關代號")]
        public string Code { get; set; } = string.Empty;
        
        [Display(Name = "金融機關住址")]
        public string? Address { get; set; }
        
        [Display(Name = "金融機關類別")]
        public string Category { get; set; } = "國內銀行";
        
        public DateTime? CreateDate { get; set; }
        
        public string? CreateUser { get; set; }
        
        public DateTime? UpdateDate { get; set; }
        
        public string? UpdateUser { get; set; }
    }
}
