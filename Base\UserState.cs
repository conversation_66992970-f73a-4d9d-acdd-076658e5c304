﻿namespace Intra2025.Components.Base
{
    public class UserState
    {
        // 狀態變化事件
        public event Action? OnStateChanged;
        /// <summary>
        /// 使用者帳號 (例如: stan1217)。
        /// </summary>
        public string Account { get; set; } = string.Empty;

        /// <summary>
        /// 使用者姓名 (例如: 郭○○)。
        /// </summary>
        public string UserName { get; set; } = string.Empty;

        /// <summary>
        /// 機關代碼 (例如: 117)。
        /// </summary>
        public string UnitCode { get; set; } = string.Empty;

        /// <summary>
        /// 機關名稱 (例如: 計畫處)。
        /// </summary>
        public string UnitName { get; set; } = string.Empty;

        /// <summary>
        /// 單位代碼 (例如: 113007)。
        /// </summary>
        public string DepCode { get; set; } = string.Empty;

        /// <summary>
        /// 單位名稱 (例如: 資訊管理科)。
        /// </summary>
        public string DepName { get; set; } = string.Empty;

        /// <summary>
        /// 職稱 (例如: 約聘管理師)。
        /// </summary>
        public string Title { get; set; } = string.Empty;

        /// <summary>
        /// 是否為管理者
        /// </summary>
        public bool IsAdmin { get; set; }

        /// <summary>
        /// 是否為管理者
        /// </summary>
        public bool IsInitialized { get; set; }

        /// <summary>
        /// 聯絡電話（辦公室電話）
        /// </summary>
        public string OfficePhone { get; set; } = string.Empty;

        /// <summary>
        /// 傳真號碼
        /// </summary>
        public string Fax { get; set; } = string.Empty;

        public void Initialize(
            string account, string userName, string unitCode, string unitName, string depCode, string depName, string title, bool isAdmin,
            string officePhone = "", string fax = "")
        {
            Account = account;
            UserName = userName;
            UnitCode = unitCode;
            UnitName = unitName;
            DepCode = depCode;
            DepName = depName;
            Title = title;
            IsAdmin = isAdmin;
            OfficePhone = officePhone;
            Fax = fax;

            // 觸發狀態變化事件，通知所有監聽的組件更新
            OnStateChanged?.Invoke();
        }
    }

}
