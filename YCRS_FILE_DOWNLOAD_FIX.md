# YCRS 檔案下載問題修復報告

## 🔍 問題診斷

### 發現的問題
1. **路徑映射不一致**：`GetActualFilePath` 方法中的路徑轉換邏輯有問題
2. **日誌記錄不足**：缺乏詳細的調試日誌來追蹤下載流程
3. **錯誤處理不完整**：下載端點的錯誤分類和處理不夠精確

### 檔案路徑結構分析
- **實際檔案位置**：`\App_Data\SecureUploads\YCRS_Care\檔案名稱`
- **資料庫儲存格式**：`/secure-uploads/YCRS_Care/檔案名稱`
- **下載 URL**：`/api/secure-download?filePath=/secure-uploads/YCRS_Care/檔案名稱`

## 🔧 修復內容

### 1. 修正路徑映射邏輯
**檔案**：`Services/SecureFileDownloadService.cs`

**問題**：`GetActualFilePath` 方法中的路徑處理邏輯不正確

**修復**：
```csharp
private string GetActualFilePath(string relativePath)
{
    _logger.LogDebug("Processing file path: {RelativePath}", relativePath);
    
    var cleanPath = relativePath.TrimStart('/');

    if (cleanPath.StartsWith("secure-uploads/"))
    {
        // 正確處理：/secure-uploads/YCRS_Care/filename.pdf
        var secureUploadPath = Path.Combine(_environment.ContentRootPath, "App_Data", "SecureUploads");
        var subPath = cleanPath.Substring("secure-uploads/".Length); // YCRS_Care/filename.pdf
        var actualPath = Path.Combine(secureUploadPath, subPath);
        
        _logger.LogDebug("Mapped secure-uploads path: {CleanPath} -> {ActualPath}", cleanPath, actualPath);
        return actualPath;
    }
    
    // 預設處理
    var defaultPath = Path.Combine(_environment.ContentRootPath, "App_Data", "SecureUploads", cleanPath);
    _logger.LogDebug("Using default path mapping: {CleanPath} -> {DefaultPath}", cleanPath, defaultPath);
    return defaultPath;
}
```

### 2. 增強下載端點日誌記錄
**檔案**：`Program.cs`

**修復**：
- 添加詳細的請求日誌
- 改善錯誤分類和狀態碼處理
- 修正檔案名稱編碼問題

```csharp
app.MapGet("/api/secure-download", async (HttpContext context, SecureFileDownloadService downloadService, UserState userState, ILogger<Program> logger) =>
{
    var filePath = context.Request.Query["filePath"].ToString();
    logger.LogInformation("Secure download request: {FilePath} by user {UserId}", filePath, userState.Account);

    // URL 解碼檔案路徑
    var decodedFilePath = System.Web.HttpUtility.UrlDecode(filePath);
    logger.LogDebug("Decoded file path: {DecodedFilePath}", decodedFilePath);

    // ... 其他邏輯 ...

    if (!result.IsSuccess)
    {
        var statusCode = result.ErrorMessage.Contains("權限") ? 403 : 
                        result.ErrorMessage.Contains("不存在") ? 404 : 400;
        
        logger.LogWarning("Download failed: {ErrorMessage} for file {FilePath} by user {UserId}", 
            result.ErrorMessage, decodedFilePath, userState.Account);
        
        context.Response.StatusCode = statusCode;
        await context.Response.WriteAsync(result.ErrorMessage);
        return;
    }

    logger.LogInformation("Download successful: {FileName} for user {UserId}", result.FileName, userState.Account);

    // 設定安全的回應標頭（修正檔案名稱編碼）
    context.Response.Headers.Append("Content-Disposition", $"attachment; filename=\"{Uri.EscapeDataString(result.FileName)}\"");
    // ... 其他標頭 ...
});
```

### 3. 創建診斷工具
**檔案**：`Components/Pages/Admin/FileDownloadDiagnostic.razor`

**功能**：
- 檢查資料庫中的檔案記錄與實際檔案系統的對應關係
- 識別路徑格式問題
- 提供詳細的診斷報告

**使用方式**：
1. 以管理員身份登入
2. 訪問 `/Admin/FileDownloadDiagnostic`
3. 點擊「執行診斷」按鈕
4. 查看診斷結果和建議

## 🧪 測試步驟

### 1. 基本功能測試
1. 登入 YCRS 系統
2. 進入溫馨關懷表列表頁面
3. 點擊任一檔案的下載連結
4. 確認檔案能正常下載

### 2. 權限測試
1. 以不同權限的使用者登入
2. 嘗試下載不同單位的檔案
3. 確認權限控制正常運作

### 3. 錯誤處理測試
1. 嘗試下載不存在的檔案
2. 使用無效的檔案路徑
3. 確認錯誤訊息正確顯示

### 4. 診斷工具測試
1. 以管理員身份訪問診斷頁面
2. 執行診斷並檢查結果
3. 確認所有檔案狀態正確

## 🔍 調試指南

### 檢查瀏覽器開發者工具
1. **Network 標籤**：
   - 檢查下載請求的狀態碼
   - 查看請求和回應標頭
   - 確認檔案路徑參數正確編碼

2. **Console 標籤**：
   - 查看 JavaScript 錯誤
   - 檢查下載連結的生成

### 檢查伺服器日誌
啟用詳細日誌記錄：
```json
{
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Intra2025.Services.SecureFileDownloadService": "Debug",
      "Microsoft.AspNetCore": "Warning"
    }
  }
}
```

### 常見錯誤和解決方案

#### 1. 404 檔案不存在
**可能原因**：
- 檔案路徑映射錯誤
- 檔案實際不存在於檔案系統中
- 資料庫記錄與實際檔案不同步

**解決方案**：
- 使用診斷工具檢查檔案狀態
- 確認檔案是否存在於 `\App_Data\SecureUploads\YCRS_Care\` 目錄
- 檢查資料庫中的 `Filepath` 欄位格式

#### 2. 403 權限被拒絕
**可能原因**：
- 使用者沒有權限存取該檔案
- 檔案不屬於使用者的單位
- 權限驗證邏輯錯誤

**解決方案**：
- 確認使用者的 `UnitCode` 與檔案記錄的 `CaseBelongId` 匹配
- 檢查管理員權限設定
- 查看權限驗證日誌

#### 3. 400 請求錯誤
**可能原因**：
- 檔案路徑參數遺失或格式錯誤
- URL 編碼問題
- 路徑包含不安全字元

**解決方案**：
- 檢查下載連結的生成邏輯
- 確認 URL 編碼正確
- 查看路徑遍歷檢查日誌

## 📋 後續改進建議

1. **效能優化**：
   - 實作檔案快取機制
   - 添加檔案下載統計

2. **安全增強**：
   - 添加下載頻率限制
   - 實作檔案存取稽核

3. **使用者體驗**：
   - 添加下載進度指示
   - 改善錯誤訊息顯示

4. **監控和維護**：
   - 定期執行檔案完整性檢查
   - 自動清理孤立檔案

## 📁 相關檔案

### 修改的檔案
- `Services/SecureFileDownloadService.cs`
- `Program.cs`

### 新增的檔案
- `Components/Pages/Admin/FileDownloadDiagnostic.razor`
- `YCRS_FILE_DOWNLOAD_FIX.md`

### 需要檢查的檔案
- `Components/Pages/YCRS/CareCaseList.razor`
- `Components/Pages/YCRS/CareCaseNewEdit.razor`
- `Models/YouthCareReportService/YCRS_Files.cs`

修復已完成，建議立即進行測試以確認問題已解決。
