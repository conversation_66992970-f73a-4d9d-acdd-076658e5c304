using System.Text;
using DocumentFormat.OpenXml;
using DocumentFormat.OpenXml.Packaging;
using DocumentFormat.OpenXml.Presentation;
using Intra2025.Models;
using Intra2025.Data;
using Intra2025.Components.Base;
using Microsoft.EntityFrameworkCore;

namespace Intra2025.Services
{
    public class ProjectProgressPresentationService
    {
        private readonly IWebHostEnvironment _env;
        private readonly ILogger<ProjectProgressPresentationService> _logger;
        private readonly AppDbContext _context;
        private readonly UserState _userState;

        public ProjectProgressPresentationService(IWebHostEnvironment env, ILogger<ProjectProgressPresentationService> logger, AppDbContext context, UserState userState)
        {
            _env = env;
            _logger = logger;
            _context = context;
            _userState = userState;
        }

        public async Task<ProjectProgressModel?> GetProjectProgressByIdAsync(int id)
        {
            var model = await _context.ProjectProgressModels.FindAsync(id);

            if (model == null)
            {
                return null; // 找不到資料
            }

            // 權限檢查：只有管理員或資料建立者可以存取
            if (!_userState.IsAdmin && model.CreatedBy != _userState.Account)
            {
                return null; // 無權限
            }

            return model;
        }

        public async Task<string> GeneratePresentationAsync(ProjectProgressModel model)
        {
            try
            {
                var fileName = $"計畫進度報告_{model.MainAgency}_{DateTime.Now:yyyyMMdd_HHmmss}.odp";
                var tempPath = Path.Combine(_env.WebRootPath, "temp", fileName);
                
                // 確保目錄存在
                Directory.CreateDirectory(Path.GetDirectoryName(tempPath)!);

                // 生成 ODP 檔案
                await CreateOdpFileAsync(model, tempPath);

                return fileName;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "生成簡報檔案時發生錯誤");
                throw;
            }
        }

        private async Task CreateOdpFileAsync(ProjectProgressModel model, string filePath)
        {
            // 使用範本方式生成 ODP
            var templatePath = Path.Combine(_env.WebRootPath, "templates", "project_progress_template.odp");
            
            if (File.Exists(templatePath))
            {
                // 複製範本檔案
                File.Copy(templatePath, filePath, true);
                
                // 替換內容（使用 LibreOffice 或其他方式）
                await ReplaceContentInOdpAsync(filePath, model);
            }
            else
            {
                // 如果沒有範本，生成基本的 ODP 結構
                await CreateBasicOdpAsync(filePath, model);
            }
        }

        private async Task ReplaceContentInOdpAsync(string filePath, ProjectProgressModel model)
        {
            // 使用 LibreOffice 命令行進行內容替換
            var replacements = PrepareReplacements(model);
            
            // 創建臨時的替換腳本
            var scriptPath = await CreateReplacementScriptAsync(replacements);
            
            try
            {
                // 執行 LibreOffice 替換
                await ExecuteLibreOfficeReplacementAsync(filePath, scriptPath);
            }
            finally
            {
                // 清理臨時腳本
                if (File.Exists(scriptPath))
                    File.Delete(scriptPath);
            }
        }

        private static Dictionary<string, string> PrepareReplacements(ProjectProgressModel model)
        {
            return new Dictionary<string, string>
            {
                {"[主管機關]", model.MainAgency},
                {"[規劃設計]", model.IsPlanningDesign ? "✓" : ""},
                {"[工程]", model.IsEngineering ? "✓" : ""},
                {"[一者均有]", model.IsBoth ? "✓" : ""},
                {"[其他]", model.IsOther ? "✓" : ""},
                {"[總計畫期程]", model.ProjectDuration},
                {"[總經費]", FormatCurrency(model.TotalBudget)},
                {"[本年預算]", FormatCurrency(model.CurrentYearBudget)},
                {"[年累計預定]", $"{model.YearPlannedProgress}%"},
                {"[年累計實際]", $"{model.YearActualProgress}%"},
                {"[年累計比較]", $"{model.YearActualProgress - model.YearPlannedProgress}%"},
                {"[總累計預定]", $"{model.TotalPlannedProgress}%"},
                {"[總累計實際]", $"{model.TotalActualProgress}%"},
                {"[總累計比較]", $"{model.TotalActualProgress - model.TotalPlannedProgress}%"},
                {"[年累計預定支用]", FormatCurrency(model.PlannedExpenditure)},
                {"[年累計實際執行]", FormatCurrency(model.ActualExpenditure)},
                {"[年累計執行率]", FormatExecutionRate(model.PlannedExpenditure, model.ActualExpenditure)},
                {"[總累計預定支用]", FormatCurrency(model.TotalPlannedExpenditure)},
                {"[總累計實際執行]", FormatCurrency(model.TotalActualExpenditure)},
                {"[總累計執行率]", FormatExecutionRate(model.TotalPlannedExpenditure, model.TotalActualExpenditure)},
                {"[上期原因]", model.PreviousPeriodReason},
                {"[本期原因]", model.CurrentPeriodReason},
                {"[因應對策]", model.CountermeasureReason}
            };
        }

        private async Task<string> CreateReplacementScriptAsync(Dictionary<string, string> replacements)
        {
            var scriptPath = Path.Combine(_env.WebRootPath, "temp", $"replace_script_{Guid.NewGuid()}.txt");
            var scriptContent = new StringBuilder();

            foreach (var replacement in replacements)
            {
                scriptContent.AppendLine($"{replacement.Key}|{replacement.Value}");
            }

            await File.WriteAllTextAsync(scriptPath, scriptContent.ToString());
            return scriptPath;
        }

        private static async Task ExecuteLibreOfficeReplacementAsync(string filePath, string scriptPath)
        {
            // 這裡可以使用 LibreOffice 的宏或命令行工具進行替換
            // 或者使用更簡單的方式：直接操作 ODP 檔案內容

            // 暫時使用參數避免警告
            _ = filePath;
            _ = scriptPath;

            await Task.CompletedTask; // 佔位符
        }

        private static async Task CreateBasicOdpAsync(string filePath, ProjectProgressModel model)
        {
            // 創建基本的 ODP 檔案結構
            // 這裡可以使用 OpenXML 或其他方式創建基本的簡報檔案

            // 暫時使用參數避免警告
            _ = filePath;
            _ = model;

            await Task.CompletedTask; // 佔位符
        }

        private static string FormatCurrency(decimal amount)
        {
            return amount == 0 ? "" : $"{amount:N0}";
        }

        private static string FormatExecutionRate(decimal planned, decimal actual)
        {
            if (planned <= 0) return "0%";
            var rate = Math.Round(actual / planned * 100, 1);
            return $"{rate}%";
        }
    }
}