/*!*  filename: sf-circulargauge.min.js 
*     version : 26.1.41 
*     Copyright Syncfusion Inc. 2001 - 2024. All rights reserved. 
*     Use of this code is subject to the terms of our license.
*     A copy of the current license can be obtained at any time by e-mailing 
*     <EMAIL>. Any infringement will be prosecuted under 
*     applicable laws. 
*/
(window.webpackJsonp=window.webpackJsonp||[]).push([[15],{"./bundles/sf-circulargauge.js":function(e,t,i){"use strict";i.r(t);i("./modules/sf-circulargauge.js")},"./modules/sf-circulargauge.js":function(e,t){window.sfBlazor=window.sfBlazor||{},window.sfBlazor.CircularGauge=function(){"use strict";var e=function(){function e(e,t,i,n){this.isResize=!1,this.isPropertyChange=!1,window.sfBlazor=window.sfBlazor,this.allowAxisCount=[],this.prevAnimatedMajorTickValue=[],this.prevAnimatedMajorTickIndex=[],this.prevAnimatedMinorTickValue=[],this.prevAnimatedMinorTickIndex=[],this.isPointerRenderCompleted=!1,this.loadingAnimationDuration=[],this.isTouch=!1,this.labelElementAnimation=function(e,t,i){var n=this;e&&!this.isResize&&new sf.base.Animation({}).animate(e,{duration:t.IsAxisLabelVisible?this.loadingAnimationDuration[i]/t.AxisLabelCount/this.allowAxisCount[i]:0,progress:function(){e.style.visibility="visible"},end:function(){var o=document.getElementById(n.element.id+"_Axis_Labels_"+i);t.ShowLastLabel&&parseInt(e.id.split("Label_")[1])===o.childElementCount-2&&(e=document.getElementById(n.element.id+"_Axis_"+i+"_Label_"+(o.childElementCount-1)))&&(e.style.visibility="visible")}})},this.id=e,this.element=t,this.dotNetRef=n,this.options=i,this.dataId=e,window.sfBlazor.setCompInstance(this)}return e.prototype.render=function(){this.wireEvents()},e.prototype.wireEvents=function(){
/*! Bind the Event handler */
sf.base.EventHandler.add(this.element,sf.base.Browser.touchStartEvent,this.gaugeOnMouseDown,this),sf.base.EventHandler.add(this.element,sf.base.Browser.touchMoveEvent,this.gaugeOnMouseMove,this),sf.base.EventHandler.add(this.element,sf.base.Browser.touchEndEvent,this.gaugeOnMouseEnd,this),sf.base.EventHandler.add(this.element,sf.base.Browser.touchCancelEvent,this.gaugeOnMouseEnd,this),sf.base.EventHandler.add(this.element,"click",this.gaugeOnMouseClick,this),window.addEventListener(sf.base.Browser.isTouch&&"orientation"in window&&"onorientationchange"in window?"orientationchange":"resize",this.gaugeOnResize.bind(this))},e.prototype.destroy=function(){this.dotNetRef=null},e.prototype.gaugeOnMouseClick=function(e){var t=["_Text_","_Shape_"],i=e.target.id;if(-1!==i.indexOf("Legend")&&this.options.legendToggleVisibility)for(var n=0;n<t.length;n++){var o=t[n];if(-1!==i.indexOf(o)){var s=parseInt(i.split(this.element.id+"_Legend_Axis_")[1].split(o)[0]),a=parseInt(i.split(this.element.id+"_Legend_Axis_")[1].split(o)[1]);sf.base.isNullOrUndefined(this.dotNetRef)||this.dotNetRef.invokeMethodAsync("TriggerLegendClick",s,a)}}},e.prototype.gaugeOnResize=function(){var e,t;if(!sf.base.isNullOrUndefined(this.element)){var i=document.getElementById(this.element.id+"_svg");if(!sf.base.isNullOrUndefined(i)){i.style.display="none";var n=this.element.getBoundingClientRect();e=n.width,t=n.height,i.style.removeProperty("display")}}this.previousHeight===t&&this.previousWidth===e||(this.previousHeight=t,this.previousWidth=e,this.isResize=!0,this.isPropertyChange=!1,sf.base.isNullOrUndefined(this.dotNetRef)||this.dotNetRef.invokeMethodAsync("TriggerResizeEvent",e,t))},e.prototype.gaugeOnMouseDown=function(e){var t=document.getElementById(this.element.id+"_legend_pagenumber"),i=e.target.id,n=0,o=0;if("touchstart"===e.type?(this.isTouch=!0,n=e.touches[0].clientX,o=e.touches[0].clientY):(this.isTouch=!1,n=e.pageX,o=e.pageY),sf.base.isNullOrUndefined(this.dotNetRef)||this.dotNetRef.invokeMethodAsync("TriggerMouseDownEvent",n,o),!sf.base.isNullOrUndefined(t)){var s=parseInt(t.textContent.split("/")[0],10);i.indexOf(this.element.id+"_legend_pageup")>-1&&!sf.base.isNullOrUndefined(this.dotNetRef)?this.dotNetRef.invokeMethodAsync("TriggerLegendPageClick",s-2,s-1):i.indexOf(this.element.id+"_legend_pagedown")>-1&&!sf.base.isNullOrUndefined(this.dotNetRef)&&this.dotNetRef.invokeMethodAsync("TriggerLegendPageClick",s,s+1)}if((this.options.enablePointerDrag||this.options.enableRangeDrag)&&(-1!==event.target.id.indexOf("Pointer")||-1!==event.target.id.indexOf("_Range_"))){e.preventDefault(),this.isMouseDown=!0;var a=i.replace(this.element.id,"").split("_Axis_")[1];this.dragAxisIndex=+a[0],this.dragElementIndex=+a[a.length-1],sf.base.isNullOrUndefined(this.dotNetRef)||(-1!==event.target.id.indexOf("Pointer")?(this.isPointerDrag=!0,this.dotNetRef.invokeMethodAsync("TriggerDragStart",this.dragAxisIndex,this.dragElementIndex,0,"Pointer")):(this.isRangeDrag=!0,this.dotNetRef.invokeMethodAsync("TriggerDragStart",this.dragAxisIndex,0,this.dragElementIndex,"Range")))}},e.prototype.gaugeOnMouseMove=function(e){var t,i,n,o,s,a=e.target.id;"touchmove"===e.type?(o=e.touches[0].clientX,s=e.touches[0].clientY):(o=e.clientX,s=e.clientY);var r=document.getElementById(this.element.id+"_svg"),l=r.getBoundingClientRect(),d=this.element.getBoundingClientRect(),g=s-d.top-Math.max(l.top-d.top,0),m=o-d.left-Math.max(l.left-d.left,0);if(-1!==a.indexOf("Legend")){var h=document.getElementById(a);this.options.legendToggleVisibility?h.setAttribute("cursor","pointer"):h.setAttribute("cursor","auto")}var u=document.getElementById(this.element.id+"_Tooltip"),c=document.getElementById(this.element.id+"_Tooltip_Group"),p=(e.target.parentElement||e.target.parentNode).id;-1===a.indexOf("Pointer")||!sf.base.isNullOrUndefined(this.options.tooltipType)&&-1===this.options.tooltipType.indexOf("Pointer")||(i=+(t=a.replace(this.element.id,"").split("_Axis_")[1])[0],n=+t[t.length-1]),this.performDragOperation(a,i,n,m,g,event);var A=p.indexOf("Annotation")>-1?"Annotation":a.indexOf("Pointer")>-1?"Pointer":a.indexOf("_Range_")>-1?"Range":a.indexOf("_CircularGaugeTitle")>-1&&event.target.textContent.indexOf("...")>-1?"Title":-1!==a.indexOf("Legend")&&a.indexOf("_Text_")>-1&&event.target.textContent.indexOf("...")>-1?"Legend":"";this.gaugeTooltip(a,p,o,s,c,u,d,l,r,A),sf.base.isNullOrUndefined(this.dotNetRef)||a.indexOf("_CircularGaugeBorder")>-1&&this.dotNetRef.invokeMethodAsync("TriggerTooltipEvent",o,s,0,0,0,!1,!1,!1,!1,!1,d)},e.prototype.gaugeTooltip=function(e,t,i,n,o,s,a,r,l,d){var g=this;if(this.options.enableTooltip&&"Title"!==d&&"Legend"!==d){var m=void 0,h=0,u=0;if("Annotation"===d){if(a={left:Math.abs(a.left-r.left),top:Math.abs(a.top-r.top),width:r.width,height:r.height,x:Math.abs(a.left-r.left),y:Math.abs(a.top-r.top),bottom:0,right:0},(-1!==e.indexOf("Annotation")||-1!==t.indexOf("Annotation"))&&!sf.base.isNullOrUndefined(this.options.tooltipType)&&-1!==this.options.tooltipType.indexOf("Annotation")){var c=void 0;if(-1!==t.indexOf("ContentTemplate")){var p=t.split("_ContentTemplate")[0];c=p[p.length-1]}var A=document.getElementById(t);h=+(m=A.id.replace(this.element.id,"").split("_Axis_")[1])[0],u=+(-1===t.indexOf("ContentTemplate")?m[m.length-1]:c),u=isNaN(u)?0:u;if(null!==o&&null!==s.lastElementChild){var f=this.element.id+"_Tooltip_Annotation_"+u+"_Content";document.getElementById(f);document.querySelectorAll("#"+f)}var v=A.getBoundingClientRect().width;sf.base.isNullOrUndefined(this.dotNetRef)||(clearTimeout(this.tooltipTimer),this.dotNetRef.invokeMethodAsync("TriggerTooltipEvent",i+v/2,n,h,u,0,!1,!1,!0,!1,!1,a),this.isTouch&&(this.tooltipTimer=setTimeout((function(){g.dotNetRef.invokeMethodAsync("TriggerTooltipEvent",0,0,0,0,0,!1,!1,!1,!1,!1,a)}),1500)))}}else if("Pointer"===d){if((-1!==e.indexOf("Pointer")||-1!==e.indexOf("_Range_")||-1!==e.indexOf("Annotation")||-1!==t.indexOf("Annotation")&&-1===e.indexOf("Annotation"))&&-1!==e.indexOf("Pointer")&&(sf.base.isNullOrUndefined(this.options.tooltipType)||-1!==this.options.tooltipType.indexOf("Pointer"))){h=+(m=e.replace(this.element.id,"").split("_Axis_")[1])[0],u=+m[m.length-1];var x=0,y=0;if(this.options.showPointerTooltipAtMousePosition)x=(b=this.getMousePosition(i,n,l)).x,y=b.y;else x=i,y=n;sf.base.isNullOrUndefined(this.dotNetRef)||(clearTimeout(this.tooltipTimer),this.dotNetRef.invokeMethodAsync("TriggerTooltipEvent",x,y,h,u,0,!1,!0,!1,!1,!1,a),this.isTouch&&(this.tooltipTimer=setTimeout((function(){g.dotNetRef.invokeMethodAsync("TriggerTooltipEvent",0,0,0,0,0,!1,!1,!1,!1,!1,a)}),1500)))}}else if("Range"===d){if(-1!==e.indexOf("_Range_")&&!sf.base.isNullOrUndefined(this.options.tooltipType)&&-1!==this.options.tooltipType.indexOf("Range")&&!this.isMouseDown){h=+(m=e.replace(this.element.id,"").split("_Axis_")[1])[0],u=+m[m.length-1];x=0,y=0;if(this.options.showRangeTooltipAtMousePosition)x=(b=this.getMousePosition(i,n,l)).x,y=b.y;else x=i,y=n;sf.base.isNullOrUndefined(this.dotNetRef)||(clearTimeout(this.tooltipTimer),this.dotNetRef.invokeMethodAsync("TriggerTooltipEvent",x,y,h,u,0,!0,!1,!1,!1,!1,a),this.isTouch&&(this.tooltipTimer=setTimeout((function(){g.dotNetRef.invokeMethodAsync("TriggerTooltipEvent",0,0,0,0,0,!1,!1,!1,!1,!1,a)}),1500)))}}else(null!==s||o&&o.childElementCount>0)&&!this.isPointerDrag&&this.dotNetRef.invokeMethodAsync("TriggerTooltipEvent",0,0,0,0,0,!1,!1,!1,!1,!1,a)}else if("Title"===d)sf.base.isNullOrUndefined(this.dotNetRef)||(clearTimeout(this.tooltipTimer),this.dotNetRef.invokeMethodAsync("TriggerTooltipEvent",0,0,0,0,0,!1,!1,!1,!0,!1,a),this.isTouch&&(this.tooltipTimer=setTimeout((function(){g.dotNetRef.invokeMethodAsync("TriggerTooltipEvent",0,0,0,0,0,!1,!1,!1,!1,!1,a)}),1500)));else if("Legend"===d){var b=this.getMousePosition(i,n,l);clearTimeout(this.tooltipTimer),this.dotNetRef.invokeMethodAsync("TriggerTooltipEvent",b.x,b.y,0,0,parseInt(e.split("_Text_")[1]),!1,!1,!1,!1,!0,a),this.isTouch&&(this.tooltipTimer=setTimeout((function(){g.dotNetRef.invokeMethodAsync("TriggerTooltipEvent",0,0,0,0,0,!1,!1,!1,!1,!1,a)}),1500))}else this.isPointerDrag||this.dotNetRef.invokeMethodAsync("TriggerTooltipEvent",0,0,0,0,0,!1,!1,!1,!1,!1,a)},e.prototype.performDragOperation=function(e,t,i,n,o,s){if(this.options.enablePointerDrag&&-1!==e.indexOf("Pointer")||this.options.enableRangeDrag&&-1!==e.indexOf("_Range_")||this.isMouseDown)if(this.isMouseDown){s.preventDefault();var a=new Date;if(null!==t&&null!==i&&this.isMouseDown){var r="touchmove"===s.type;this.isRangeDrag&&this.options.enableRangeDrag&&!sf.base.isNullOrUndefined(this.dotNetRef)?(document.getElementById(this.element.id+"_svg").setAttribute("cursor","grabbing"),this.dotNetRef.invokeMethodAsync("TriggerRangeDragEvent",n,o,this.dragAxisIndex,this.dragElementIndex,r)):this.isPointerDrag&&this.options.enablePointerDrag&&(sf.base.isNullOrUndefined(this.dragMilliseconds)||Math.abs(a.getMilliseconds()-this.dragMilliseconds)>100)&&!sf.base.isNullOrUndefined(this.dotNetRef)&&(this.dragMilliseconds=a.getMilliseconds(),document.getElementById(this.element.id+"_svg").setAttribute("cursor","grabbing"),this.dotNetRef.invokeMethodAsync("TriggerDragEvent",n,o,this.dragAxisIndex,this.dragElementIndex,r))}}else document.getElementById(this.element.id+"_svg").setAttribute("cursor","pointer");else document.getElementById(this.element.id+"_svg").setAttribute("cursor","auto")},e.prototype.gaugeOnMouseEnd=function(e){var t=0,i=0;this.dragMilliseconds=null;var n=e.target.id;if("touchend"===e.type){var o=e;t=o.changedTouches[0].pageX,i=o.changedTouches[0].pageY,this.isTouch=!0}else this.isTouch=!1,t=e.clientX,i=e.clientY;if(this.isTouch){var s=document.getElementById(this.element.id+"_svg"),a=s.getBoundingClientRect(),r=this.element.getBoundingClientRect(),l=(e.target.parentElement||e.target.parentNode).id,d=document.getElementById(this.element.id+"_Tooltip"),g=document.getElementById(this.element.id+"_Tooltip_Group");this.isMouseDown=!1;var m=l.indexOf("Annotation")>-1?"Annotation":n.indexOf("Pointer")>-1?"Pointer":n.indexOf("_Range_")>-1?"Range":n.indexOf("_CircularGaugeTitle")>-1&&e.target.textContent.indexOf("...")>-1?"Title":-1!==n.indexOf("Legend")&&n.indexOf("_Text_")>-1&&e.target.textContent.indexOf("...")>-1?"Legend":"";this.gaugeTooltip(n,l,t,i,g,d,r,a,s,m)}sf.base.isNullOrUndefined(this.dotNetRef)||(this.isTouch||"mouseup"==e.type?this.dotNetRef.invokeMethodAsync("TriggerMouseUpEvent",t,i):this.dotNetRef.invokeMethodAsync("TriggerMouseLeaveEvent",t,i),this.isPointerDrag?this.dotNetRef.invokeMethodAsync("TriggerDragEnd",this.dragAxisIndex,this.dragElementIndex,0,"Pointer"):this.isRangeDrag&&this.dotNetRef.invokeMethodAsync("TriggerDragEnd",this.dragAxisIndex,0,this.dragElementIndex,"Range")),this.isMouseDown=!1,this.isPointerDrag=!1,this.isRangeDrag=!1},e.prototype.performRangeBarAnimation=function(e,t,i,n){var o,s=this,a=this.allowLoadingAnimation?this.allowLoadingAnimation?t.animationEnable&&t.duration>0?t.duration:this.loadingAnimationDuration[i]:t.duration:t.animationEnable?t.duration:0;new sf.base.Animation({}).animate(e,{duration:a,progress:function(i){o=s.animationRangeProgress(o,t,i,e,a)},end:function(){if(!t.isClockWise){var o=t.endAngle;t.endAngle=t.minimumAngle,t.minimumAngle=o}if(0!=t.roundRadius&&t.end!=t.minimumAxis){var a=t.roundRadius,r=.25*t.roundRadius;t.end<=r&&(a=t.end<6?8:a,r=.25*(a/=2));var l=((t.radius-t.pointerWidth/2)*(t.minimumAngle*Math.PI/180)-a/r)/(t.radius-t.pointerWidth/2)*180/Math.PI,d=((t.radius-t.pointerWidth/2)*(t.endAngle*Math.PI/180)+a/r)/(t.radius-t.pointerWidth/2)*180/Math.PI,g=t.radius*(t.minimumAngle*Math.PI/180)/t.radius*180/Math.PI,m=t.radius*(t.endAngle*Math.PI/180)/t.radius*180/Math.PI;m=t.end>0?m:m-a/3,g=t.end>0?g:g+a/3,e.setAttribute("d",s.getRoundedPathArc({x:t.midPointX,y:t.midPointY},Math.floor(g),m,Math.floor(l),Math.floor(d),t.radius,t.pointerWidth,t.pointerWidth,Math.abs(t.roundRadius)))}else e.setAttribute("d",s.getCompletePathArc({x:t.midPointX,y:t.midPointY},t.minimumAngle,t.endAngle,t.radius,t.innerRadius,t.isClockWise));s.allowLoadingAnimation&&s.annotationAnimationProcess(),s.options.isAnimationEvent&&t.start!=t.end&&!sf.base.isNullOrUndefined(s.dotNetRef)&&s.dotNetRef.invokeMethodAsync("AnimatePointer",i,n,t.end)}})},e.prototype.performNeedleAnimation=function(e,t,i,n){var o,s=this;new sf.base.Animation({}).animate(e,{duration:this.allowLoadingAnimation?this.allowLoadingAnimation?t.animationEnable&&t.duration>0?t.duration:this.loadingAnimationDuration[i]:t.duration:t.animationEnable?t.duration:0,progress:function(i){o=s.animationProgress(o,t,i,e)},end:function(){e.setAttribute("transform","rotate("+t.endAngle+","+t.midPointX+","+t.midPointY+")"),s.allowLoadingAnimation&&s.annotationAnimationProcess(),s.options.isAnimationEvent&&t.start!=t.end&&!sf.base.isNullOrUndefined(s.dotNetRef)&&s.dotNetRef.invokeMethodAsync("AnimatePointer",i,n,t.end)}})},e.prototype.animationProgress=function(e,t,i,n){return e=t.start<t.end||Math.round(t.startAngle)===Math.round(t.endAngle)?t.isClockWise||t.start===t.end?t.endAngle-t.startAngle:t.endAngle-t.startAngle-360:t.isClockWise?t.endAngle-t.startAngle-360:t.endAngle-t.startAngle,n.style.animation="None",n.setAttribute("transform","rotate("+(-e*Math.cos(i.timeStamp/i.duration*(Math.PI/2))+e+t.startAngle)+","+t.midPointX+","+t.midPointY+")"),e},e.prototype.animationRangeProgress=function(e,t,i,n,o){var s,a,r;return e=t.start<t.end||Math.round(t.startAngle)===Math.round(t.endAngle)?t.isClockWise||t.start===t.end?t.endAngle-t.startAngle:t.endAngle-t.startAngle-360:t.isClockWise?t.endAngle-t.startAngle-360:t.endAngle-t.startAngle,n.style.animation="None",t.roundRadius<=0&&(s=-e*Math.cos(i.timeStamp/o*(Math.PI/2))+e+t.startAngle),t.isClockWise?t.roundRadius>0&&t.end!=t.minimumAxis?(a=-e*Math.cos(i.timeStamp/o*(Math.PI/2))+e+Math.floor(t.startAngle),r=-e*Math.cos(i.timeStamp/o*(Math.PI/2))+e+Math.floor(t.startAngle+Math.abs(t.roundRadius)/2),n.setAttribute("d",this.getRoundedPathArc({x:t.midPointX,y:t.midPointY},Math.floor(t.minimumAngle),a+1e-4,Math.floor(t.oldStart),Math.floor(r+1e-4),t.radius,t.pointerWidth,t.pointerWidth,Math.abs(t.roundRadius)))):n.setAttribute("d",this.getCompletePathArc({x:t.midPointX,y:t.midPointY},t.minimumAngle,s+1e-4,t.radius,t.innerRadius,!1)):t.roundRadius>0&&t.end!=t.minimumAxis?(a=-e*Math.cos(i.timeStamp/o*(Math.PI/2))+e+Math.floor(t.startAngle),r=-e*Math.cos(i.timeStamp/o*(Math.PI/2))+e+Math.floor(t.startAngle-Math.abs(t.roundRadius)/2),n.setAttribute("d",this.getRoundedPathArc({x:t.midPointX,y:t.midPointY},a,t.minimumAngle+1e-4,r,Math.floor(t.oldStart+Math.abs(t.roundRadius)),t.radius,t.pointerWidth,t.pointerWidth,Math.abs(t.roundRadius)))):n.setAttribute("d",this.getCompletePathArc({x:t.midPointX,y:t.midPointY},s,t.minimumAngle+1e-4,t.radius,t.innerRadius,!1)),e},e.prototype.annotationAnimationProcess=function(){if(this.isPointerRenderCompleted)for(var e=0;e<this.axes.length;e++)if(this.axes[e].IsAnnotationVisible){var t=document.getElementById(this.element.id+"_Axis_"+e+"_Annotation");null!=t&&this.annotationAnimateProgress(t,e)}},e.prototype.annotationAnimateProgress=function(e,t){var i=0;"0"===e.style.opacity&&new sf.base.Animation({}).animate(e,{duration:this.loadingAnimationDuration[t],progress:function(t){t.timeStamp>t.delay&&(i=(t.timeStamp-t.delay)/t.duration,e.style.opacity=1*i)},end:function(){e.style.opacity=1}})},e.prototype.pointerAnimationProcess=function(e){var t=document.getElementById(e.id);if(!sf.base.isNullOrUndefined(t))for(var i=t.id.replace(this.element.id,"").split("_Axis_")[1],n=+i[0],o=+i[i.length-1],s=sf.base.Browser.isIE?t.childNodes.length:t.childElementCount,a=0;a<s;a++){var r=sf.base.Browser.isIE?t.childNodes[a]:t.children[a];"#comment"!==r.nodeName&&("RangeBar"===e.pointerType?this.performRangeBarAnimation(r,e,n,o):e.isMarkerShapeText?this.performTextAnimation(r,e,n,o):this.performNeedleAnimation(r,e,n,o))}},e.prototype.performTextAnimation=function(e,t,i,n){var o,s,a,r,l=this,d=Math.abs(t.start-t.end);new sf.base.Animation({}).animate(e,{duration:this.allowLoadingAnimation?this.allowLoadingAnimation?t.animationEnable&&t.duration>0?t.duration:this.loadingAnimationDuration[i]:t.duration:t.animationEnable?t.duration:0,progress:function(i){r=i.timeStamp/t.duration,a=t.end>t.start?t.start+r*d:t.start-r*d,o=l.getAngleFromValue(a,t.maximumAxis,t.minimumAxis,t.axisStartAngle,t.axisEndAngle,t.isClockWise),s=l.getLocationFromAngle(o,t.radius,{x:t.midPointX,y:t.midPointY}),e.setAttribute("transform","rotate("+(o+90)+", "+s.x+", "+s.y+")"),e.setAttribute("x",String(s.x)),e.setAttribute("y",String(s.y)),e.style.visibility="visible"},end:function(){o=l.getAngleFromValue(t.pointerValue,t.maximumAxis,t.minimumAxis,t.axisStartAngle,t.axisEndAngle,t.isClockWise),s=l.getLocationFromAngle(o,t.radius,{x:t.midPointX,y:t.midPointY}),e.setAttribute("transform","rotate("+(o+90)+","+s.x+","+s.y+")"),e.setAttribute("x",String(s.x)),e.setAttribute("y",String(s.y)),l.allowLoadingAnimation&&l.annotationAnimationProcess(),l.options.isAnimationEvent&&t.start!=t.end&&!sf.base.isNullOrUndefined(l.dotNetRef)&&l.dotNetRef.invokeMethodAsync("AnimatePointer",i,n,t.end)}})},e.prototype.getAngleFromValue=function(e,t,i,n,o,s){var a;return o-=this.isCompleteAngle(n,o)?1e-4:0,n-=90,o-=90,a=s?(e-i)*(this.getDegreeValue(n,o)/(t-i))+n:(a=o-(e-i)*(this.getDegreeValue(n,o)/(t-i)))<0?360+a:a,a=Math.round(a)>=360?a-360:Math.round(a)<0?360+a:a},e.prototype.isCompleteAngle=function(e,t){var i=t-e;return i=i<=0?i+360:i,0!=Math.floor(i/360)},e.prototype.getDegreeValue=function(e,t){var i=t-e;return i<0?i+360:i},e.prototype.getLocationFromAngle=function(e,t,i){var n=e*Math.PI/180;return{x:Math.cos(n)*t+i.x,y:Math.sin(n)*t+i.y}},e.prototype.getCompletePath=function(e,t,i,n,o,s,a){return"M "+e.x+" "+e.y+" A "+i+" "+i+" 0 "+a+" 1 "+t.x+" "+t.y+" L "+o.x+" "+o.y+" A "+s+" "+s+" 0 "+a+" 0 "+n.x+" "+n.y+" Z"},e.prototype.getRoundedPath=function(e,t,i,n,o,s,a,r,l,d,g,m){return"M "+e.x+" "+e.y+" A "+l+" "+l+" 0 "+m+" 1 "+t.x+" "+t.y+" C "+i.x+" "+i.y+" "+n.x+" "+n.y+" "+r.x+" "+r.y+" A "+g+" "+d+" 0 "+m+" 0 "+a.x+" "+a.y+" C "+s.x+" "+s.y+" "+o.x+" "+o.y+" "+e.x+" "+e.y+" Z"},e.prototype.getCompletePathArc=function(e,t,i,n,o,s){i-=this.isCompleteAngle(t,i)&&!s&&t!=i?1e-4:0;var a=this.getDegreeValue(t,i),r=a<180&&a>0||a>360&&a<540||t>630?0:1;return this.getCompletePath(this.getLocationFromAngle(t,n,e),this.getLocationFromAngle(i,n,e),n,this.getLocationFromAngle(t,o,e),this.getLocationFromAngle(i,o,e),o,r)},e.prototype.getRoundedPathArc=function(e,t,i,n,o,s,a,r,l){i-=this.isCompleteAngle(t,i)?1e-4:0;var d=this.getDegreeValue(t,i),g=s-(a=s<a?s:a),m=s-(r=s<r?s:r),h=s-(a+r)/2,u=180;l&&(o+=s>=a?l/s:0,n-=s>=a?l/s:0,t+=s>=a?l/2:0,i-=s>=a?l/2:0,u+=l);var c=d<=u&&d>0||d>2*(u-l)&&d<3*(u-l)||t>3*(u-l)+(u-l)/2?0:1;return this.getRoundedPath(this.getLocationFromAngle(t,s,e),this.getLocationFromAngle(i,s,e),this.getLocationFromAngle(o,s,e),this.getLocationFromAngle(o,m,e),this.getLocationFromAngle(n,s,e),this.getLocationFromAngle(n,g,e),this.getLocationFromAngle(t,g,e),this.getLocationFromAngle(i,m,e),s,h,h,c)},e.prototype.getMousePosition=function(e,t,i){var n=i.getBoundingClientRect(),o=i.ownerDocument.defaultView.pageXOffset,s=i.ownerDocument.defaultView.pageYOffset,a=i.ownerDocument.documentElement.clientTop,r=i.ownerDocument.documentElement.clientLeft;return{x:e-(n.left+o-r),y:t-(n.top+s-a)}},e.prototype.loadingAnimationProcess=function(e,t,i,n){this.axes=e,this.durationSplitUp(n,e),this.allowLoadingAnimation=n>0;for(var o=0;o<e.length;o++)this.isPointerRenderCompleted=0===i.length,this.axisLineAnimation(e[o],t[o],o,this.loadingAnimationDuration[o],i)},e.prototype.durationSplitUp=function(e,t){for(var i=0;i<t.length;i++){var n=0,o=0;t[i].IsAxisLineVisible&&n++,t[i].IsMajorTickVisible&&(n++,o++),t[i].IsMinorTickVisible&&(t[i].IsMajorTickVisible||n++,o++),t[i].IsAxisLabelVisible&&(t[i].IsMajorTickVisible||t[i].IsMinorTickVisible||n++,o++),t[i].IsRangeVisible&&n++,t[i].IsPointerVisible&&n++,t[i].IsAnnotationVisible&&n++,this.allowAxisCount.push(0===o?1:o),this.loadingAnimationDuration.push(0===n?e:e/n)}},e.prototype.axisLineAnimation=function(e,t,i,n,o){var s=this;this.prevAnimatedMajorTickValue.push(0),this.prevAnimatedMinorTickValue.push(0),this.prevAnimatedMinorTickIndex.push(0),this.prevAnimatedMajorTickIndex.push(0),this.prevAnimatedTickType="major";var a=this.isCompleteAngle(e.AxisStartAngle,e.AxisEndAngle),r=(a?0:e.AxisStartAngle)-90,l=(a?360:e.AxisEndAngle)-90;l-=this.isCompleteAngle(r,l)?1e-4:0;var d=this.getDegreeValue(r,l),g=document.getElementById(this.element.id+"_AxisLine_"+i);t.duration=n,new sf.base.Animation({}).animate(g,{duration:e.IsAxisLineVisible?n:0,progress:function(i){g.setAttribute("style","pointer-events:none; visibility: visible"),s.animationAxisProgress(e,void 0,t,i,g)},end:function(){if(s.isResize)g.setAttribute("fill","transparent"),g.setAttribute("stroke-width",t.pointerWidth);else{if(t.radius-=t.pointerWidth/2,!t.isClockWise){var a=t.endAngle;t.endAngle=t.minimumAngle,t.minimumAngle=a}var l=s.getCirclePath(s.getLocationFromAngle(r,t.radius,{x:t.midPointX,y:t.midPointY}),s.getLocationFromAngle(t.endAngle,t.radius,{x:t.midPointX,y:t.midPointY}),e.CurrentRadius,d<180?0:1);g.setAttribute("d",l),g.setAttribute("fill","transparent"),g.setAttribute("stroke-width",t.pointerWidth),s.tickElementAnimation(document.getElementById(s.element.id+"_Axis_Major_TickLine_"+i+"_0"),document.getElementById(s.element.id+"_Axis_"+i+"_Label_0"),n,i,o,e.IsMajorTickVisible&&e.IsMinorTickVisible?0:-1,"major",e)}}})},e.prototype.elementLabelAnimation=function(e,t,i,n,o){var s=this;e&&!this.isResize&&new sf.base.Animation({}).animate(e,{duration:n.IsAxisLabelVisible?this.loadingAnimationDuration[i]/n.AxisLabelCount:0,progress:function(){e.style.visibility="visible"},end:function(){if(!s.isResize){t+=1,s.elementLabelAnimation(document.getElementById(s.element.id+"_Axis_"+i+"_Label_"+t),t,i,n,o);var a=document.getElementById(s.element.id+"_Axis_Labels_"+i);n.ShowLastLabel&&parseInt(e.id.split("Label_")[1])===a.childElementCount-2&&(e=document.getElementById(s.element.id+"_Axis_"+i+"_Label_"+(a.childElementCount-1)))&&(e.style.visibility="visible"),n.AxisLabelCount-1===t&&s.rangePointerAnimation(o,i)}}})},e.prototype.pointerAnimate=function(e,t){if(!this.isResize)if(this.axes.length-1===t&&e.length>0)for(var i=0;i<e.length;i++){var n=parseInt(e[i].id.split("_Axis_")[1].split("_")[0],10);document.getElementById(this.element.id+"_Axis_Pointers_"+n).style.visibility="visible",this.isPointerRenderCompleted=i===e.length-1,this.pointerAnimationProcess(e[i])}else this.annotationAnimationProcess()},e.prototype.rangePointerAnimation=function(e,t){if(!this.isResize)for(var i=0;i<this.axes.length;i++)this.axes[i].IsRangeVisible?this.rangeElementAnimation(document.getElementById(this.element.id+"_Axis_Ranges_"+i),e,i):this.axes.length-1===i&&this.pointerAnimate(e,i)},e.prototype.tickElementAnimation=function(e,t,i,n,o,s,a,r){var l=this;if(e&&!this.isResize){var d=this.axes[n];new sf.base.Animation({}).animate(e,{duration:d.IsMinorTickVisible?0===d.MinorTickCount?0:this.loadingAnimationDuration[n]/d.MinorTickCount/this.allowAxisCount[n]:0===d.MajorTickCount?0:this.loadingAnimationDuration[n]/d.MajorTickCount/this.allowAxisCount[n],progress:function(){e.style.visibility="visible"},end:function(){if(d.IsMajorTickVisible&&d.IsMinorTickVisible){e.style.visibility="visible";var g=parseFloat(e.getAttribute("data-interval"));l.prevAnimatedTickType=a,"major"===a?(l.prevAnimatedMajorTickValue[n]=g,l.prevAnimatedMajorTickIndex[n]=s):(l.prevAnimatedMinorTickValue[n]=g,l.prevAnimatedMinorTickIndex[n]=s);var m=r.MinorTickInterval,h=m<r.MajorTickInterval?g+m:l.prevAnimatedMinorTickValue[n]+m,u=l.prevAnimatedMajorTickValue[n]+r.MajorTickInterval;"major"===(a=h<u?"minor":"major")&&r.MajorTickCount!==r.MinorTickCount&&0!==s&&"minor"===l.prevAnimatedTickType&&(s=l.prevAnimatedMajorTickIndex[n]),"minor"===a&&r.MajorTickCount!==r.MinorTickCount&&0!==s&&"major"===l.prevAnimatedTickType&&(s=l.prevAnimatedMinorTickIndex[n]),s="minor"===a&&(r.MajorTickCount===r.MinorTickCount||g===l.prevAnimatedMajorTickValue[n])?s:s+1,e="minor"===a?document.getElementById(l.element.id+"_Axis_Minor_TickLine_"+n+"_"+s):document.getElementById(l.element.id+"_Axis_Major_TickLine_"+n+"_"+s),t="minor"===a?null:document.getElementById(l.element.id+"_Axis_"+n+"_Label_"+s),"major"!==a&&0!=s||!d.IsAxisLabelVisible||l.labelElementAnimation(document.getElementById(l.element.id+"_Axis_"+n+"_Label_"+s),r,n),l.tickElementAnimation(e,t,i,n,o,s,a,r),"major"===a&&s===r.MajorTickCount-1&&l.axes.length-1===n&&l.rangePointerAnimation(o,n)}else d.IsMajorTickVisible?(a="major",s+=1,e=document.getElementById(l.element.id+"_Axis_Major_TickLine_"+n+"_"+s),t=document.getElementById(l.element.id+"_Axis_"+n+"_Label_"+s),l.tickElementAnimation(e,t,i,n,o,s,a,r),"major"!==a&&0!==s||!d.IsAxisLabelVisible||l.labelElementAnimation(document.getElementById(l.element.id+"_Axis_"+n+"_Label_"+s),r,n),"major"===a&&s===r.MajorTickCount-1&&l.axes.length-1===n&&l.rangePointerAnimation(o,n)):d.IsMinorTickVisible?(a="minor",s+=1,e=document.getElementById(l.element.id+"_Axis_Minor_TickLine_"+n+"_"+s),t=document.getElementById(l.element.id+"_Axis_"+n+"_Label_"+s),l.tickElementAnimation(e,t,i,n,o,s,a,r),"minor"!==a&&0!==s||!d.IsAxisLabelVisible||l.labelElementAnimation(document.getElementById(l.element.id+"_Axis_"+n+"_Label_"+s),r,n),"minor"===a&&s===r.MinorTickCount-1&&l.axes.length-1===n&&l.rangePointerAnimation(o,n)):d.IsAxisLabelVisible?l.elementLabelAnimation(document.getElementById(l.element.id+"_Axis_"+n+"_Label_0"),s,n,r,o):d.IsRangeVisible?l.rangePointerAnimation(o,n):d.IsPointerVisible?l.pointerAnimate(o,n):d.IsAnnotationVisible&&l.annotationAnimationProcess()}})}},e.prototype.rangeElementAnimation=function(e,t,i){var n=this,o=0;new sf.base.Animation({}).animate(e,{duration:this.axes[i].IsRangeVisible?this.loadingAnimationDuration[i]:0,progress:function(t){t.timeStamp>t.delay&&(o=(t.timeStamp-t.delay)/t.duration,e.style.opacity=1*o)},end:function(){n.axes.length-1!==i||n.isResize||n.pointerAnimate(t,i)}})},e.prototype.getCirclePath=function(e,t,i,n){return"M "+e.x+" "+e.y+" A "+i+" "+i+" 0 "+n+" 1 "+t.x+" "+t.y},e.prototype.animationAxisProgress=function(e,t,i,n,o){if(this.isResize)o.setAttribute("fill","transparent"),o.setAttribute("stroke-width",i.pointerWidth),this.isPropertyChange&&o.setAttribute("d",e.AxisLinePath);else{var s;if(s=-(t=i.start<i.end||Math.round(i.startAngle)===Math.round(i.endAngle)?i.isClockWise||i.start===i.end?i.endAngle-i.startAngle:i.endAngle-i.startAngle-360:i.isClockWise?i.endAngle-i.startAngle-360:i.endAngle-i.startAngle)*Math.cos(n.timeStamp/i.duration*(Math.PI/2))+t+i.startAngle,i.isClockWise){var a=this.getCompletePathArc({x:i.midPointX,y:i.midPointY},i.minimumAngle,s+1e-4,i.radius,i.innerRadius,!1);o.setAttribute("d",a)}else o.setAttribute("d",this.getCompletePathArc({x:i.midPointX,y:i.midPointY},s,i.minimumAngle+1e-4,i.radius,i.innerRadius,!1))}},e}();return{initialize:function(t,i,n,o){new e(t.id,t,i,n).render();var s={height:0,width:0,isIE:!1,isDevice:!1};if(o){var a=this.getContainerSize(t.id,n);s.height=a.height,s.width=a.width}return s.isIE=sf.base.Browser.isIE,s.isDevice=sf.base.Browser.isDevice,s},getContainerSize:function(e,t){var i=document.getElementById(e),n=i.clientWidth;return{height:i.clientHeight,width:n}},pointerLoadingAnimation:function(e,t){var i=window.sfBlazor.getCompInstance(e);if(i&&i.element){i.allowLoadingAnimation=!1;for(var n=JSON.parse(t.pointerAnimation),o=0;o<n.length;o++)i.pointerAnimationProcess(n[o])}},axisLoadingAnimation:function(e,t,i,n,o){var s=window.sfBlazor.getCompInstance(e);if(s&&s.element){s.allowLoadingAnimation=o;var a=JSON.parse(t.axis),r=JSON.parse(i.pointerAnimation),l=JSON.parse(i.axisAnimation);s.loadingAnimationProcess(a,l,r,n)}},setPointerDragStatus:function(e,t){var i=window.sfBlazor.getCompInstance(e);i&&i.element&&(i.options.enablePointerDrag=t)},setRangeDragStatus:function(e,t){var i=window.sfBlazor.getCompInstance(e);i&&i.element&&(i.options.enableRangeDrag=t)},setLegendToggle:function(e,t){var i=window.sfBlazor.getCompInstance(e);i&&i.element&&(i.options.legendToggleVisibility=t)},stopAnimationProcess:function(e,t){var i=window.sfBlazor.getCompInstance(e);i&&i.element&&(i.isResize=t,i.isPropertyChange=!0)},getElementBounds:function(e){var t=document.getElementById(e);if(t){var i=t.getBoundingClientRect();return{width:i.width,height:i.height,top:i.top,bottom:i.bottom,left:i.left,right:i.right}}return null},dispose:function(e){var t=window.sfBlazor.getCompInstance(e);t&&t.element&&t.destroy()}}}()}}]);(async()=>{await import(`${document.baseURI}_content/Syncfusion.Blazor/scripts/syncfusion-blazor-base.min.js?v=26.1.undefined`).then(()=>{sfBlazor.loadDependencies('sfcirculargauge');})})();