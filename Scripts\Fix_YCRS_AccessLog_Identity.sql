-- 修復 YCRS_AccessLog 表的 Id 欄位為自動遞增
-- 這個腳本需要在 YCRS 資料庫中執行

USE [YMDB]
GO

-- 檢查 YCRS_AccessLog 表是否存在
IF EXISTS (SELECT * FROM sys.tables WHERE name = 'YCRS_AccessLog')
BEGIN
    PRINT 'YCRS_AccessLog 表存在，開始檢查 Id 欄位設定...'
    
    -- 檢查 Id 欄位是否為 IDENTITY
    IF EXISTS (
        SELECT * FROM sys.columns c
        INNER JOIN sys.tables t ON c.object_id = t.object_id
        WHERE t.name = 'YCRS_AccessLog' 
        AND c.name = 'Id' 
        AND c.is_identity = 1
    )
    BEGIN
        PRINT 'Id 欄位已經是 IDENTITY，無需修改。'
    END
    ELSE
    BEGIN
        PRINT '開始修復 Id 欄位為 IDENTITY...'
        
        -- 備份現有資料
        IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'YCRS_AccessLog_Backup')
        BEGIN
            SELECT * INTO YCRS_AccessLog_Backup FROM YCRS_AccessLog
            PRINT '已備份現有資料到 YCRS_AccessLog_Backup 表'
        END
        
        -- 如果表中有資料，需要先處理
        DECLARE @RowCount INT
        SELECT @RowCount = COUNT(*) FROM YCRS_AccessLog
        
        IF @RowCount > 0
        BEGIN
            PRINT '表中有 ' + CAST(@RowCount AS VARCHAR(10)) + ' 筆資料，需要重建表結構...'
            
            -- 創建新的表結構
            CREATE TABLE YCRS_AccessLog_New (
                Id int IDENTITY(1,1) PRIMARY KEY,
                Sn nvarchar(max) NULL,
                Action nvarchar(50) NOT NULL,
                Account nvarchar(50) NOT NULL,
                IP nvarchar(50) NULL,
                Timestamp datetime2 NOT NULL
            )
            
            -- 將資料插入新表（不包含 Id，讓它自動生成）
            SET IDENTITY_INSERT YCRS_AccessLog_New OFF
            INSERT INTO YCRS_AccessLog_New (Sn, Action, Account, IP, Timestamp)
            SELECT Sn, Action, Account, IP, Timestamp
            FROM YCRS_AccessLog
            ORDER BY Id  -- 保持原有順序
            
            -- 刪除舊表
            DROP TABLE YCRS_AccessLog
            
            -- 重命名新表
            EXEC sp_rename 'YCRS_AccessLog_New', 'YCRS_AccessLog'
            
            PRINT '表結構重建完成，Id 欄位現在是 IDENTITY。'
        END
        ELSE
        BEGIN
            PRINT '表中無資料，直接修改表結構...'
            
            -- 檢查是否有主鍵約束
            DECLARE @ConstraintName NVARCHAR(128)
            SELECT @ConstraintName = name 
            FROM sys.key_constraints 
            WHERE type = 'PK' 
            AND parent_object_id = OBJECT_ID('YCRS_AccessLog')
            
            -- 如果有主鍵約束，先刪除
            IF @ConstraintName IS NOT NULL
            BEGIN
                EXEC('ALTER TABLE YCRS_AccessLog DROP CONSTRAINT [' + @ConstraintName + ']')
                PRINT '已刪除主鍵約束: ' + @ConstraintName
            END
            
            -- 刪除現有的 Id 欄位
            ALTER TABLE YCRS_AccessLog DROP COLUMN Id
            
            -- 添加新的 IDENTITY Id 欄位
            ALTER TABLE YCRS_AccessLog ADD Id int IDENTITY(1,1) PRIMARY KEY
            
            PRINT 'Id 欄位已修改為 IDENTITY。'
        END
    END
END
ELSE
BEGIN
    PRINT 'YCRS_AccessLog 表不存在，將由 Entity Framework 自動創建。'
END

-- 驗證修改結果
IF EXISTS (
    SELECT * FROM sys.columns c
    INNER JOIN sys.tables t ON c.object_id = t.object_id
    WHERE t.name = 'YCRS_AccessLog' 
    AND c.name = 'Id' 
    AND c.is_identity = 1
)
BEGIN
    PRINT '✓ 驗證成功：Id 欄位現在是 IDENTITY。'
END
ELSE
BEGIN
    PRINT '✗ 驗證失敗：Id 欄位仍然不是 IDENTITY。'
END

PRINT '腳本執行完成。'
