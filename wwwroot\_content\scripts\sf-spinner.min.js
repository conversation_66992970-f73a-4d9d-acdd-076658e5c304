/*!*  filename: sf-spinner.min.js 
*     version : 26.1.41 
*     Copyright Syncfusion Inc. 2001 - 2024. All rights reserved. 
*     Use of this code is subject to the terms of our license.
*     A copy of the current license can be obtained at any time by e-mailing 
*     <EMAIL>. Any infringement will be prosecuted under 
*     applicable laws. 
*/
(window.webpackJsonp=window.webpackJsonp||[]).push([[58],{"./bundles/sf-spinner.js":function(n,e,i){"use strict";i.r(e);i("./modules/sf-spinner.js")},"./modules/sf-spinner.js":function(n,e){window.sfBlazor=window.sfBlazor||{},window.sfBlazor.Spinner=function(){"use strict";var n=function(){function n(n){this.element=n,this.element.blazor__instance=this}return n.prototype.initialize=function(n){return window.getComputedStyle(n,":after").getPropertyValue("content").replace(/['"]+/g,"")},n}();return{initialize:function(e){return sf.base.isNullOrUndefined(e)?null:(new n(e),sf.base.isNullOrUndefined(e)||sf.base.isNullOrUndefined(e.blazor__instance)?null:e.blazor__instance.initialize(e))}}}()}}]);(async()=>{await import(`${document.baseURI}_content/Syncfusion.Blazor/scripts/syncfusion-blazor-base.min.js?v=26.1.undefined`).then(()=>{sfBlazor.loadDependencies('sfspinner');})})();