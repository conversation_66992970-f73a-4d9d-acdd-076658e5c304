namespace Intra2025.Services
{
    /// <summary>
    /// 檔案上傳配置服務，集中管理檔案上傳的安全設定
    /// </summary>
    public class FileUploadConfigService
    {
        /// <summary>
        /// 檔案名稱最大長度
        /// </summary>
        public int MaxFileNameLength { get; set; } = 100;

        /// <summary>
        /// 檔案內容掃描的最大大小
        /// </summary>
        public long MaxScanSize { get; set; } = 50 * 1024 * 1024; // 50MB

        /// <summary>
        /// 檔案上傳類別配置
        /// </summary>
        private readonly Dictionary<string, FileUploadCategory> _categories = new()
        {
            {
                "YCRS_Care", new FileUploadCategory
                {
                    Name = "溫馨關懷表",
                    MaxFileSize = 20 * 1024 * 1024, // 20MB
                    AllowedExtensions = new[] { ".pdf", ".zip", ".7z" },
                    AllowedMimeTypes = new[] 
                    { 
                        "application/pdf", 
                        "application/zip", 
                        "application/x-zip-compressed",
                        "application/x-7z-compressed"
                    },
                    MaxFilesPerUpload = 1,
                    Description = "溫馨關懷表相關文件"
                }
            },
            {
                "Reports", new FileUploadCategory
                {
                    Name = "規劃報告書",
                    MaxFileSize = 200 * 1024 * 1024, // 200MB
                    AllowedExtensions = new[] { ".pdf", ".odt", ".ods", ".odp", ".zip", ".7z" },
                    AllowedMimeTypes = new[] 
                    { 
                        "application/pdf",
                        "application/vnd.oasis.opendocument.text",
                        "application/vnd.oasis.opendocument.spreadsheet", 
                        "application/vnd.oasis.opendocument.presentation",
                        "application/zip",
                        "application/x-zip-compressed",
                        "application/x-7z-compressed"
                    },
                    MaxFilesPerUpload = 5,
                    Description = "規劃報告書相關文件"
                }
            },
            {
                "ComRemit", new FileUploadCategory
                {
                    Name = "支付作業",
                    MaxFileSize = 5 * 1024 * 1024, // 5MB
                    AllowedExtensions = new[] { ".csv", ".txt" },
                    AllowedMimeTypes = new[] 
                    { 
                        "text/csv",
                        "text/plain",
                        "application/csv"
                    },
                    MaxFilesPerUpload = 1,
                    Description = "支付作業批次上傳檔案"
                }
            }
        };

        /// <summary>
        /// 取得允許的副檔名
        /// </summary>
        public string[] GetAllowedExtensions(string category)
        {
            return _categories.TryGetValue(category, out var config) 
                ? config.AllowedExtensions 
                : Array.Empty<string>();
        }

        /// <summary>
        /// 取得最大檔案大小
        /// </summary>
        public long GetMaxFileSize(string category)
        {
            return _categories.TryGetValue(category, out var config) 
                ? config.MaxFileSize 
                : 1024 * 1024; // 預設 1MB
        }

        /// <summary>
        /// 取得允許的 MIME 類型
        /// </summary>
        public string[] GetAllowedMimeTypes(string category)
        {
            return _categories.TryGetValue(category, out var config) 
                ? config.AllowedMimeTypes 
                : Array.Empty<string>();
        }

        /// <summary>
        /// 取得每次上傳的最大檔案數量
        /// </summary>
        public int GetMaxFilesPerUpload(string category)
        {
            return _categories.TryGetValue(category, out var config) 
                ? config.MaxFilesPerUpload 
                : 1;
        }

        /// <summary>
        /// 取得類別描述
        /// </summary>
        public string GetCategoryDescription(string category)
        {
            return _categories.TryGetValue(category, out var config) 
                ? config.Description 
                : "未知類別";
        }

        /// <summary>
        /// 取得所有可用的類別
        /// </summary>
        public IEnumerable<string> GetAvailableCategories()
        {
            return _categories.Keys;
        }

        /// <summary>
        /// 取得類別配置
        /// </summary>
        public FileUploadCategory? GetCategoryConfig(string category)
        {
            return _categories.TryGetValue(category, out var config) ? config : null;
        }

        /// <summary>
        /// 驗證類別是否存在
        /// </summary>
        public bool IsCategoryValid(string category)
        {
            return _categories.ContainsKey(category);
        }

        /// <summary>
        /// 取得檔案大小的友善顯示
        /// </summary>
        public string GetFileSizeDisplay(string category)
        {
            var sizeInBytes = GetMaxFileSize(category);
            if (sizeInBytes >= 1024 * 1024 * 1024)
            {
                return $"{sizeInBytes / (1024 * 1024 * 1024)} GB";
            }
            else if (sizeInBytes >= 1024 * 1024)
            {
                return $"{sizeInBytes / (1024 * 1024)} MB";
            }
            else if (sizeInBytes >= 1024)
            {
                return $"{sizeInBytes / 1024} KB";
            }
            else
            {
                return $"{sizeInBytes} bytes";
            }
        }

        /// <summary>
        /// 取得允許副檔名的顯示字串
        /// </summary>
        public string GetAllowedExtensionsDisplay(string category)
        {
            var extensions = GetAllowedExtensions(category);
            return string.Join(", ", extensions);
        }

        /// <summary>
        /// 驗證檔案是否符合類別要求
        /// </summary>
        public ValidationResult ValidateFileForCategory(string fileName, long fileSize, string category)
        {
            var result = new ValidationResult { IsValid = true };

            if (!IsCategoryValid(category))
            {
                result.IsValid = false;
                result.ErrorMessage = "無效的檔案類別。";
                return result;
            }

            var extension = Path.GetExtension(fileName).ToLowerInvariant();
            var allowedExtensions = GetAllowedExtensions(category);

            if (!allowedExtensions.Contains(extension))
            {
                result.IsValid = false;
                result.ErrorMessage = $"不支援的檔案格式。允許的格式：{GetAllowedExtensionsDisplay(category)}";
                return result;
            }

            var maxSize = GetMaxFileSize(category);
            if (fileSize > maxSize)
            {
                result.IsValid = false;
                result.ErrorMessage = $"檔案大小超過限制。最大允許：{GetFileSizeDisplay(category)}";
                return result;
            }

            return result;
        }

        /// <summary>
        /// 取得安全上傳指南
        /// </summary>
        public string GetSecurityGuidelines(string category)
        {
            var config = GetCategoryConfig(category);
            if (config == null) return "無效的檔案類別。";

            return $@"
檔案上傳安全指南：
• 允許的檔案格式：{GetAllowedExtensionsDisplay(category)}
• 最大檔案大小：{GetFileSizeDisplay(category)}
• 每次最多上傳：{GetMaxFilesPerUpload(category)} 個檔案
• 檔案將進行安全掃描，包括病毒檢測和內容驗證
• 檔案儲存在安全的非 Web 可存取目錄中
• 所有上傳操作都會記錄稽核日誌
";
        }
    }

    /// <summary>
    /// 檔案上傳類別配置
    /// </summary>
    public class FileUploadCategory
    {
        public string Name { get; set; } = string.Empty;
        public long MaxFileSize { get; set; }
        public string[] AllowedExtensions { get; set; } = Array.Empty<string>();
        public string[] AllowedMimeTypes { get; set; } = Array.Empty<string>();
        public int MaxFilesPerUpload { get; set; } = 1;
        public string Description { get; set; } = string.Empty;
    }
}
