/*!*  filename: sf-sidebar.min.js 
*     version : 26.1.41 
*     Copyright Syncfusion Inc. 2001 - 2024. All rights reserved. 
*     Use of this code is subject to the terms of our license.
*     A copy of the current license can be obtained at any time by e-mailing 
*     <EMAIL>. Any infringement will be prosecuted under 
*     applicable laws. 
*/
(window.webpackJsonp=window.webpackJsonp||[]).push([[52],{"./bundles/sf-sidebar.js":function(e,t,i){"use strict";i.r(t);i("./modules/sf-sidebar.js")},"./modules/sf-sidebar.js":function(e,t){window.sfBlazor=window.sfBlazor||{},window.sfBlazor.Sidebar=function(){"use strict";var e="Left",t=function(){function t(e,t,i,s){this.enableGestures=!0,this.isOpen=!1,this.closeOnDocumentClick=!1,this.isPositionChange=!1,this.dockSize="auto",this.width="auto",this.isSwipChange=!1,this.element=t,this.dataId=e,window.sfBlazor.setCompInstance(this),this.dotnetRef=i,this.resetProperty(s)}return t.prototype.initialize=function(){this.setTarget(),this.addClass(),this.setType(),this.setCloseOnDocumentClick(),this.setMediaQuery(),sf.base.Browser.isDevice&&(this.windowWidth=window.innerWidth),this.wireEvents()},t.prototype.addClass=function(){var e=document.querySelector(".e-main-content");sf.base.isNullOrUndefined(e||this.targetElement)||sf.base.addClass([e||this.targetElement],["e-content-animation"])},t.prototype.setTarget=function(){this.targetElement=this.element.nextElementSibling,"string"==typeof this.target&&(this.target=document.querySelector(this.target)),this.target&&(this.target.insertBefore(this.element,this.target.children[0]),sf.base.addClass([this.element],"e-sidebar-absolute"),sf.base.addClass([this.target],"e-sidebar-context"),this.targetElement=this.getTargetElement())},t.prototype.getTargetElement=function(){for(var e=this.element.nextElementSibling;!sf.base.isNullOrUndefined(e)&&e.classList.contains("e-sidebar");)e=e.nextElementSibling;return e},t.prototype.hide=function(){var t=this,i=document.querySelector(".e-main-content")||this.targetElement;!this.enableDock&&i&&(i.style.transform=i.classList.contains("e-sidebar")?"":"translateX(0px)",i.style[this.position===e?"marginLeft":"marginRight"]="0px"),this.destroyBackDrop(),this.isOpen=!1,this.sidebarOpened=!1,this.enableDock&&setTimeout((function(){return t.sidebarTimeout()}),50),sf.base.EventHandler.add(this.element,"transitionend",this.transitionEnd,this)},t.prototype.show=function(e){var t=this;e&&setTimeout((function(){return t.setType()}),50),this.isOpen=!0,this.sidebarOpened=!0,sf.base.EventHandler.add(this.element,"transitionend",this.transitionEnd,this)},t.prototype.transitionEnd=function(t){if(this.enableDock&&!this.isOpen){var i=this.position===e?"-100":"100",s=this.position===e?this.setDimension(this.dockSize):"-"+this.setDimension(this.dockSize),n="z-index: "+this.element.style.zIndex+"; width: "+this.element.style.width+";"+" transform: translateX("+i+"%) translateX("+s+")";this.element.setAttribute("style",n)}this.dotnetRef.invokeMethodAsync("SetDock"),sf.base.isNullOrUndefined(t)||this.dotnetRef.invokeMethodAsync("TriggerChange",this.isOpen,t),sf.base.EventHandler.remove(this.element,"transitionend",this.transitionEnd)},t.prototype.createBackDrop=function(e){var t=this.target;(this.backDropApplied=this.showBackdrop,this.resetProperty(e),this.showBackdrop&&this.sidebarOpened)?(this.backDropApplied&&this.destroyBackDrop(),this.modal=sf.base.createElement("div"),this.modal.className="e-sidebar-overlay",this.modal.style.display="block",this.target||t||!this.backDropApplied?(document.querySelector(".e-main-content")||this.targetElement).appendChild(this.modal):document.body.appendChild(this.modal)):this.destroyBackDrop()},t.prototype.destroyBackDrop=function(){(this.showBackdrop||this.backDropApplied)&&this.modal&&(this.modal.style.display="none",this.modal.outerHTML="",this.modal=null)},t.prototype.enableGestureHandler=function(t){!this.isOpen&&(this.position===e&&"Right"===t.swipeDirection&&t.startX<=20&&t.distanceX>=50&&t.velocity>=.5||"Right"===this.position&&t.swipeDirection===e&&window.innerWidth-t.startX<=20&&t.distanceX>=50&&t.velocity>=.5)?(this.eventArgs={left:this.getXYValue(event,"X"),top:this.getXYValue(event,"Y")},this.dotnetRef.invokeMethodAsync("TriggerShow",this.eventArgs),this.show(),this.isSwipChange=!0):(this.isOpen&&this.position===e&&t.swipeDirection===e||"Right"===this.position&&"Right"===t.swipeDirection)&&(this.eventArgs={left:this.getXYValue(event,"X"),top:this.getXYValue(event,"Y")},this.dotnetRef.invokeMethodAsync("TriggerHide",this.eventArgs),this.hide(),this.isSwipChange=!1)},t.prototype.resize=function(){this.setMediaQuery(),sf.base.Browser.isDevice&&(this.windowWidth=window.innerWidth)},t.prototype.setEnableGestures=function(e){this.resetProperty(e),this.enableGestures?(this.mainContentElement=new sf.base.Touch(document.body,{swipe:this.enableGestureHandler.bind(this)}),this.sidebarElement=new sf.base.Touch(this.element,{swipe:this.enableGestureHandler.bind(this)})):this.mainContentElement&&this.sidebarElement&&(this.mainContentElement.destroy(),this.sidebarElement.destroy())},t.prototype.wireEvents=function(){this.setEnableGestures(),window.addEventListener("resize",this.resize.bind(this))},t.prototype.unWireEvents=function(){window.removeEventListener("resize",this.resize.bind(this)),sf.base.EventHandler.remove(document,"mousedown touchstart",this.documentclickHandler),this.mainContentElement&&this.mainContentElement.destroy(),this.sidebarElement&&this.sidebarElement.destroy()},t.prototype.documentclickHandler=function(e){sf.base.closest(e.target,".e-control.e-sidebar")||(this.eventArgs={left:this.getXYValue(event,"X"),top:this.getXYValue(event,"Y")},this.closeOnDocumentClick&&this.dotnetRef.invokeMethodAsync("TriggerHide",this.eventArgs))},t.prototype.setCloseOnDocumentClick=function(e){this.resetProperty(e),this.closeOnDocumentClick?sf.base.EventHandler.add(document,"mousedown touchstart",this.documentclickHandler,this):sf.base.EventHandler.remove(document,"mousedown touchstart",this.documentclickHandler)},t.prototype.setMediaQuery=function(){this.mediaQuery&&this.windowWidth!==window.innerWidth&&(window.matchMedia(this.mediaQuery).matches?this.dotnetRef.invokeMethodAsync("TriggerShow",null):this.isOpen&&this.dotnetRef.invokeMethodAsync("TriggerHide",null))},t.prototype.setDimension=function(e){return e="number"==typeof e?sf.base.formatUnit(e):"string"==typeof e?e.match(/px|%|em/)?e:sf.base.formatUnit(e):"100%"},t.prototype.sidebarTimeout=function(){var e=document.querySelector(".e-main-content")||this.targetElement,t=this.isOpen?this.setDimension(this.width):this.setDimension(this.dockSize),i=this.setDimension(this.element.getBoundingClientRect().width);e&&(this.isOpen?this.positionStyles(this.width,e,i,t):this.element.classList.contains("e-close")&&this.positionStyles(this.dockSize,e,i,t))},t.prototype.positionStyles=function(t,i,s,n){this.position===e?i.style.marginLeft="auto"===t?s:n:i.style.marginRight="auto"===t?s:n},t.prototype.siblingStyle=function(t,i){t.style[this.position===e?"marginLeft":"marginRight"]=i},t.prototype.resetProperty=function(e){sf.base.isNullOrUndefined(e)||(this.type=e.Type,this.isOpen=e.IsOpen,this.isPositionChange=this.position!==e.Position,this.position=e.Position,this.enableDock=e.EnableDock,this.showBackdrop=e.ShowBackdrop,this.target=e.Target,this.enableGestures=e.EnableGestures,this.closeOnDocumentClick=e.CloseOnDocumentClick,this.mediaQuery=e.MediaQuery,this.dockSize=e.DockSize,this.width=e.Width)},t.prototype.getXYValue=function(e,t){var i,s=e.changedTouches;if(!(i="X"===t?s?s[0].clientX:e.clientX:s?s[0].clientY:e.clientY)&&"focus"===e.type&&e.target){var n=e.target.getBoundingClientRect();i=n?"X"===t?n.left:n.top:null}return Math.ceil(i)},t.prototype.setType=function(t){if(!sf.base.closest(this.element,".e-sidebarcontainer")){this.resetProperty(t);var i="auto"!==this.width&&this.enableDock?this.setDimension(this.width):this.element.getBoundingClientRect().width;i=this.enableDock&&!this.isOpen?this.dockSize:this.enableDock||this.isOpen?i:0;var s=document.querySelector(".e-main-content")||this.targetElement;if(s){this.isPositionChange&&(s.style[this.position===e?"marginRight":"marginLeft"]="0px"),s.style.transform=s.classList.contains("e-sidebar")?"":"translateX(0px)",sf.base.Browser.isDevice||"Auto"===this.type||"Over"===this.type||(s.style[this.position===e?"marginLeft":"marginRight"]="0px"),this.isPositionChange=!1;var n="string"==typeof i?i:i+"px",o=this.position===e?i:-i,r=s&&(this.enableDock||this.isOpen||this.isSwipChange);switch(this.type){case"Push":r&&this.siblingStyle(s,n);break;case"Slide":r&&(s.style.transform="translateX("+o+"px)",this.siblingStyle(s,n));break;case"Over":this.element.classList.contains("e-close")&&(this.enableDock?this.siblingStyle(s,this.dockSize):this.siblingStyle(s,"0px"));break;case"Auto":sf.base.Browser.isDevice?this.enableDock&&!this.isOpen&&this.siblingStyle(s,n):this.enableDock||this.isOpen||this.isSwipChange?this.siblingStyle(s,n):this.enableDock||this.isOpen||this.siblingStyle(s,n),this.isSwipChange=!1}}}},t.prototype.destroy=function(){this.destroyBackDrop(),this.element.style.width=this.element.style.zIndex=this.element.style.transform="",this.windowWidth=null,this.mediaQuery=null;var e=document.querySelector(".e-main-content")||this.targetElement;sf.base.isNullOrUndefined(e)||(e.style.margin=e.style.transform=""),this.unWireEvents()},t}();return{initialize:function(e,i,s,n){new t(e,i,s,n);var o=window.sfBlazor.getCompInstance(e);return i&&!sf.base.isNullOrUndefined(o)&&(o.initialize(),sf.base.removeClass([i],"e-hidden")),!(sf.base.Browser.isDevice||!sf.base.isNullOrUndefined(n.MediaQuery)&&!window.matchMedia(n.MediaQuery).matches)||!(!sf.base.Browser.isDevice||!n.IsOpen)},setType:function(e,t){var i=window.sfBlazor.getCompInstance(e);sf.base.isNullOrUndefined(i)||i.setType(t)},hide:function(e,t){var i=window.sfBlazor.getCompInstance(e);sf.base.isNullOrUndefined(i)||(i.setType(t),i.hide())},show:function(e,t,i){var s=window.sfBlazor.getCompInstance(e);sf.base.isNullOrUndefined(s)||(s.setType(t),s.show(i),s.createBackDrop(t))},onPropertyChange:function(e,t){var i=window.sfBlazor.getCompInstance(e);sf.base.isNullOrUndefined(i)||(void 0!==t.CloseOnDocumentClick&&i.setCloseOnDocumentClick(t),void 0!==t.ShowBackdrop&&i.createBackDrop(t))},destroy:function(e){var t=window.sfBlazor.getCompInstance(e);sf.base.isNullOrUndefined(t)||t.destroy()}}}()}}]);(async()=>{await import(`${document.baseURI}_content/Syncfusion.Blazor/scripts/syncfusion-blazor-base.min.js?v=26.1.undefined`).then(()=>{sfBlazor.loadDependencies('sfsidebar');})})();