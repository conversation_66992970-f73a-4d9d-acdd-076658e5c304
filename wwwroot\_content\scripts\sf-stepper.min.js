/*!*  filename: sf-stepper.min.js 
*     version : 26.1.41 
*     Copyright Syncfusion Inc. 2001 - 2024. All rights reserved. 
*     Use of this code is subject to the terms of our license.
*     A copy of the current license can be obtained at any time by e-mailing 
*     <EMAIL>. Any infringement will be prosecuted under 
*     applicable laws. 
*/
(window.webpackJsonp=window.webpackJsonp||[]).push([[60],{"./bundles/sf-stepper.js":function(e,t,s){"use strict";s.r(t);s("./modules/sf-stepper.js")},"./modules/sf-stepper.js":function(e,t){window.sfBlazor=window.sfBlazor||{},window.sfBlazor.Stepper=function(){"use strict";var e="e-indicator",t="e-step-text-container",s="e-step-selected",i="e-step-inprogress",o="e-step-notstarted",n="e-step-focus",r="e-step-completed",a=function(){function a(e){window.sfBlazor=window.sfBlazor,this.updateContext(e),window.sfBlazor.setCompInstance(this),this.stepperItemList=this.element.querySelector(".e-stepper-steps"),this.progressValue=this.element.querySelector(".e-progressbar-value"),this.progressbar=this.element.querySelector(".e-stepper-progressbar"),this.getElements(),this.bindEvent(),this.renderProgressBar(),this.navigateToStep(this.activeStep,!1,null)}return a.prototype.updateContext=function(e){sf.base.extend(this,this,e)},a.prototype.getElements=function(){this.liElements=this.element.querySelectorAll(".e-step-container")},a.prototype.bindItemEvent=function(e,t){var s=this;sf.base.EventHandler.add(e,"mouseover",(function(e){return s.openStepperTooltip(t,e)}),this),sf.base.EventHandler.add(e,"mouseleave",(function(){if(s.showTooltip)return s.closeTooltip()}),this),sf.base.EventHandler.add(e,"keydown",this.keyActionHandler.bind(this),this),sf.base.EventHandler.add(e,"click",(function(e){return s.linearModeHandler(e,t)}),this),sf.base.EventHandler.add(window,"resize",(function(){s.resizeHandler()}),this)},a.prototype.bindEvent=function(){var e=this;sf.base.EventHandler.add(window,"click",(function(){e.updateStepFocus()}),this);for(var t=0;t<this.liElements.length;t++)this.bindItemEvent(this.liElements[t],t)},a.prototype.unBindItemEvent=function(e,t){var s=this;sf.base.EventHandler.remove(e,"mouseover",(function(e){return s.openStepperTooltip(t,e)})),sf.base.EventHandler.remove(e,"mouseleave",(function(){if(s.showTooltip)return s.closeTooltip()})),sf.base.EventHandler.remove(e,"keydown",this.keyActionHandler),sf.base.EventHandler.remove(e,"click",(function(e){return s.linearModeHandler(e,t)})),sf.base.EventHandler.remove(window,"resize",(function(){s.resizeHandler()}))},a.prototype.unBindEvent=function(){var e=this;sf.base.EventHandler.remove(window,"click",(function(){e.updateStepFocus()}));for(var t=0;t<this.liElements.length;t++)this.unBindItemEvent(this.liElements[t],t)},a.prototype.linearModeHandler=function(e,t){if(this.linear){var s=t-this.activeStep;1===Math.abs(s)&&this.stepClickHandler(t,e)}else this.stepClickHandler(t,e)},a.prototype.resizeHandler=function(){if(this.stepperItemList&&this.progressbar&&this.element.classList.contains("e-horizontal"))return this.renderProgressBar()},a.prototype.stepClickHandler=function(e,t){this.readOnly||(this.dotNetRef.invokeMethodAsync("StepClickHandler",this.activeStep,e),this.navigateToStep(e,!0,t))},a.prototype.updateStepFocus=function(){if(this.isKeyNavFocus){this.isKeyNavFocus=!1;var e=this.element.querySelector("."+n);e&&(e.classList.remove(n),this.element.classList.remove("e-steps-focus"))}},a.prototype.renderProgressBar=function(){if(this.element.classList.contains("e-horizontal")){var s=this.element.querySelector(".e-step-container"),i=s.firstChild,o=this.stepperItemList.lastChild.firstChild,n=s.querySelector("."+t),r=s.querySelector(".e-step-label-container");if(!s.classList.contains("e-step-item")&&(n&&!n.classList.contains("e-text")||r&&!r.classList.contains("e-label")))n?this.progressbar.style.setProperty("--progress-top-position",n.querySelector(".e-text").offsetHeight/2+5+"px"):this.progressbar.style.setProperty("--progress-top-position",r.querySelector(".e-label").offsetHeight/2+5+"px");else{var a=0;a=this.element.classList.contains("e-label-before")?this.stepperItemList.offsetHeight-i.offsetHeight/2-1:i.offsetHeight/2,this.progressbar.style.setProperty("--progress-top-position",a+"px")}if(this.element.classList.contains("e-rtl")){var l=i.offsetLeft+i.offsetWidth-this.stepperItemList.offsetWidth;this.progressbar.style.setProperty("--progress-left-position",Math.abs(l)+"px"),this.progressbar.style.setProperty("--progress-bar-width",Math.abs(o.offsetLeft-i.offsetLeft)+"px")}else this.progressbar.style.setProperty("--progress-left-position",i.offsetLeft+1+"px"),this.progressbar.style.setProperty("--progress-bar-width",o.offsetWidth+o.offsetLeft-2-(i.offsetLeft+2)+"px")}else{this.progressBarPosition=this.beforeLabelWidth=this.textEleWidth=0;for(var p=!!this.element.classList.contains("e-label-before"),h=0;h<this.liElements.length;h++){n=this.liElements[h].querySelector("."+t);var c=!(!this.liElements[h].classList.contains("e-step-item")||this.liElements[h].classList.contains("e-step-text")||this.liElements[h].classList.contains("e-step-label"));if(n&&(this.textEleWidth=this.textEleWidth<n.offsetWidth?n.offsetWidth:this.textEleWidth),p){var d=void 0,f=this.liElements[h].querySelector(".e-label").offsetWidth+15;this.beforeLabelWidth<f&&(this.beforeLabelWidth=f),this.element.querySelector("ol").lastChild.querySelector("."+e)?d=this.beforeLabelWidth+this.liElements[h].querySelector("."+e).offsetWidth/2:this.liElements[h].querySelector("."+t)&&(d=this.beforeLabelWidth+this.liElements[h].querySelector("."+t).offsetWidth/2),this.progressBarPosition<d&&(this.progressBarPosition=d)}else this.progressBarPosition<(c?this.liElements[h].offsetWidth:this.element.querySelector("ol").lastChild.firstChild.offsetWidth)&&(this.progressBarPosition=c?this.liElements[h].offsetWidth:this.element.querySelector("ol").lastChild.firstChild.offsetWidth)}var u=this.element.querySelector("li").querySelector(".e-step-label-container");if(this.element.classList.contains("e-label-bottom")||this.element.classList.contains("e-label-top")?this.progressbar.style.setProperty("--progress-position",this.stepperItemList.offsetWidth/2+"px"):this.progressbar.style.setProperty("--progress-position",this.progressBarPosition/2-1+"px"),u&&u.classList.contains("e-label-before")){var v=this.stepperItemList.querySelectorAll(".e-label");for(h=0;h<v.length;h++){(r=v[parseInt(h.toString(),10)]).style.setProperty("--label-width",this.beforeLabelWidth+"px")}this.progressbar.style.setProperty("--progress-position",this.progressBarPosition-1+"px")}}},a.prototype.updateStepperStatus=function(){for(var e=0;e<this.liElements.length;e++)if(this.stepperStatus&&this.statusIndex===this.activeStep){var t=this.liElements[e];t.classList.remove(s,i,r,o),this.updateStatusClass(e,this.statusIndex,t,"completed"===this.stepperStatus.toLowerCase()?null:"inprogress"===this.stepperStatus.toLowerCase())}},a.prototype.updateStatusClass=function(e,t,n,a){e<t?n.classList.add(r):e===t?null==a?n.classList.add(r,s):a?n.classList.add(i,s):n.classList.add(o):n.classList.add(o)},a.prototype.navigateToStep=function(e,t,s){var i=this;if(!1!==t){var o=this.activeStep,n={cancel:!1,isInteracted:!0,previousStep:this.activeStep,activeStep:e,element:this.element,event:s};this.dotNetRef.invokeMethodAsync("StepChangingHandler",n).then((function(s){s.cancel?(i.navigationHandler(i.activeStep),i.updateStepperStatus()):(i.navigationHandler(e),i.updateStepperStatus(),i.dotNetRef.invokeMethodAsync("StepChangedHandler",t,o,i.activeStep))}))}else this.navigationHandler(e),this.updateStepperStatus()},a.prototype.navigationHandler=function(t){t=t>=this.liElements.length-1?this.liElements.length-1:t;var n=this.liElements.length;t>=0&&t<n&&(t=this.liElements[parseInt(t.toString(),10)].classList.contains("e-step-disabled")?this.activeStep:t),this.activeStep=t;for(var a=0;a<this.liElements.length;a++){var l=this.liElements[a];if(l.classList.remove(s,i,r,o),a===this.activeStep&&l.classList.add(s),this.activeStep>=0&&this.progressValue)if(this.element.classList.contains("e-horizontal"))if((this.element.classList.contains("e-label-before")||this.element.classList.contains("e-label-after"))&&!this.element.classList.contains("e-step-type-indicator")&&this.liElements[this.activeStep].classList.contains("e-step-item")){var p=this.liElements[this.activeStep].firstChild,h=0===this.activeStep?0:(p.offsetLeft-this.progressbar.offsetLeft+p.offsetWidth/2)/this.progressbar.offsetWidth*100;this.element.classList.contains("e-rtl")?(h=(this.progressbar.getBoundingClientRect().right-p.getBoundingClientRect().right+p.offsetWidth/2)/this.progressbar.offsetWidth*100,this.progressValue.style.setProperty("--progress-value",h+"%")):this.progressValue.style.setProperty("--progress-value",h+"%")}else{for(var c=0,d=0,f=0;f<this.liElements.length;f++)c+=this.liElements[f].offsetWidth,f<=this.activeStep&&(f<this.activeStep?d+=this.liElements[f].offsetWidth:f===this.activeStep&&0!==f&&(d+=this.liElements[f].offsetWidth/2));var u=(d+(this.stepperItemList.offsetWidth-c)/(this.liElements.length-1)*this.activeStep)/this.stepperItemList.offsetWidth*100;this.progressValue.style.setProperty("--progress-value",u+"%")}else this.progressValue.style.setProperty("--progress-value",100/(this.liElements.length-1)*t+"%");else this.activeStep<0&&this.progressValue&&this.progressValue.style.setProperty("--progress-value","0%");a===this.activeStep?l.classList.add(i):this.activeStep>0&&a<this.activeStep?l.classList.add(r):l.classList.add(o),l.classList.contains(i)?sf.base.attributes(l,{tabindex:"0","aria-current":"true"}):sf.base.attributes(l,{tabindex:"-1","aria-current":"false"}),this.element.classList.contains("e-step-type-indicator")&&this.isDefaultStep&&!l.classList.contains("e-step-valid")&&!l.classList.contains("e-step-error")&&(l.classList.contains(r)?(l.firstChild.classList.remove("e-icons","e-step-indicator"),l.firstChild.classList.add(e,"e-icons","e-check")):(l.classList.contains(i)||l.classList.contains(o))&&(l.firstChild.classList.remove(e,"e-icons","e-check"),l.firstChild.classList.add("e-icons","e-step-indicator")))}},a.prototype.openStepperTooltip=function(e,t){var s=this.tooltipContent[e];if(this.showTooltip&&s){var i=t.target.classList.contains("e-step-container")?t.target:t.target.closest(".e-step-container");this.element.querySelector(".e-stepper-tooltip").classList.contains("e-show-tooltip")||(this.tooltipTemplate?this.dotNetRef.invokeMethodAsync("TooltipHandler",!0,e):this.openTooltip(i,s))}},a.prototype.openTooltip=function(e,t){if(e){var s=this.element.querySelector(".e-stepper-tooltip");s.querySelector(".e-tip-content").innerHTML=t,e.appendChild(s),s.classList.add("e-show-tooltip")}},a.prototype.closeTooltip=function(){var e=this.element.querySelector(".e-stepper-tooltip");e&&e.classList.remove("e-show-tooltip"),this.dotNetRef.invokeMethodAsync("TooltipHandler",!1,0)},a.prototype.keyActionHandler=function(e){if(!this.readOnly)switch(e.key){case"ArrowUp":case"ArrowDown":case"ArrowLeft":case"ArrowRight":case"Tab":this.handleNavigation(this.enableRtl&&this.element.classList.contains("e-horizontal")?"ArrowLeft"===e.key||e.shiftKey&&"Tab"===e.key||"ArrowUp"===e.key:"ArrowRight"===e.key||"Tab"===e.key&&!e.shiftKey||"ArrowDown"===e.key,e);break;case" ":case"Enter":case"Escape":this.handleNavigation(null,e);break;case"Home":case"End":this.handleNavigation(null,e,this.enableRtl)}},a.prototype.handleNavigation=function(e,t,i){this.isKeyNavFocus=!0,this.element.classList.add("e-steps-focus");var o=this.element.querySelector("."+n);o||(o=this.element.querySelector("."+s));var r=Array.prototype.slice.call(this.stepperItemList.children),a=r.indexOf(o);if("Tab"===t.key||"ArrowDown"===t.key||"ArrowUp"===t.key||" "===t.key||"Home"===t.key||"End"===t.key)if("Tab"===t.key&&!t.shiftKey&&a===r.length-1||"Tab"===t.key&&t.shiftKey&&0===a){if(o.classList.contains(n))return void this.updateStepFocus()}else t.preventDefault();if("Escape"===t.key&&(r[parseInt(a.toString(),10)].classList.remove(n),this.element.classList.remove("e-steps-focus"))," "!==t.key&&"Enter"!==t.key){var l=a;for(a=e?a+1:a-1;a>=0&&a<r.length&&r[parseInt(a.toString(),10)].classList.contains("e-step-disabled");)a=e?a+1:a-1;a=a<0?0:a>r.length-1?r.length-1:a,r[parseInt(l.toString(),10)].classList.contains(n)&&r[parseInt(l.toString(),10)].classList.remove(n),"Home"!==t.key&&"End"!==t.key||(a="Home"===t.key?i?r.length-1:0:i?0:r.length-1),a>=0&&a<r.length&&r[parseInt(a.toString(),10)].classList.add(n)}else if(" "===t.key||"Enter"===t.key){var p=!1;if(this.linear){var h=this.activeStep-a;1===Math.abs(h)&&(this.navigateToStep(a,!0,null),p=!0)}else this.navigateToStep(a,!0,null),p=!0;p&&(this.updateStepFocus(),this.liElements[a].focus())}},a.prototype.updateLabelClass=function(e){var t=this.element.classList.value.match(/(e-label-[after|before|start|end|top|bottom]+)/g);t&&sf.base.removeClass([this.element],t),this.element.classList.add(e)},a.prototype.updateStepLength=function(e,t){var s=this.liElements.length;if(!e)for(var i=s-t;i<s;i++)this.unBindItemEvent(this.liElements[i],i);if(this.getElements(),e)for(i=s;i<this.liElements.length;i++)this.bindItemEvent(this.liElements[i],i);this.navigationHandler(this.activeStep)},a.prototype.destroy=function(){this.unBindEvent()},a}();return{initialize:function(e){e.dataId&&(e.showLabelClass&&e.element.classList.add(e.showLabelClass),new a(e))},updateStepperProps:function(e){if(e.dataId){var t=window.sfBlazor.getCompInstance(e.dataId);t.updateContext(e),t.getElements(),e.showLabelClass&&t.updateLabelClass(e.showLabelClass),e.stepNavigation&&t.navigateToStep(e.activeStep,!0,null)}},updateDynamicStepperProps:function(e){if(e.dataId){var t=window.sfBlazor.getCompInstance(e.dataId);t.updateContext(e),t.getElements(),e.showLabelClass&&t.updateLabelClass(e.showLabelClass),t.renderProgressBar(),t.navigateToStep(e.activeStep,!0,null)}},updateStepperValue:function(e,t,s){window.sfBlazor.getCompInstance(e).navigateToStep(t,s,null)},updateLinear:function(e){e.dataId&&window.sfBlazor.getCompInstance(e.dataId).updateContext(e)},updateStepLength:function(e,t,s){window.sfBlazor.getCompInstance(e).updateStepLength(t,s)},refreshProgressbar:function(e,t){if(e){var s=window.sfBlazor.getCompInstance(e);s.renderProgressBar(),s.navigateToStep(t,!1,null)}},destroy:function(e){e&&window.sfBlazor.getCompInstance(e).destroy()}}}()}}]);(async()=>{await import(`${document.baseURI}_content/Syncfusion.Blazor/scripts/syncfusion-blazor-base.min.js?v=26.1.undefined`).then(()=>{sfBlazor.loadDependencies('sfstepper');})})();