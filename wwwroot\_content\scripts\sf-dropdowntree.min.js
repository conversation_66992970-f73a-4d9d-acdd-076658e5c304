/*!*  filename: sf-dropdowntree.min.js 
*     version : 26.1.41 
*     Copyright Syncfusion Inc. 2001 - 2024. All rights reserved. 
*     Use of this code is subject to the terms of our license.
*     A copy of the current license can be obtained at any time by e-mailing 
*     <EMAIL>. Any infringement will be prosecuted under 
*     applicable laws. 
*/
(window.webpackJsonp=window.webpackJsonp||[]).push([[25],{"./bundles/sf-dropdowntree.js":function(e,t,s){"use strict";s.r(t);s("./modules/sf-dropdowntree.js")},"./modules/sf-dropdowntree.js":function(e,t){window.sfBlazor=window.sfBlazor||{},window.sfBlazor.DropDownTree=function(){"use strict";var e="e-input-focus",t="e-icon-hide",s="e-show-chip",i="e-chip-input",o=function(){function o(e,t,s,i,o){window.sfBlazor=window.sfBlazor,this.dataId=e,this.inputWrapper=t,this.element=sf.base.select(".e-dropdowntree",t),this.options=i,window.sfBlazor.setCompInstance(this),this.dotNetRef=s,this.uniqueID=o}return o.prototype.initialize=function(){this.keyConfigs={escape:"escape",altUp:"alt+uparrow",altDown:"alt+downarrow",tab:"tab",shiftTab:"shift+tab",space:"space",moveDown:"downarrow"},this.options.showClearButton&&(this.overAllClear=sf.base.select(".e-clear-icon",this.inputWrapper)),this.createOverFlowWrapper(),this.setDisable(),this.wireEvents()},o.prototype.createOverFlowWrapper=function(){(this.options.allowMultiSelection||this.options.showCheckBox)&&("Delimiter"!==this.options.mode&&this.createChip(),this.options.textWrap||"Custom"===this.options.mode||(this.overFlowWrapper=sf.base.select(".e-overflow",this.inputWrapper),this.inputWrapper.insertBefore(this.overFlowWrapper,this.element),"Box"!==this.options.mode&&sf.base.addClass([this.overFlowWrapper],"e-show-text")))},o.prototype.renderPopup=function(){sf.base.addClass([this.inputWrapper],["e-icon-anim"]),this.popupEle=sf.base.select("#"+this.element.id+"_options_"+this.uniqueID),this.keyboardModule=new sf.base.KeyboardEvents(this.popupEle,{keyAction:this.popupKeyActionHandler.bind(this),keyConfigs:this.keyConfigs,eventName:"keydown"}),document.body.appendChild(this.popupEle),this.createPopup(this.popupEle),(this.options.allowMultiSelection||this.options.showCheckBox)&&"Delimiter"!==this.options.mode&&this.createChip(),sf.base.removeClass([this.popupEle],"e-ddt-icon-hide"),this.options.allowFiltering&&(this.filterContainer=sf.base.select(".e-filter-wrap",this.popupEle)),this.options.showCheckBox&&this.options.showSelectAll&&!this.popupDiv.classList.contains("e-no-data")&&(this.checkAllParent=sf.base.select(".e-selectall-parent"),this.checkBoxElement=sf.base.select(".e-checkbox-wrapper",this.checkAllParent)),sf.base.attributes(this.element,{"aria-expanded":"true"}),this.popupObj.show(null,1e3===this.options.zIndex?this.element:null),sf.base.removeClass([this.popupEle],"e-ddt-icon-hide"),this.updatePopupHeight(),this.popupObj.refreshPosition();var t=this.popupDiv.querySelectorAll("li");this.options.showCheckBox&&this.options.showSelectAll||this.popupDiv.classList.contains("e-no-data")||!(t.length>0)||this.popupDiv.querySelector("li").focus();this.options.allowFiltering&&(sf.base.removeClass([this.inputWrapper],[e]),this.filterInputEle=sf.base.select("#"+this.element.id+"_filter_"+this.uniqueID,this.filterContainer),this.filterInputEle.focus())},o.prototype.removeChip=function(e){var t=e.target.parentElement?e.target.parentElement.getAttribute("data-value"):"";this.dotNetRef.invokeMethodAsync("RemoveChip",t)},o.prototype.updateView=function(){"Custom"===this.options.mode||this.inputFocus||("Box"!==this.options.mode?sf.base.addClass([this.inputWrapper,this.overFlowWrapper],"e-show-text"):sf.base.addClass([this.inputWrapper],s),this.options.value&&0!==this.options.value.length?(this.inputWrapper.contains(this.chipWrapper)&&(this.chipWrapper.style.display="",sf.base.addClass([this.chipWrapper],t)),sf.base.addClass([this.element],i),this.updateOverFlowView(),this.ensurePlaceHolder()):(this.overFlowWrapper.innerHTML="",sf.base.addClass([this.overFlowWrapper],t),sf.base.removeClass([this.element],i)))},o.prototype.updateOverFlowView=function(){var e;if(this.overFlowWrapper.classList.remove("e-total-count"),sf.base.removeClass([this.overFlowWrapper],t),this.options.showClearButton&&(e=sf.base.select(".e-clear-icon",this.inputWrapper).offsetWidth),this.options.value&&this.options.value.length){var s,i="",o=void 0,p=void 0,a=void 0,r=1,n=void 0,l=void 0;this.overFlowWrapper.innerHTML="";var h=sf.base.createElement("span",{className:"e-remain"});this.overFlowWrapper.appendChild(h),h.innerText="+${count} more..".replace("${count}",this.options.value.length.toString());var u=h.offsetWidth;if(sf.base.remove(h),s=sf.base.select(".e-ddt-icon",this.inputWrapper).offsetWidth,!sf.base.isNullOrUndefined(this.options.value))if("Box"!=this.options.mode)for(var c=this.element.value.split(this.options.delimiterChar+" "),d=0;!sf.base.isNullOrUndefined(c[d]);d++){if(i+=0===d?"":this.options.delimiterChar+" ",i+=p=c[d],p=this.overFlowWrapper.innerHTML,this.overFlowWrapper.innerHTML=i,(n=this.overFlowWrapper.offsetWidth)+s+e>(o=this.inputWrapper.offsetWidth)){for(void 0!==a&&""!==a&&(p=a,d=r+1),this.overFlowWrapper.innerHTML=p,l=this.options.value.length-d,n=this.overFlowWrapper.offsetWidth;n+u+s+e>=o&&0!==n&&""!==this.overFlowWrapper.innerHTML;){var f=this.overFlowWrapper.innerHTML.split(this.options.delimiterChar);f.pop(),this.overFlowWrapper.innerHTML=f.join(this.options.delimiterChar),l++,n=this.overFlowWrapper.offsetWidth}break}n+u+s+e<=o?(a=i,r=d):0===d&&(a="",r=-1)}else{sf.base.addClass([this.chipWrapper],t),sf.base.addClass([this.overFlowWrapper],"e-chip-list");for(var v=this.chipWrapper.cloneNode(!0),b=sf.base.selectAll(".e-chips",v),w=0;w<b.length;w++){if(p=this.overFlowWrapper.innerHTML,this.overFlowWrapper.appendChild(b[w]),i=this.overFlowWrapper.innerHTML,(n=this.overFlowWrapper.offsetWidth)+s+e>(o=this.inputWrapper.offsetWidth)){for(void 0!==a&&""!==a&&(p=a,w=r+1),this.overFlowWrapper.innerHTML=p,l=this.options.value.length-w,n=this.overFlowWrapper.offsetWidth;n+u+s+e>=o&&0!==n&&""!==this.overFlowWrapper.innerHTML;)this.overFlowWrapper.removeChild(this.overFlowWrapper.lastChild),l++,n=this.overFlowWrapper.offsetWidth;break}n+u+s+e<=o?(a=i,r=w):0===w&&(a="",r=-1)}var W=sf.base.selectAll(".e-chips",this.overFlowWrapper);for(w=0;w<W.length;w++){var C=sf.base.select(".e-chips-close",W[w]);sf.base.EventHandler.add(C,"mousedown",this.removeChip,this)}}l>0&&this.overFlowWrapper.appendChild(this.updateRemainTemplate(h,l,"+${count} more..","${count} selected")),"Box"!==this.options.mode||this.overFlowWrapper.classList.contains("e-total-count")||sf.base.addClass([h],"e-wrap-count")}else this.overFlowWrapper.innerHTML="",sf.base.addClass([this.overFlowWrapper],t)},o.prototype.updateRemainTemplate=function(e,t,s,i){return this.overFlowWrapper.firstChild&&3===this.overFlowWrapper.firstChild.nodeType&&""===this.overFlowWrapper.firstChild.nodeValue&&this.overFlowWrapper.removeChild(this.overFlowWrapper.firstChild),e.innerHTML="",e.innerText=!this.overFlowWrapper.firstChild||3!==this.overFlowWrapper.firstChild.nodeType&&"Box"!==this.options.mode?i.replace("${count}",t.toString()):s.replace("${count}",t.toString()),!this.overFlowWrapper.firstChild||3!==this.overFlowWrapper.firstChild.nodeType&&"Box"!==this.options.mode?(sf.base.addClass([this.overFlowWrapper],"e-total-count"),sf.base.removeClass([this.overFlowWrapper],"e-wrap-count")):sf.base.removeClass([this.overFlowWrapper],"e-total-count"),e},o.prototype.setDisable=function(){this.options.disabled?(this.isPopupOpen&&this.invokePopupEvent(),this.inputWrapper&&this.inputWrapper.classList.contains(e)&&sf.base.removeClass([this.inputWrapper],[e]),this.element.setAttribute("aria-disabled","true")):this.element.setAttribute("aria-disabled","false")},o.prototype.createChip=function(){this.inputWrapper.contains(this.chipWrapper)||(this.chipWrapper=sf.base.select(".e-chips-wrapper",this.inputWrapper),this.inputWrapper.insertBefore(this.chipWrapper,this.element),sf.base.addClass([this.inputWrapper],s),this.getValidMode()&&null!==this.options.value&&this.options.value&&0!==this.options.value.length?sf.base.addClass([this.element],i):(null===this.options.value||this.options.value&&0===this.options.value.length||this.checkBoxElement)&&sf.base.addClass([this.chipWrapper],t))},o.prototype.getValidMode=function(){return!(!this.options.allowMultiSelection&&!this.options.showCheckBox)&&("Box"===this.options.mode||!("Default"!==this.options.mode||!this.inputFocus))},o.prototype.createPopup=function(e){var t=this;this.popupObj=new sf.popups.Popup(e,{width:this.setWidth(),targetType:"relative",collision:{X:"flip",Y:"flip"},relateTo:this.inputWrapper,zIndex:this.options.zIndex,enableRtl:!sf.base.isNullOrUndefined(sf.base.select(".e-rtl")),position:{X:"left",Y:"bottom"},close:function(){t.isPopupOpen=!1,t.dotNetRef.invokeMethodAsync("UpdatePopupState",t.isPopupOpen)},open:function(){t.isPopupOpen=!0,t.dotNetRef.invokeMethodAsync("UpdatePopupState",t.isPopupOpen)},targetExitViewport:function(){sf.base.Browser.isDevice||t.invokePopupEvent()}})},o.prototype.getHeight=function(){var e=sf.base.formatUnit(this.options.popupHeight);return e.indexOf("%")>-1&&(e=(document.documentElement.clientHeight*parseFloat(e)/100).toString()+"px"),e},o.prototype.updatePopupHeight=function(){var e=this.getHeight();this.popupEle.style.maxHeight=e;var t=sf.base.select(".e-ddt-header",this.popupEle),s=sf.base.select(".e-ddt-footer",this.popupEle);if(this.options.allowFiltering){var i=Math.round(this.filterContainer.getBoundingClientRect().height);e=sf.base.formatUnit(parseInt(e,10)-i+"px")}if(t){i=Math.round(t.getBoundingClientRect().height);e=sf.base.formatUnit(parseInt(e,10)-i+"px")}if(this.options.showCheckBox&&this.options.showSelectAll&&!this.popupDiv.classList.contains("e-no-data")){i=Math.round(this.checkAllParent.getBoundingClientRect().height);e=sf.base.formatUnit(parseInt(e,10)-i+"px")}if(s){i=Math.round(s.getBoundingClientRect().height);e=sf.base.formatUnit(parseInt(e,10)-i+"px")}var o=parseInt(window.getComputedStyle(this.popupEle).borderTopWidth,10);o+=parseInt(window.getComputedStyle(this.popupEle).borderBottomWidth,10),e=sf.base.formatUnit(parseInt(e,10)-o+"px"),this.popupDiv.style.maxHeight=e},o.prototype.setWidth=function(){var e=sf.base.formatUnit(this.options.popupWidth);return e.indexOf("%")>-1&&(e=(this.inputWrapper.offsetWidth*parseFloat(e)/100).toString()+"px"),e},o.prototype.onDocumentClick=function(e){var t=e.target,s=sf.base.closest(t,".e-list-parent"),i=sf.base.closest(t,".e-filter-wrap"),o=sf.base.closest(t,".e-ddt-header"),p=sf.base.closest(t,".e-ddt-footer"),a=!!t.classList.contains("e-dropdown")||(sf.base.matches(t,".e-ddt .e-popup")||sf.base.matches(t,".e-ddt .e-treeview"));(this.overAllClear&&t==this.overAllClear||t.classList.contains("e-chips-close"))&&(this.isClearIconClick=!0),this.isPopupOpen&&(!sf.base.isNullOrUndefined(this.inputWrapper)&&this.inputWrapper.contains(t)||s||a||o||p)||(this.options.allowMultiSelection||this.options.showCheckBox)&&(this.isPopupOpen&&t.classList.contains("e-chips-close")||this.isPopupOpen&&(t.classList.contains("e-selectall-parent")||t.classList.contains("e-all-text")||t.classList.contains("e-frame")||t.classList.contains("e-label")))?e.preventDefault():sf.base.isNullOrUndefined(this.inputWrapper)||this.inputWrapper.contains(t)||!this.inputFocus||this.focusOut(e,!sf.base.isNullOrUndefined(i))},o.prototype.wireEvents=function(){sf.base.EventHandler.add(this.inputWrapper,"focus",this.focusIn,this),sf.base.EventHandler.add(this.inputWrapper,"blur",this.focusOut,this),sf.base.EventHandler.add(document,"mousedown",this.onDocumentClick,this),this.keyboardModule=new sf.base.KeyboardEvents(this.inputWrapper,{keyAction:this.inputKeyActionHandler.bind(this),keyConfigs:this.keyConfigs,eventName:"keydown"}),window.addEventListener("resize",this.onWindowResize.bind(this))},o.prototype.unWireEvents=function(){sf.base.EventHandler.remove(this.inputWrapper,"focus",this.focusIn),sf.base.EventHandler.remove(this.inputWrapper,"blur",this.focusOut),sf.base.EventHandler.remove(document,"mousedown",this.onDocumentClick),window.removeEventListener("resize",this.onWindowResize.bind(this)),this.keyboardModule&&this.keyboardModule.destroy()},o.prototype.onWindowResize=function(){this.isPopupOpen&&(this.popupObj.setProperties({width:this.setWidth()}),this.popupObj.refreshPosition())},o.prototype.inputKeyActionHandler=function(e){switch(e.action){case"escape":case"altUp":this.isPopupOpen&&this.invokePopupEvent();break;case"shiftTab":case"tab":this.isPopupOpen&&this.invokePopupEvent(),this.inputFocus&&this.focusOut(e);break;case"altDown":this.isPopupOpen||(this.dotNetRef.invokeMethodAsync("InvokePopupEvent",null),e.preventDefault());break;case"moveDown":this.options.showSelectAll&&this.options.showCheckBox&&this.checkAllParent.focus()}},o.prototype.popupKeyActionHandler=function(e){switch(e.target){case this.filterInputEle:this.filterAction(e);break;case this.checkAllParent:this.checkAllAction(e);break;default:this.popupDiv.contains(e.target)&&this.treeAction(e)}},o.prototype.checkAllAction=function(e){switch(e.action){case"space":this.dotNetRef.invokeMethodAsync("OnSelectAllClick");break;case"moveDown":this.popupDiv.querySelector("li").focus();break;case"shiftTab":e.preventDefault(),this.options.allowFiltering?this.filterInputEle.focus():this.inputWrapper.focus()}},o.prototype.treeAction=function(e){switch(e.action){case"escape":case"altUp":this.inputWrapper.focus(),e.preventDefault(),this.isPopupOpen&&this.invokePopupEvent();break;case"tab":this.isPopupOpen&&this.invokePopupEvent();break;case"shiftTab":e.preventDefault(),this.options.showSelectAll&&this.options.showCheckBox?this.checkAllParent.focus():this.options.allowFiltering?this.filterInputEle.focus():this.inputWrapper.focus()}},o.prototype.filterAction=function(e){switch(e.action){case"escape":case"altUp":this.inputWrapper.focus(),e.preventDefault(),this.isPopupOpen&&this.invokePopupEvent();break;case"shiftTab":this.inputFocus=!1,e.preventDefault(),this.inputWrapper.focus();break;case"tab":if(this.options.showSelectAll&&this.options.showCheckBox)this.checkAllParent.focus();else this.popupDiv.querySelector("li").focus(),e.preventDefault()}},o.prototype.showPopup=function(){this.options.disabled||this.isPopupOpen||(this.focusIn(),this.renderPopup())},o.prototype.focusIn=function(o){this.options.disabled||this.inputFocus||this.isClearIconClick?this.isClearIconClick=!1:(this.inputFocus=!0,sf.base.addClass([this.inputWrapper],[e]),(this.options.allowMultiSelection||this.options.showCheckBox)&&("Delimiter"!==this.options.mode&&this.inputFocus&&(this.chipWrapper&&this.options.value&&0!==this.options.value.length&&(sf.base.removeClass([this.chipWrapper],t),sf.base.addClass([this.element],i)),sf.base.addClass([this.inputWrapper],s)),this.options.textWrap||"Custom"===this.options.mode||(this.inputWrapper.contains(this.overFlowWrapper)&&sf.base.addClass([this.overFlowWrapper],t),"Delimiter"===this.options.mode?(sf.base.removeClass([this.inputWrapper],s),sf.base.removeClass([this.element],i)):sf.base.addClass([this.inputWrapper],s),sf.base.removeClass([this.inputWrapper],"e-show-text"),this.ensurePlaceHolder()),this.popupObj&&this.popupObj.refreshPosition()))},o.prototype.focusOut=function(e,t){!this.options.disabled&&this.inputFocus&&(!sf.base.Browser.isIE&&"edge"!==sf.base.Browser.info.name||e.target!==this.inputWrapper)&&(e.target===this.inputWrapper&&this.isPopupOpen||this.onFocusOut(t))},o.prototype.onFocusOut=function(o){void 0===o&&(o=!1),this.inputFocus=o,this.isPopupOpen&&!o&&this.invokePopupEvent(),this.overAllClear&&!this.overAllClear.classList.contains(t)&&(sf.base.addClass([this.overAllClear],t),sf.base.removeClass([this.inputWrapper],"e-show-clear")),sf.base.removeClass([this.inputWrapper],[e]),(this.options.allowMultiSelection||this.options.showCheckBox)&&("Delimiter"!==this.options.mode&&"Custom"!==this.options.mode&&this.chipWrapper&&"Default"===this.options.mode&&(this.chipWrapper.style.display="",sf.base.addClass([this.chipWrapper],t),sf.base.removeClass([this.inputWrapper],s),sf.base.removeClass([this.element],i)),!this.options.textWrap&&this.options.value&&this.options.value.length&&this.updateView())},o.prototype.invokePopupEvent=function(){var e={offsetX:this.popupObj.offsetX,offsetY:this.popupObj.offsetY,targetType:this.popupObj.targetType,collision:{X:this.popupObj.collision.X,Y:this.popupObj.collision.Y},position:{X:this.popupObj.position.X,Y:this.popupObj.position.Y}};this.dotNetRef.invokeMethodAsync("InvokePopupEvent",e)},o.prototype.closePopup=function(){this.inputWrapper.classList.remove("e-icon-anim"),this.popupEle&&sf.base.addClass([this.popupEle],"e-ddt-icon-hide"),sf.base.attributes(this.element,{"aria-expanded":"false"}),this.popupObj&&this.isPopupOpen&&(this.popupObj.hide(),this.popupObj.destroy(),this.popupObj=null,this.inputFocus&&(this.inputWrapper.focus(),this.options.allowFiltering&&sf.base.addClass([this.inputWrapper],[e])))},o.prototype.showOverAllClear=function(){this.options.disabled||(this.options.showClearButton&&(this.overAllClear=sf.base.select(".e-clear-icon",this.inputWrapper)),this.overAllClear&&(!!this.options.value&&!!this.options.value.length&&this.options.showClearButton?(sf.base.removeClass([this.overAllClear],[t,"e-clear-icon-hide"]),sf.base.addClass([this.inputWrapper],"e-show-clear")):(sf.base.addClass([this.overAllClear],t),sf.base.removeClass([this.inputWrapper],"e-show-clear"))))},o.prototype.onNodeSelected=function(e){this.options.value=e,this.showOverAllClear(),this.invokePopupEvent()},o.prototype.clearIconClick=function(e){this.options.value=e,this.showOverAllClear(),(sf.base.isNullOrUndefined(this.options.value)||this.options.value&&0===this.options.value.length)&&(sf.base.removeClass([this.element],i),this.options.textWrap||sf.base.addClass([this.overFlowWrapper],t),"Delimiter"!==this.options.mode&&(sf.base.addClass([this.chipWrapper],t),this.chipWrapper.style.display="")),this.ensurePlaceHolder(),(this.options.allowMultiSelection||this.options.showCheckBox)&&this.popupObj&&this.popupObj.refreshPosition()},o.prototype.updateSelectedValue=function(e){var i=!!this.options.value&&!!this.options.value.length;"Delimiter"!==this.options.mode&&(this.options.allowMultiSelection||this.options.showCheckBox)&&i&&(sf.base.addClass([this.inputWrapper],s),this.chipWrapper.style.display="block");var o=this.getValidMode();"Custom"===this.options.mode||"Box"===this.options.mode||!this.options.allowMultiSelection&&!this.options.showCheckBox||o||this.chipWrapper&&(sf.base.addClass([this.chipWrapper],t),sf.base.removeClass([this.inputWrapper],s),this.chipWrapper.style.display=""),"Custom"===this.options.mode&&(this.options.allowMultiSelection||this.options.showCheckBox)&&this.setCustomModeClass(),this.options.showClearButton&&this.inputFocus&&this.showOverAllClear(),e&&this.setChipWrapperClass(),(this.options.allowMultiSelection||this.options.showCheckBox)&&this.popupObj&&this.popupObj.refreshPosition(),this.ensurePlaceHolder()},o.prototype.setCustomModeClass=function(){sf.base.isNullOrUndefined(this.options.value)||(this.inputWrapper.contains(this.chipWrapper)||this.createChip(),this.inputWrapper.classList.contains(s)||sf.base.addClass([this.inputWrapper],s),this.element.classList.contains(i)||sf.base.addClass([this.element],i),this.chipWrapper.classList.contains(t)&&sf.base.removeClass([this.chipWrapper],t))},o.prototype.ensurePlaceHolder=function(){(sf.base.isNullOrUndefined(this.options.value)||this.options.value&&0===this.options.value.length)&&(sf.base.removeClass([this.element],i),this.chipWrapper&&(sf.base.addClass([this.chipWrapper],t),this.chipWrapper.style.display=""))},o.prototype.setChipWrapperClass=function(){var e=!!this.options.allowMultiSelection||!!this.options.showCheckBox;this.inputWrapper.contains(this.chipWrapper)&&!e&&(sf.base.removeClass([this.element],i),sf.base.detach(this.chipWrapper)),this.getValidMode()&&null!==this.options.value&&(sf.base.addClass([this.element],i),this.chipWrapper&&sf.base.removeClass([this.chipWrapper],t));var s=!!this.options.value&&!!this.options.value.length;this.chipWrapper&&"Box"===this.options.mode&&!s&&(sf.base.addClass([this.chipWrapper],t),this.options.textWrap||sf.base.addClass([this.overFlowWrapper],t),sf.base.removeClass([this.element],i),this.chipWrapper.style.display=""),this.options.textWrap||0==this.inputWrapper.offsetWidth||!this.options.allowMultiSelection&&!this.options.showCheckBox||this.updateView()},o.prototype.updateOverflowWrapper=function(e){if(e){if(this.inputWrapper.contains(this.overFlowWrapper)&&e)for(;this.overFlowWrapper.firstChild;)this.overFlowWrapper.removeChild(this.overFlowWrapper.firstChild)}else this.inputWrapper.contains(this.overFlowWrapper)||(this.overFlowWrapper=sf.base.select(".e-overflow",this.inputWrapper),this.inputWrapper.insertBefore(this.overFlowWrapper,this.element))},o.prototype.updateMode=function(){if("Custom"!==this.options.mode)if("Delimiter"!==this.options.mode){this.inputWrapper.contains(this.chipWrapper)||this.createChip();var e=this.getValidMode();this.chipWrapper.classList.contains(t)&&e?(sf.base.removeClass([this.chipWrapper],t),sf.base.addClass([this.inputWrapper],s)):e||(sf.base.addClass([this.chipWrapper],t),sf.base.removeClass([this.inputWrapper],s),this.chipWrapper.style.display="");var o=null!==this.options.value&&0!==this.options.value.length;e&&o?sf.base.addClass([this.element],i):sf.base.removeClass([this.element],i)}else this.element.classList.contains(i)&&(sf.base.removeClass([this.element],i),this.chipWrapper&&(sf.base.addClass([this.chipWrapper],t),sf.base.addClass([this.overFlowWrapper],t),sf.base.removeClass([this.inputWrapper],s),this.chipWrapper.style.display=""))},o.prototype.updateProperties=function(e){for(var o=0,p=Object.keys(e);o<p.length;o++){switch(p[o]){case"showSelectAll":this.options.showSelectAll=e.showSelectAll;break;case"showCheckBox":this.options.showCheckBox=e.showCheckBox,this.createOverFlowWrapper();break;case"popupHeight":this.options.popupHeight=e.popupHeight,this.popupObj&&(this.popupObj.height=this.options.popupHeight,this.updatePopupHeight());break;case"popupWidth":this.options.popupWidth=e.popupWidth,this.popupObj&&(this.popupObj.element.style.width=this.setWidth());break;case"zIndex":this.options.zIndex=e.zIndex,this.popupObj&&(this.popupObj.zIndex=this.options.zIndex);break;case"allowFiltering":this.options.allowFiltering=e.allowFiltering;break;case"allowMultiSelection":this.options.allowMultiSelection=e.allowMultiSelection,this.createOverFlowWrapper();break;case"disabled":this.options.disabled=e.disabled,this.setDisable();break;case"mode":if(!this.options.showCheckBox&&!this.options.allowMultiSelection)return;var a=this.options.mode;this.options.mode=e.mode,"Custom"===this.options.mode?(this.overFlowWrapper&&sf.base.detach(this.overFlowWrapper),this.chipWrapper&&sf.base.detach(this.chipWrapper),this.setCustomModeClass()):("Custom"===a&&this.updateOverflowWrapper(this.options.textWrap),this.updateMode());break;case"showClearButton":this.options.showClearButton=e.showClearButton,this.overAllClear=sf.base.select(".e-clear-icon",this.inputWrapper);break;case"textWrap":this.options.textWrap=e.textWrap,this.updateOverflowWrapper(this.options.textWrap),!this.options.allowMultiSelection&&!this.options.showCheckBox||this.options.textWrap?(sf.base.addClass([this.overFlowWrapper],t),sf.base.removeClass([this.inputWrapper],"e-show-text"),this.chipWrapper&&"Box"===this.options.mode?sf.base.removeClass([this.chipWrapper],t):(sf.base.removeClass([this.inputWrapper],s),sf.base.removeClass([this.element],i))):this.updateView()}}},o.prototype.destroy=function(){this.popupObj&&(this.popupObj.destroy(),this.popupObj=null),this.popupDiv=null,this.popupEle=null,this.unWireEvents()},o}();return{initialize:function(e,t,s,i,p){var a=new o(e,t,s,i,p);sf.base.isNullOrUndefined(a)||a.initialize()},showPopup:function(e,t,s,i){var o=window.sfBlazor.getCompInstance(e);if(!sf.base.isNullOrUndefined(o)){if(o.popupDiv=i,o.options.value=t,o.chipWrapper&&"Delimiter"!==o.options.mode&&o.options.value&&0!=o.options.value.length&&(o.chipWrapper.style.display="block"),!sf.base.isNullOrUndefined(s)){var p=document.elementFromPoint(s.clientX,s.clientY);if(p&&p.classList.contains("e-chips-close"))return}o.isPopupOpen||(o.showOverAllClear(),o.showPopup(),o.inputFocus=!0)}},invokePopupEvent:function(e,t,s){var i=window.sfBlazor.getCompInstance(e);if(!sf.base.isNullOrUndefined(i)){if(i.options.value=t,!sf.base.isNullOrUndefined(s)){var o=document.elementFromPoint(s.clientX,s.clientY);if(o&&o.classList.contains("e-chips-close"))return}i.isPopupOpen&&(i.invokePopupEvent(),i.showOverAllClear())}},closePopup:function(e){var t=window.sfBlazor.getCompInstance(e);sf.base.isNullOrUndefined(t)||t.closePopup()},onNodeSelected:function(e,t){var s=window.sfBlazor.getCompInstance(e);sf.base.isNullOrUndefined(s)||s.onNodeSelected(t)},clearIconClick:function(e,t,s){var i=window.sfBlazor.getCompInstance(e);sf.base.isNullOrUndefined(i)||(i.clearIconClick(t),s&&i.inputFocus&&i.onFocusOut())},updateSelectedValue:function(e,t,s){var i=window.sfBlazor.getCompInstance(e);sf.base.isNullOrUndefined(i)||(i.options.value=t,i.updateSelectedValue(s))},getTreeItemsId:function(e){var t=[],s=window.sfBlazor.getCompInstance(e);if(!sf.base.isNullOrUndefined(s))for(var i=sf.base.selectAll("li",s.popupDiv),o=void 0,p=0;p<i.length;p++)o=i[p].getAttribute("data-uid").toString(),t.push(o);return t},updateProperties:function(e,t){var s=window.sfBlazor.getCompInstance(e);sf.base.isNullOrUndefined(s)||s.updateProperties(t)},refreshPosition:function(e){var t=window.sfBlazor.getCompInstance(e);sf.base.isNullOrUndefined(t&&t.popupObj)||t.popupObj.refreshPosition()},destroy:function(e){var t=window.sfBlazor.getCompInstance(e);sf.base.isNullOrUndefined(t)||t.destroy()}}}()}}]);(async()=>{await import(`${document.baseURI}_content/Syncfusion.Blazor/scripts/syncfusion-blazor-base.min.js?v=26.1.undefined`).then(()=>{sfBlazor.loadDependencies('sfdropdowntree');})})();