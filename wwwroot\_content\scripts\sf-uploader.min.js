/*!*  filename: sf-uploader.min.js 
*     version : 26.1.41 
*     Copyright Syncfusion Inc. 2001 - 2024. All rights reserved. 
*     Use of this code is subject to the terms of our license.
*     A copy of the current license can be obtained at any time by e-mailing 
*     <EMAIL>. Any infringement will be prosecuted under 
*     applicable laws. 
*/
(window.webpackJsonp=window.webpackJsonp||[]).push([[73],{"./bundles/sf-uploader.js":function(e,t,s){"use strict";s.r(t);s("./modules/sf-uploader.js")},"./modules/sf-uploader.js":function(e,t){function s(e){return(s="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}window.sfBlazor=window.sfBlazor||{},window.sfBlazor.Uploader=function(){"use strict";var e={};function t(t,s){if(t.target){var r,o=sf.base.isNullOrUndefined(s)?sf.base.createElement:s,u=function(e,t){var s=t("div",{}),i=t("div",{});return s.classList.add("e-spinner-pane"),i.classList.add("e-spinner-inner"),e.appendChild(s),s.appendChild(i),{wrap:s,inner_wrap:i}}(t.target,o);if(sf.base.isNullOrUndefined(t.cssClass)||u.wrap.classList.add(t.cssClass),sf.base.isNullOrUndefined(t.template)&&sf.base.isNullOrUndefined(null)){var d=sf.base.isNullOrUndefined(t.type)?function(e){return window.getComputedStyle(e,":after").getPropertyValue("content").replace(/['"]+/g,"")}(u.wrap):t.type;r=function(e,t){var s;switch(t){case"Material":case"Fabric":s=30;break;case"Tailwind":case"Tailwind-dark":s=30;break;case"Bootstrap4":s=36;break;default:s=30}return e=e?parseFloat(e+""):s,"Bootstrap"===t?e:e/2}(sf.base.isNullOrUndefined(t.width)?void 0:t.width,d),function(t,s,r,o){var u=s.querySelector(".e-spinner-inner"),d=u.querySelector("svg");sf.base.isNullOrUndefined(d)||u.removeChild(d);switch(t){case"Material":!function(t,s,a){var r=i();e[r]={timeOut:0,type:"Material",radius:s},n(t,r,a,"e-spin-material"),l(s,t,"Material","e-spin-material")}(u,r,o);break;case"Fabric":!function(t,s,n){var r=i();e[r]={timeOut:0,type:"Fabric",radius:s},a(t,r,"e-spin-fabric"),c(s,t,"e-spin-fabric")}(u,r);break;case"Bootstrap":!function(t,s,a){var n=i();e[n]={timeOut:0,type:"Bootstrap",radius:s},function(e,t,s){var i=document.createElementNS("http://www.w3.org/2000/svg","svg");i.setAttribute("id",t),i.setAttribute("class","e-spin-bootstrap"),i.setAttribute("viewBox","0 0 64 64"),e.insertBefore(i,e.firstChild);for(var a=0;a<=7;a++){var n=document.createElementNS("http://www.w3.org/2000/svg","circle");n.setAttribute("class","e-path-circle_"+a),n.setAttribute("r","2"),n.setAttribute("transform","translate(32,32)"),i.appendChild(n)}}(t,n),function(e,t){var s=e.querySelector("svg.e-spin-bootstrap");s.style.width=s.style.height=t+"px";for(var i=90,a=0;a<=7;a++){var n=h(0,0,24,i),r=s.querySelector(".e-path-circle_"+a);r.setAttribute("cx",n.x+""),r.setAttribute("cy",n.y+""),i=i>=360?0:i,i+=45}}(t,s)}(u,r);break;case"HighContrast":!function(t,s,n){var r=i();e[r]={timeOut:0,type:"HighContrast",radius:s},a(t,r,"e-spin-high-contrast"),c(s,t,"e-spin-high-contrast")}(u,r);break;case"Bootstrap4":!function(t,s,a){var r=i();e[r]={timeOut:0,type:"Bootstrap4",radius:s},n(t,r,a,"e-spin-bootstrap4"),l(s,t,"Bootstrap4","e-spin-bootstrap4")}(u,r,o);break;case"Tailwind":case"Tailwind-dark":!function(t,s,n){var r=i();e[r]={timeOut:0,type:"Tailwind",radius:s},a(t,r,"e-spin-tailwind"),c(s,t,"e-spin-tailwind")}(u,r)}}(d,u.wrap,r,o),sf.base.isNullOrUndefined(t.label)||function(e,t,s){var i=s("div",{});i.classList.add("e-spin-label"),i.innerHTML=t,e.appendChild(i)}(u.inner_wrap,t.label,o)}else{var f=sf.base.isNullOrUndefined(t.template)?null:t.template;u.wrap.classList.add("e-spin-template"),function(e,t,s){sf.base.isNullOrUndefined(s)||e.classList.add(s);e.querySelector(".e-spinner-inner").innerHTML=t}(u.wrap,f,null)}u.wrap.classList.add("e-spin-hide"),u=null}}function i(){for(var e="",t="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789",s=0;s<5;s++)e+=t.charAt(Math.floor(Math.random()*t.length));return e}function a(e,t,s,i){var a=document.createElementNS("http://www.w3.org/2000/svg","svg");a.setAttribute("id",t),a.setAttribute("class",s);var n=document.createElementNS("http://www.w3.org/2000/svg","path");n.setAttribute("class","e-path-circle");var r=document.createElementNS("http://www.w3.org/2000/svg","path");r.setAttribute("class","e-path-arc"),e.insertBefore(a,e.firstChild),a.appendChild(n),a.appendChild(r)}function n(e,t,s,i){var a=document.createElementNS("http://www.w3.org/2000/svg","svg"),n=document.createElementNS("http://www.w3.org/2000/svg","path");a.setAttribute("class",i),a.setAttribute("id",t),n.setAttribute("class","e-path-circle"),e.insertBefore(a,e.firstChild),a.appendChild(n)}function r(t){!function(t,s,i,a,n,l,d){var c=++d.globalInfo[d.uniqueID].previousId,h=(new Date).getTime(),f=s-t,p=(b=2*d.globalInfo[d.uniqueID].radius+"",parseFloat(b)),v=o(p),m=-90*(d.globalInfo[d.uniqueID].count||0);var b;!function s(n){var o=Math.max(0,Math.min((new Date).getTime()-h,a));!function(e,t){if(!sf.base.isNullOrUndefined(t.querySelector("svg.e-spin-material"))&&!sf.base.isNullOrUndefined(t.querySelector("svg.e-spin-material").querySelector("path.e-path-circle"))){var s=t.querySelector("svg.e-spin-material").querySelector("path.e-path-circle");s.setAttribute("stroke-dashoffset",u(p,v,e,l)+""),s.setAttribute("transform","rotate("+m+" "+p/2+" "+p/2+")")}}(i(o,t,f,a),n.container),c===n.globalInfo[n.uniqueID].previousId&&o<a?e[n.uniqueID].timeOut=setTimeout(s.bind(null,n),1):r(n)}(d)}(1,149,d,1333,t.globalInfo[t.uniqueID].count,75,t),t.globalInfo[t.uniqueID].count=++t.globalInfo[t.uniqueID].count%4}function l(e,t,s,i){var a=2*e,n=t.querySelector("svg."+i),r=n.querySelector("path.e-path-circle"),l=o(a),d=a/2+"px";n.setAttribute("viewBox","0 0 "+a+" "+a),n.style.width=n.style.height=a+"px",n.style.transformOrigin=d+" "+d+" "+d,r.setAttribute("d",function(e,t){var s=e/2,i=t/2;return"M"+s+","+i+"A"+(s-i)+","+(s-i)+" 0 1 1 "+i+","+s}(a,l)),"Material"===s&&(r.setAttribute("stroke-width",l+""),r.setAttribute("stroke-dasharray",(a-l)*Math.PI*.75+""),r.setAttribute("stroke-dashoffset",u(a,l,1,75)+""))}function o(e){return.1*e}function u(e,t,s,i){return(e-t)*Math.PI*(3*i/100-s/100)}function d(e,t,s,i){var a=(e/=i)*e,n=a*e;return t+s*(6*n*a+-15*a*a+10*n)}function c(e,t,s){var i=e,a=e,n=2*e,r=t.querySelector("."+s),l=r.querySelector(".e-path-circle"),o=r.querySelector(".e-path-arc"),u=n/2+"px";l.setAttribute("d",function(e,t,s){return["M",e,t,"m",-s,0,"a",s,s,0,1,0,2*s,0,"a",s,s,0,1,0,2*-s,0].join(" ")}(i,a,e)),o.setAttribute("d",function(e,t,s,i,a){var n=h(e,t,s,a),r=h(e,t,s,i);return["M",n.x,n.y,"A",s,s,0,0,0,r.x,r.y].join(" ")}(i,a,e,315,45)),r.setAttribute("viewBox","0 0 "+n+" "+n),r.style.transformOrigin=u+" "+u+" "+u,r.style.width=r.style.height=n+"px"}function h(e,t,s,i){var a=(i-90)*Math.PI/180;return{x:e+s*Math.cos(a),y:t+s*Math.sin(a)}}function f(e){p(e,!1),e=null}function p(t,s){var i;if(t&&(i=t.classList.contains("e-spinner-pane")?t:t.querySelector(".e-spinner-pane")),t&&i){var a=i.querySelector(".e-spinner-inner");if(s?!i.classList.contains("e-spin-template")&&!i.classList.contains("e-spin-hide"):!i.classList.contains("e-spin-template")&&!i.classList.contains("e-spin-show")){var n=i.querySelector("svg");if(sf.base.isNullOrUndefined(n))return;var l=n.getAttribute("id");switch(e[l].isAnimate=!s,e[l].type){case"Material":s?clearTimeout(e[l].timeOut):function(t,s,i){var a={};e[s].timeOut=0,a[s]=function(e,t,s,i){return{radius:t,count:s,previousId:i}}(0,i,0,0),r({uniqueID:s,container:t,globalInfo:a,timeOutVar:0})}(a,l,e[l].radius);break;case"Bootstrap":s?clearTimeout(e[l].timeOut):function(t){for(var s,i,a,n,r,l=t.querySelector("svg.e-spin-bootstrap").getAttribute("id"),o=1;o<=8;o++){u(t.getElementsByClassName("e-path-circle_"+(8===o?0:o))[0],o,o,(s=void 0,i=void 0,a=void 0,n=void 0,r=void 0,s=[],a=o,n=!1,r=1,function e(t){s.push(t),(t!==a||1===r)&&(t<=i&&t>1&&!n?t=parseFloat((t-.2).toFixed(2)):1===t?(t=7,t=parseFloat((t+.2).toFixed(2)),n=!0):t<8&&n?8===(t=parseFloat((t+.2).toFixed(2)))&&(n=!1):t<=8&&!n&&(t=parseFloat((t-.2).toFixed(2))),++r,e(t))}(i=o),s),l)}function u(t,s,i,a,n){var r=0;!function s(i){e[n].isAnimate&&(++r,t.setAttribute("r",i+""),r>=a.length&&(r=0),e[n].timeOut=setTimeout(s.bind(null,a[r]),18))}(s)}}(a)}}s?sf.base.classList(i,["e-spin-hide"],["e-spin-show"]):sf.base.classList(i,["e-spin-show"],["e-spin-hide"]),t=null}}function v(e){p(e,!0),e=null}var m=function(e,t,s,i){return new(s||(s=Promise))((function(a,n){function r(e){try{o(i.next(e))}catch(e){n(e)}}function l(e){try{o(i.throw(e))}catch(e){n(e)}}function o(e){e.done?a(e.value):new s((function(t){t(e.value)})).then(r,l)}o((i=i.apply(e,t||[])).next())}))},b=function(e,t){var s,i,a,n,r={label:0,sent:function(){if(1&a[0])throw a[1];return a[1]},trys:[],ops:[]};return n={next:l(0),throw:l(1),return:l(2)},"function"==typeof Symbol&&(n[Symbol.iterator]=function(){return this}),n;function l(n){return function(l){return function(n){if(s)throw new TypeError("Generator is already executing.");for(;r;)try{if(s=1,i&&(a=2&n[0]?i.return:n[0]?i.throw||((a=i.return)&&a.call(i),0):i.next)&&!(a=a.call(i,n[1])).done)return a;switch(i=0,a&&(n=[2&n[0],a.value]),n[0]){case 0:case 1:a=n;break;case 4:return r.label++,{value:n[1],done:!1};case 5:r.label++,i=n[1],n=[0];continue;case 7:n=r.ops.pop(),r.trys.pop();continue;default:if(!(a=r.trys,(a=a.length>0&&a[a.length-1])||6!==n[0]&&2!==n[0])){r=0;continue}if(3===n[0]&&(!a||n[1]>a[0]&&n[1]<a[3])){r.label=n[1];break}if(6===n[0]&&r.label<a[1]){r.label=a[1],a=n;break}if(a&&r.label<a[2]){r.label=a[2],r.ops.push(n);break}a[2]&&r.ops.pop(),r.trys.pop();continue}n=t.call(e,r)}catch(e){n=[6,e],i=0}finally{s=a=0}if(5&n[0])throw n[1];return{value:n[0]?n[1]:void 0,done:!0}}([n,l])}}},g="e-upload-file-list",y="e-file-status",S="e-file-remove-btn",F="e-file-delete-btn",C="e-file-abort-btn",U="e-file-reload-btn",D="e-upload-drag-hover",E="e-upload-progress-wrap",N="e-upload-progress-bar",k="e-upload-success",A="e-upload-fails",w="e-file-pause-btn",x=function(){function e(e,t,s,i){this.initialAttr={accept:null,multiple:!1,disabled:!1},this.uploadedFilesData=[],this.base64String=[],this.isForm=!1,this.allTypes=!1,this.pausedData=[],this.uploadMetaData=[],this.btnTabIndex="0",this.disableKeyboardNavigation=!1,this.count=-1,this.actionCompleteCount=0,this.flag=!0,this.selectedFiles=[],this.uploaderName="UploadFiles",this.fileStreams=[],this.newFileRef=0,this.isFirstFileOnSelection=!1,this.dragCounter=0,this.fileList=[],this.filesData=[],window.sfBlazor=window.sfBlazor,this.dataId=e,this.element=t.element,this.updateProperty(i),window.sfBlazor.setCompInstance(this),this.dotNetRef=s,this.withCredentials=!1}return e.prototype.initialize=function(){this.preRender(),this.render()},e.prototype.updateProperty=function(e){sf.base.extend(this,this,e)},e.prototype.reRenderFileList=function(){this.listParent&&(sf.base.detach(this.listParent),this.listParent=null,this.fileList=[],this.createFileList(this.filesData),this.actionButtons&&(this.removeActionButtons(),this.renderActionButtons(),this.checkActionButtonStatus()))},e.prototype.updateDropArea=function(){if(this.dropArea)this.setDropArea();else{this.dropZoneElement=null;var e=this.dropAreaWrapper.querySelector(".e-file-drop");e&&sf.base.remove(e)}},e.prototype.propertyChanges=function(e,t){this.updateProperty(e),this.isSaveUrlNotConfigured=""===this.asyncSettings.saveUrl||sf.base.isNullOrUndefined(this.asyncSettings.saveUrl);for(var s=0,i=Object.keys(t);s<i.length;s++){switch(i[s]){case"AllowedExtensions":this.clearAll();break;case"EnableRtl":this.reRenderFileList();break;case"Buttons":this.buttons.browse=sf.base.isNullOrUndefined(this.buttons.browse)?"":this.buttons.browse,this.buttons.clear=sf.base.isNullOrUndefined(this.buttons.clear)?"":this.buttons.clear,this.buttons.upload=sf.base.isNullOrUndefined(this.buttons.upload)?"":this.buttons.upload,this.renderButtonTemplates();break;case"DropArea":this.unBindDropEvents(),this.updateDropArea();break;case"Files":this.renderPreLoadFiles();break;case"MinFileSize":case"MaxFileSize":case"AutoUpload":case"SequentialUpload":this.clearAll()}}},e.prototype.preRender=function(){this.isValidTemplate=""!==this.template&&!sf.base.isNullOrUndefined(this.template),this.isSaveUrlNotConfigured=""===this.asyncSettings.saveUrl||sf.base.isNullOrUndefined(this.asyncSettings.saveUrl),this.isSaveUrlNotConfigured&&this.sequentialUpload&&(this.sequentialUpload=!1),this.isSaveUrlNotConfigured||this.formRendered(),this.keyConfigs={enter:"enter"},this.browserName=sf.base.Browser.info.name,this.uploaderName=this.element.getAttribute("name")},e.prototype.formRendered=function(){var e=sf.base.closest(this.element,"form");if(!sf.base.isNullOrUndefined(e))for(;e&&e!==document.documentElement;e=e.parentElement)"FORM"===e.tagName&&(this.isForm=!0,this.formElement=e,e.setAttribute("enctype","multipart/form-data"),e.setAttribute("encoding","multipart/form-data"))},e.prototype.render=function(){this.dropAreaWrapper=sf.base.closest(this.element,".e-file-select-wrap"),this.uploadWrapper=sf.base.closest(this.element,".e-upload.e-control-wrapper"),this.browseButton=this.dropAreaWrapper.querySelector("button.e-upload-browse-btn"),this.setDropArea(),this.renderPreLoadFiles(),this.wireEvents()},e.prototype.wireEvents=function(){sf.base.EventHandler.add(this.browseButton,"click",this.browseButtonClick,this),sf.base.EventHandler.add(this.element,"change",this.onSelectFiles,this),sf.base.EventHandler.add(document,"click",this.removeFocus,this),this.keyboardModule=new sf.base.KeyboardEvents(this.uploadWrapper,{keyAction:this.keyActionHandler.bind(this),keyConfigs:this.keyConfigs,eventName:"keydown"}),this.isForm&&sf.base.EventHandler.add(this.formElement,"reset",this.resetForm,this)},e.prototype.renderPreLoadFiles=function(){if(this.files&&this.files.length){if(this.enablePersistence&&this.filesData.length)return void this.createFileList(this.filesData);if(sf.base.isNullOrUndefined(this.files[0].size))return;var e=[].slice.call(this.files),t=[];this.multiple||(this.clearData(),e=[e[0]]);for(var s=0,i=e;s<i.length;s++){var a=i[s],n={name:a.name+"."+a.type.split(".")[a.type.split(".").length-1],rawFile:"",size:a.size,status:this.localizedTexts("uploadSuccessMessage"),type:a.type,validationMessages:{minSize:"",maxSize:""},statusCode:"2"};t.push(n),this.filesData.push(n)}if(this.isValidTemplate)return;this.createFileList(t),this.autoUpload||!this.listParent||this.actionButtons||this.isForm&&!this.allowUpload()||!this.showFileList||this.renderActionButtons(),this.checkActionButtonStatus(),this.sequentialUpload&&(this.count=this.filesData.length-1)}},e.prototype.renderActionButtons=function(){this.element.setAttribute("tabindex","-1"),this.isSaveUrlNotConfigured||this.isValidTemplate||(this.actionButtons=sf.base.createElement("div",{className:"e-upload-actions"}),this.uploadButton=sf.base.createElement("button",{className:"e-file-upload-btn e-css e-btn e-flat e-primary",attrs:{type:"button",tabindex:this.btnTabIndex}}),this.clearButton=sf.base.createElement("button",{className:"e-file-clear-btn e-css e-btn e-flat",attrs:{type:"button",tabindex:this.btnTabIndex}}),this.actionButtons.appendChild(this.clearButton),this.actionButtons.appendChild(this.uploadButton),this.renderButtonTemplates(),this.uploadWrapper.appendChild(this.actionButtons),this.browseButton.blur(),this.uploadButton.focus(),this.wireActionButtonEvents())},e.prototype.setDropArea=function(){var e=this.dropAreaWrapper.querySelector(".e-file-drop");if(this.dropArea){this.dropZoneElement="string"!=typeof this.dropArea?this.dropArea:document.querySelector(this.dropArea);for(var t=this.element,s=!1;t.parentNode;)(t=t.parentNode)===this.dropZoneElement&&(s=!0);!s&&e&&sf.base.remove(e)}else sf.base.isNullOrUndefined(this.dropArea)&&(this.dropZoneElement=this.uploadWrapper);this.bindDropEvents()},e.prototype.serverActionButtonsEventBind=function(e){e&&!this.isForm&&(this.browseButton.blur(),this.actionButtons=e,this.uploadButton=this.actionButtons.querySelector(".e-file-upload-btn"),this.clearButton=this.actionButtons.querySelector(".e-file-clear-btn"),this.uploadButton.focus(),this.unwireActionButtonEvents(),this.wireActionButtonEvents(),this.checkActionButtonStatus())},e.prototype.serverUlElement=function(e){e&&(this.isSaveUrlNotConfigured||this.isValidTemplate)&&(this.listParent=e,this.fileList=[].slice.call(this.listParent.querySelectorAll("li")),this.serverRemoveIconBindEvent(),this.isForm||this.checkAutoUpload(this.filesData))},e.prototype.wireActionButtonEvents=function(){sf.base.EventHandler.add(this.uploadButton,"click",this.uploadButtonClick,this),sf.base.EventHandler.add(this.clearButton,"click",this.clearButtonClick,this)},e.prototype.unwireActionButtonEvents=function(){sf.base.EventHandler.remove(this.uploadButton,"click",this.uploadButtonClick),sf.base.EventHandler.remove(this.clearButton,"click",this.clearButtonClick)},e.prototype.checkActionButtonStatus=function(){if(this.actionButtons&&!this.isValidTemplate){var e=this.uploadWrapper.querySelectorAll(".e-validation-fails").length+this.uploadWrapper.querySelectorAll(".e-upload-fails:not(.e-upload-progress)").length+this.uploadWrapper.querySelectorAll("span."+k).length+this.uploadWrapper.querySelectorAll("span.e-upload-progress").length;e>0&&e===this.uploadWrapper.querySelectorAll("li").length?this.uploadButton.setAttribute("disabled","disabled"):this.uploadButton.removeAttribute("disabled")}},e.prototype.renderButtonTemplates=function(){if("string"==typeof this.buttons.browse?(this.browseButton.textContent="Browse..."===this.buttons.browse?this.localizedTexts("browse"):this.buttons.browse,this.browseButton.setAttribute("title",this.browseButton.textContent)):(this.browseButton.innerHTML="",this.browseButton.appendChild(this.buttons.browse)),this.uploadButton){var e;e=sf.base.isNullOrUndefined(this.buttons.upload)?"Upload":this.buttons.upload,this.buttons.upload=e,"string"==typeof this.buttons.upload?(this.uploadButton.textContent="Upload"===this.buttons.upload?this.localizedTexts("upload"):this.buttons.upload,this.uploadButton.setAttribute("title",this.uploadButton.textContent)):(this.uploadButton.innerHTML="",this.uploadButton.appendChild(this.buttons.upload))}if(this.clearButton){var t;t=sf.base.isNullOrUndefined(this.buttons.clear)?"Clear":this.buttons.clear,this.buttons.clear=t,"string"==typeof this.buttons.clear?(this.clearButton.textContent="Clear"===this.buttons.clear?this.localizedTexts("clear"):this.buttons.clear,this.clearButton.setAttribute("title",this.clearButton.textContent)):(this.clearButton.innerHTML="",this.clearButton.appendChild(this.buttons.clear))}},e.prototype.checkAutoUpload=function(e){if(this.autoUpload){if(this.sequentialUpload)this.sequenceUpload(e);else if("e-fm-upload"==this.cssClass)for(var t=0;t<e.length;t++)this.upload(e[t]);else this.upload(e);this.removeActionButtons()}else this.actionButtons||this.renderActionButtons();this.checkActionButtonStatus()},e.prototype.removeActionButtons=function(){this.actionButtons&&(this.unwireActionButtonEvents(),this.isSaveUrlNotConfigured||this.isValidTemplate||sf.base.detach(this.actionButtons),this.actionButtons=null)},e.prototype.sequenceUpload=function(e){var t=e&&e.length?e.length:e?1:0;if(this.filesData.length-t==0||"1"!==this.filesData[this.filesData.length-t-1].statusCode){(this.multiple||this.count<0)&&++this.count;var i=!this.showFileList;"object"===s(this.filesData[this.count])?(this.isFirstFileOnSelection=!1,"2"!=this.filesData[this.count].statusCode&&this.upload(this.filesData[this.count],i),"0"===this.filesData[this.count].statusCode&&this.sequenceUpload(e)):--this.count}},e.prototype.unWireEvents=function(){sf.base.EventHandler.remove(this.browseButton,"click",this.browseButtonClick),sf.base.EventHandler.remove(this.element,"change",this.onSelectFiles),sf.base.EventHandler.remove(document,"click",this.removeFocus),this.isForm&&sf.base.EventHandler.remove(this.formElement,"reset",this.resetForm),this.keyboardModule&&this.keyboardModule.destroy()},e.prototype.resetForm=function(){this.clearAll()},e.prototype.keyActionHandler=function(e){var t=e.target;switch(e.action){case"enter":if(e.target===this.clearButton)this.clearButtonClick();else if(e.target===this.uploadButton)this.uploadButtonClick();else if(e.target===this.browseButton)this.browseButtonClick();else if(t.classList.contains(w)){(s=this.getCurrentMetaData(null,e)).file.statusCode="4",s.file.status=this.localizedTexts("pauseUpload"),this.abortUpload(s,!1)}else if(t.classList.contains("e-file-play-btn"))this.resumeUpload(this.getCurrentMetaData(null,e),e);else if(t.classList.contains(U)){var s=this.getCurrentMetaData(null,e);if(sf.base.isNullOrUndefined(s)){var i=e.target.parentElement,a=this.filesData[this.fileList.indexOf(i)];this.retry(a)}else s.file.statusCode="1",s.file.status=this.localizedTexts("readyToUploadMessage"),this.chunkUpload(s.file)}else{if(this.isSaveUrlNotConfigured)break;this.removeFiles(e),t.classList.contains(C)||this.browseButton.focus()}e.preventDefault(),e.stopPropagation()}},e.prototype.getCurrentMetaData=function(e,t){var s,i;if(sf.base.isNullOrUndefined(e)){var a=t.target.parentElement;s=this.filesData[this.fileList.indexOf(a)]}else s=e;for(var n=0;n<this.uploadMetaData.length;n++)this.uploadMetaData[n].file.name===s.name&&(i=this.uploadMetaData[n]);return i},e.prototype.removeFocus=function(){this.uploadWrapper&&this.listParent&&this.listParent.querySelector(".e-clear-icon-focus")&&(document.activeElement.blur(),this.listParent.querySelector(".e-clear-icon-focus").classList.remove("e-clear-icon-focus"))},e.prototype.browseButtonClick=function(){this.element.click()},e.prototype.uploadButtonClick=function(){this.sequentialUpload?this.sequenceUpload(this.filesData):this.upload(this.filesData)},e.prototype.clearButtonClick=function(){this.clearAll(),this.sequentialUpload&&(this.count=-1),this.actionCompleteCount=0},e.prototype.bindDropEvents=function(){this.dropZoneElement&&(sf.base.EventHandler.add(this.dropZoneElement,"drop",this.dropElement,this),sf.base.EventHandler.add(this.dropZoneElement,"dragover",this.dragHover,this),sf.base.EventHandler.add(this.dropZoneElement,"dragleave",this.onDragLeave,this),sf.base.EventHandler.add(this.dropZoneElement,"paste",this.onPasteFile,this),sf.base.EventHandler.add(this.dropZoneElement,"dragenter",this.onDragEnter,this))},e.prototype.unBindDropEvents=function(){this.dropZoneElement&&(sf.base.EventHandler.remove(this.dropZoneElement,"drop",this.dropElement),sf.base.EventHandler.remove(this.dropZoneElement,"dragover",this.dragHover),sf.base.EventHandler.remove(this.dropZoneElement,"dragleave",this.onDragLeave),sf.base.EventHandler.remove(this.dropZoneElement,"dragenter",this.onDragEnter))},e.prototype.onDragEnter=function(e){this.enabled&&(this.dropZoneElement.classList.add(D),this.dragCounter=this.dragCounter+1,e.preventDefault(),e.stopPropagation())},e.prototype.onDragLeave=function(e){this.enabled&&(this.dragCounter=this.dragCounter-1,this.dragCounter||this.dropZoneElement.classList.remove(D))},e.prototype.dragHover=function(e){this.enabled&&("Default"!==this.dropEffect&&(e.dataTransfer.dropEffect=this.dropEffect.toLowerCase()),-1!==e.dataTransfer.types.indexOf("Files")&&(e.preventDefault(),e.stopPropagation()))},e.prototype.getFileFromEntry=function(e){return new Promise((function(t,s){e.file(t,s)}))},e.prototype.traverseDirectory=function(e){return m(this,void 0,void 0,(function(){var t,s=this;return b(this,(function(i){return t=e.createReader(),[2,new Promise((function(e,i){t.readEntries((function(t){return m(s,void 0,void 0,(function(){var s,i,a,n;return b(this,(function(r){switch(r.label){case 0:s=0,r.label=1;case 1:if(!(s<t.length))return[3,9];if(!(i=t[s]).isFile)return[3,6];r.label=2;case 2:return r.trys.push([2,4,,5]),[4,this.getFileFromEntry(i)];case 3:return a=r.sent(),this.FileDirectoryDetails.push({path:i.fullPath,file:a}),this.DropDirectoryFiles.items.add(a),[3,5];case 4:return n=r.sent(),console.error("Failed to convert FileEntry to File object:",n),[3,5];case 5:return[3,8];case 6:return i.isDirectory?[4,this.traverseDirectory(i)]:[3,8];case 7:r.sent(),r.label=8;case 8:return s++,[3,1];case 9:return e(),[2]}}))}))}),i)}))]}))}))},e.prototype.dropElement=function(e){var t=this;if(this.dragCounter=0,e.preventDefault(),this.isSaveUrlNotConfigured&&this.directoryUpload){this.FileDirectoryDetails=[],this.DropDirectoryFiles=new DataTransfer;for(var s=e.dataTransfer.items,i=0;i<s.length;i++){var a=s[i].webkitGetAsEntry();a&&a.isDirectory&&this.traverseDirectory(a).then((function(){t.element.files=t.DropDirectoryFiles.files;var s=new CustomEvent("change",{bubbles:!0,detail:{DropEvent:e}});t.element.dispatchEvent(s),t.dropZoneElement.classList.remove(D),-1!==e.dataTransfer.types.indexOf("Files")&&(e.preventDefault(),e.stopPropagation())})).catch((function(e){console.error("Failed to traverse directory:",e)}))}}else{this.element.files=e.dataTransfer.files;var n=new CustomEvent("change",{bubbles:!0,detail:{DropEvent:e}});this.element.dispatchEvent(n),this.dropZoneElement.classList.remove(D),-1!==e.dataTransfer.types.indexOf("Files")&&(e.preventDefault(),e.stopPropagation())}},e.prototype.onPasteFile=function(e){if(sf.base.isNullOrUndefined(this.dropArea)||!this.dropArea.includes(".e-rte-content")){var t=e.clipboardData.items;if(1===t.length||this.multiple){var s=[];if(t.length!==e.clipboardData.files.length)for(var i=0;i<e.clipboardData.files.length;i++){(n=e.clipboardData.files[i]).type.match("^image/")&&s.push(n)}else for(i=0;i<e.clipboardData.files.length;i++){var a=[].slice.call(t)[i],n=e.clipboardData.files[i];("file"===a.kind||a.type.match("^image/"))&&s.push(n)}var r=new DataTransfer;s.forEach((function(e){r.items.add(e)})),this.element.files=r.files;var l=new CustomEvent("change",{bubbles:!0,detail:{isDirectory:!1,isPaste:!0}});this.element.dispatchEvent(l)}}},e.prototype.getSelectedFiles=function(e){for(var t=[],s=this.fileList[e],i=this.getFilesData(),a=+s.getAttribute("data-files-count"),n=0,r=0;r<e;r++)n+=+this.fileList[r].getAttribute("data-files-count");for(var l=n;l<n+a;l++)t.push(i[l]);return t},e.prototype.removeFiles=function(e){if(this.enabled){var s=e.target.parentElement;this.isSaveUrlNotConfigured&&(this.fileList=[].slice.call(this.uploadWrapper.querySelectorAll("li")));var i=this.fileList.indexOf(s),a=this.fileList[i],n=this.isFormUpload(),r=n?this.getSelectedFiles(i):this.getFilesInArray(this.filesData[i]);if(!sf.base.isNullOrUndefined(r)){if(e.target.classList.contains(C)&&!n){if(r[0].statusCode="5",!sf.base.isNullOrUndefined(a)){var l=a.querySelector("."+C);t({target:l,width:"20px"}),f(l)}this.sequentialUpload&&this.uploadSequential(),a.classList.contains("e-restrict-retry")||this.checkActionComplete(!0)}else sf.base.closest(e.target,".e-spinner-pane")||this.remove(r,!1,!1,!0,e);this.element.value="",this.checkActionButtonStatus()}}},e.prototype.removeFilesData=function(e,t){var s;if(t)this.showFileList||(s=this.filesData.indexOf(e),this.filesData.splice(s,1));else{var i=this.getLiElement(e);if(!sf.base.isNullOrUndefined(i)){this.isSaveUrlNotConfigured||this.isValidTemplate||sf.base.detach(i);var a=i.querySelector(".e-spinner-pane");sf.base.isNullOrUndefined(a)||v(a),s=this.fileList.indexOf(i),this.fileList.splice(s,1),this.filesData.splice(s,1),this.isSaveUrlNotConfigured||this.isValidTemplate||0!==this.fileList.length||sf.base.isNullOrUndefined(this.listParent)?this.dotNetRef.invokeMethodAsync("RemoveFileData",s):(sf.base.detach(this.listParent),this.listParent=null,this.removeActionButtons(),this.sequentialUpload&&s<=this.count&&--this.count)}}},e.prototype.removeUploadedFile=function(e,t,s,i){var a=this,n=e,r=new sf.base.Ajax(this.asyncSettings.removeUrl,"POST",!0,null);r.emitError=!1;var l=new FormData;r.beforeSend=function(i){t.currentRequest=r.httpRequest,a.currentRequestHeader&&a.updateCustomheader(r.httpRequest,a.currentRequestHeader),a.customFormDatas&&a.updateFormData(l,a.customFormDatas),!s&&a.removingEnabled?a.dotNetRef.invokeMethodAsync("RemovingEvent",t).then((function(t){t.cancel?i.cancel=!0:a.removingEventCallback(e)})):a.removingEventCallback(e),a.sequentialUpload&&--a.count};var o=this.element.getAttribute("name");t.postRawFile&&!sf.base.isNullOrUndefined(n.rawFile)&&""!==n.rawFile?l.append(o,n.rawFile,n.name):l.append(o,n.name),r.onLoad=function(e){return a.removeCompleted(e,n,i),{}},r.onError=function(e){return a.removeFailed(e,n,i),{}},r.send(l)},e.prototype.removeCompleted=function(e,t,s){var i=e&&e.currentTarget?this.getResponse(e):null,a=e.target;if(4===a.readyState&&a.status>=200&&a.status<=299){var n={e:e,response:i,operation:"remove",file:this.updateStatus(t,this.localizedTexts("removedSuccessMessage"),"2")};this.successEnabled&&this.dotNetRef.invokeMethodAsync("SuccessEvent",n),this.removeFilesData(t,s);var r=this.uploadedFilesData.indexOf(t);this.uploadedFilesData.splice(r,1),this.changeEnabled&&this.dotNetRef.invokeMethodAsync("ChangeEvent",{files:this.uploadedFilesData})}else this.removeFailed(e,t,s)},e.prototype.removeFailed=function(e,t,s){var i={e:e,response:e&&e.currentTarget?this.getResponse(e):null,operation:"remove",file:this.updateStatus(t,this.localizedTexts("removedFailedMessage"),"0")};if(!s){var a=this.filesData.indexOf(t),n=this.fileList[a];if(n){n.classList.remove(k),n.classList.add(A);var r=n.querySelector("."+y);r&&(r.classList.remove(k),r.classList.add(A))}this.checkActionButtonStatus()}this.failuredEnabled&&this.dotNetRef.invokeMethodAsync("FailureEvent",i);var l=this.getLiElement(t);sf.base.isNullOrUndefined(l)||sf.base.isNullOrUndefined(l.querySelector("."+F))||(v(l.querySelector("."+F)),sf.base.detach(l.querySelector(".e-spinner-pane")))},e.prototype.removingEventCallback=function(e){var s=this.getLiElement(e);if(!(sf.base.isNullOrUndefined(s)||sf.base.isNullOrUndefined(s.querySelector("."+F))&&sf.base.isNullOrUndefined(s.querySelector("."+S)))){var i;t({target:i=s.querySelector("."+F)?s.querySelector("."+F):s.querySelector("."+S),width:"20px"}),f(i)}},e.prototype.updateFormData=function(e,t){if(t&&t.length>0&&t[0])for(var s=function(s){var i=t[s],a=Object.keys(i).map((function(e){return i[e]}));e.append(Object.keys(i)[0],a)},i=0;i<t.length;i++)s(i)},e.prototype.updateCustomheader=function(e,t){if(t.length>0&&t[0])for(var s=function(s){var i=t[s],a=Object.keys(i).map((function(e){return i[e]}));e.setRequestHeader(Object.keys(i)[0],a)},i=0;i<t.length;i++)s(i)},e.prototype.getFilesFromFolder=function(e){var t;if(this.filesEntries=[],t=this.multiple?e.dataTransfer.items:[e.dataTransfer.items[0]],this.checkDirectoryUpload(t))for(var s=function(s){var a=t[s].webkitGetAsEntry();if(a.isFile){var n=[];a.file((function(e){var t=a.fullPath;n.push({path:t,file:e})})),i.renderSelectedFiles(e,n,!0)}else a.isDirectory&&i.traverseFileTree(a,e)},i=this,a=0;a<t.length;a++)s(a)},e.prototype.checkDirectoryUpload=function(e){for(var t=0;e&&t<e.length;t++){if(e[t].webkitGetAsEntry().isDirectory)return!0}return!1},e.prototype.traverseFileTree=function(e,t){if(e.isFile)this.filesEntries.push(e);else if(e.isDirectory){var s=e.createReader();this.readFileFromDirectory(s,t)}},e.prototype.readFileFromDirectory=function(e,t){var s=this;e.readEntries((function(i){for(var a=0;a<i.length;a++)s.traverseFileTree(i[a],t);s.pushFilesEntries(t),i.length&&s.readFileFromDirectory(e)}))},e.prototype.pushFilesEntries=function(e){for(var t=this,s=[],i=function(i){a.filesEntries[i].file((function(a){if(t.filesEntries.length){var n=t.filesEntries[i].fullPath;s.push({path:n,file:a}),i===t.filesEntries.length-1&&(t.filesEntries=[],t.renderSelectedFiles(e,s,!0))}}))},a=this,n=0;n<this.filesEntries.length;n++)i(n)},e.prototype.onSelectFiles=function(e){var t;if(this.enabled)if(e&&e.detail&&e.detail.DropEvent&&"drop"===e.detail.DropEvent.type)if(this.directoryUpload)this.isSaveUrlNotConfigured?this.renderSelectedFiles(e.detail.DropEvent,this.FileDirectoryDetails,!0):this.getFilesFromFolder(e.detail.DropEvent);else{for(var s=this.sortFilesList=e.detail.DropEvent.dataTransfer.files,i=[],a=e.detail.DropEvent.dataTransfer.items,n=0;n<s.length;n++)for(var r=0;r<a.length;r++){var l=a[r].webkitGetAsEntry();l&&l.isFile&&l.name==s[n].name&&i.push(s[n])}var o=new DataTransfer;for(n=0;n<i.length;n++)o.items.add(i[n]);var u=o.files;"msie"!==this.browserName&&"edge"!==this.browserName&&"safari"!==this.browserName&&(this.element.files=u),u.length>0&&(t=this.multiple?this.sortFileList(u):[u[0]],this.renderSelectedFiles(e,t))}else t=[].slice.call(e.target.files),e.detail?this.renderSelectedFiles(e,t,e.detail.isDirectory,e.detail.isPaste):this.renderSelectedFiles(e,t)},e.prototype.getBase64=function(e){return new Promise((function(t,s){var i=new FileReader;i.readAsDataURL(e),i.onload=function(){return t(i.result)},i.onerror=function(e){return s(e)}}))},e.prototype.renderSelectedFiles=function(e,t,s,i){var a=this;this.base64String=[];var n={event:e,cancel:!1,filesData:[],isModified:!1,modifiedFilesData:[],progressInterval:"",isCanceled:!1,currentRequest:null,customFormData:null,type:e&&e.type};if(t.length<1)return n.isCanceled=!0,void(this.selectedEnabled&&this.dotNetRef.invokeMethodAsync("SelectedEvent",n));this.flag=!0;var r=[];this.multiple||(this.clearData(!0),this.actionCompleteCount=0,t=[t[0]]);for(var l=0;l<t.length;l++){var o=s?t[l].file:t[l];this.updateInitialFileDetails(e,t,o,l,r,s,i)}if(n.filesData=r,this.allowedExtensions.indexOf("*")>-1&&(this.allTypes=!0),this.enableHtmlSanitizer)for(l=0;l<r.length;l++){var u=sf.base.SanitizeHtmlHelper.beforeSanitize();if(sf.base.SanitizeHtmlHelper.serializeValue(u,r[l].name)!=r[l].name){var d=t[l].name.replace(/[\u00A0-\u9999<>\&]/g,(function(e){return"&#"+e.charCodeAt(0)+";"}));r[l].name=d,r[l].status=this.localizedTexts("invalidFileName"),r[l].statusCode="0"}}this.allTypes||(r=this.checkExtension(r)),this.selectedEnabled?this.dotNetRef.invokeMethodAsync("SelectedEvent",n).then((function(e){for(var t={},s=0,i=r;s<i.length;s++){t[(o=i[s]).id]=o}if(e.isModified&&e.filesData.length>0)for(var n=0,l=e.filesData;n<l.length;n++){var o,u=l[n];(o=t[u.id])&&(o.name=u.name)}a._internalRenderSelect(e,r)})):this._internalRenderSelect(n,r)},e.prototype.updateInitialFileDetails=function(e,t,s,i,a,n,r){var l={name:n?t[i].path.substring(1,t[i].path.length):r?sf.base.getUniqueID(s.name.substring(0,s.name.lastIndexOf(".")))+"."+this.getFileType(s.name):this.directoryUpload?t[i].webkitRelativePath:s.name,rawFile:s,size:s.size,status:this.localizedTexts("readyToUploadMessage"),type:this.getFileType(s.name),mimeContentType:s.type,lastModifiedDate:new Date(s.lastModified),validationMessages:this.validatedFileSize(s.size),statusCode:"1",id:sf.base.getUniqueID(s.name.substring(0,s.name.lastIndexOf(".")))+"."+this.getFileType(s.name)};r&&(l.fileSource="paste"),l.status=""!==l.validationMessages.minSize?this.localizedTexts("invalidMinFileSize"):""!==l.validationMessages.maxSize?this.localizedTexts("invalidMaxFileSize"):l.status,""===l.validationMessages.minSize&&""===l.validationMessages.maxSize||(l.statusCode="0"),a.push(l)},e.prototype._internalRenderSelect=function(e,t){if(!e.cancel){if(this.currentRequestHeader=e.currentRequest,this.customFormDatas=e.customFormData,this.selectedFiles=this.selectedFiles.concat(t),this.btnTabIndex=this.disableKeyboardNavigation?"-1":"0",this.showFileList){if(e.isModified&&e.modifiedFilesData.length>0){for(var s=0;s<e.modifiedFilesData.length;s++)for(var i=0;i<t.length;i++)e.modifiedFilesData[s].id===t[i].id&&(e.modifiedFilesData[s].rawFile=t[i].rawFile);var a=this.allTypes?e.modifiedFilesData:this.checkExtension(e.modifiedFilesData);this.updateSortedFileList(a),this.filesData=this.filesData.concat(a),this.isForm&&!this.allowUpload()||this.isSaveUrlNotConfigured||this.isValidTemplate||this.checkAutoUpload(a)}else{if(this.createFileList(t,!0),this.autoUpload&&this.sequenceUpload&&this.sequentialUpload&&!this.isSaveUrlNotConfigured&&this.filesData.length>0&&"2"!==this.filesData[this.filesData.length-1].statusCode&&"0"!==this.filesData[this.filesData.length-1].statusCode)return void(this.filesData=this.filesData.concat(t));this.isSaveUrlNotConfigured||this.isValidTemplate||(this.filesData=this.filesData.concat(t)),this.isForm&&!this.allowUpload()||this.isSaveUrlNotConfigured||this.isValidTemplate||this.checkAutoUpload(t)}sf.base.isNullOrUndefined(e.progressInterval)||""===e.progressInterval||(this.progressInterval=e.progressInterval)}else{if(this.filesData=this.filesData.concat(t),this.isSaveUrlNotConfigured)return void this.dotNetRef.invokeMethodAsync("UpdateServerFileData",this.filesData,this.isForm);this.autoUpload&&(this.sequentialUpload?this.sequenceUpload(this.filesData):this.upload(this.filesData,!0))}if(!this.isSaveUrlNotConfigured)for(var n=0;n<this.filesData.length;n++)"0"===this.filesData[n].statusCode&&this.checkActionComplete(!0);this.isFirstFileOnSelection=!0}},e.prototype.allowUpload=function(){var e=!1;return this.isForm&&!sf.base.isNullOrUndefined(this.asyncSettings.saveUrl)&&""!==this.asyncSettings.saveUrl&&(e=!0),e},e.prototype.isFormUpload=function(){var e=!1;return!this.isForm||!sf.base.isNullOrUndefined(this.asyncSettings.saveUrl)&&""!==this.asyncSettings.saveUrl||!sf.base.isNullOrUndefined(this.asyncSettings.removeUrl)&&""!==this.asyncSettings.removeUrl||(e=!0),e},e.prototype.clearData=function(e){sf.base.isNullOrUndefined(this.listParent)||this.isSaveUrlNotConfigured||this.isValidTemplate||(sf.base.detach(this.listParent),this.listParent=null),"msie"===this.browserName||e||(this.element.value=""),this.fileList=[],this.filesData=[],this.isSaveUrlNotConfigured||this.isValidTemplate?this.dotNetRef.invokeMethodAsync("ClearAllFile"):this.removeActionButtons()},e.prototype.updateSortedFileList=function(e){var t=sf.base.createElement("div",{id:"clonewrapper"}),s=-1;if(this.listParent){for(var i=0;i<this.listParent.querySelectorAll("li").length;i++){var a=this.listParent.querySelectorAll("li")[i];t.appendChild(a.cloneNode(!0))}this.removeActionButtons();var n=[].slice.call(t.childNodes);this.createParentUL();for(var r=0;r<e.length;r++){for(var l=0;l<this.filesData.length;l++)this.filesData[l].name===e[r].name&&(this.listParent.appendChild(n[l]),sf.base.EventHandler.add(n[l].querySelector(".e-icons"),"click",this.removeFiles,this),this.fileList.push(n[l]),s=r);s!==r&&this.createFileList([e[r]])}}else this.createFileList(e)},e.prototype.isBlank=function(e){return!e||/^\s*$/.test(e)},e.prototype.checkExtension=function(e){var t=e;if(!this.isBlank(this.allowedExtensions)){for(var s=[],i=0,a=this.allowedExtensions.split(",");i<a.length;i++){var n=a[i];s.push(n.trim().toLocaleLowerCase())}for(var r=0;r<e.length;r++)-1===s.indexOf(("."+e[r].type).toLocaleLowerCase())&&(e[r].status=this.localizedTexts("invalidFileType"),e[r].statusCode="0")}return t},e.prototype.validatedFileSize=function(e){var t="",s="";return e<this.minFileSize?t=this.localizedTexts("invalidMinFileSize"):e>this.maxFileSize?s=this.localizedTexts("invalidMaxFileSize"):(t="",s=""),{minSize:t,maxSize:s}},e.prototype.isPreLoadFile=function(e){var t=!1;if(this.files)for(var s=0;s<this.files.length;s++)if(this.files[s].name===e.name.slice(0,e.name.lastIndexOf("."))&&this.files[s].type===e.type){t=!0;break}return t},e.prototype.createParentUL=function(){sf.base.isNullOrUndefined(this.listParent)&&(this.listParent=sf.base.createElement("ul",{className:"e-upload-files"}),this.uploadWrapper.appendChild(this.listParent))},e.prototype.formFileList=function(e,t){var s=sf.base.createElement("li",{className:g});s.setAttribute("data-files-count",e.length+"");for(var i,a=sf.base.createElement("span",{className:"e-file-container"}),n=0,r=e;n<r.length;n++){var l=r[n],o=sf.base.createElement("span",{className:"e-file-name"});o.innerHTML=this.getFileNameOnly(l.name);var u=sf.base.createElement("span",{className:"e-file-type"}),d=this.getFileType(l.name);if(u.innerHTML="."+d,d||u.classList.add("e-hidden"),this.enableRtl){var c=sf.base.createElement("span",{className:"e-rtl-container"});c.appendChild(u),c.appendChild(o),a.appendChild(c)}else a.appendChild(o),a.appendChild(u);this.truncateName(o),i=this.formValidateFileInfo(l,s)}s.appendChild(a),this.setListToFileInfo(e,s);var h=this.listParent.querySelectorAll("li").length,f=sf.base.createElement("span");if(s.classList.contains("e-file-invalid")?(f.classList.add(y),f.classList.add("e-file-invalid"),f.innerText=e.length>1?this.localizedTexts("invalidFileSelection"):i):(f.classList.add(e.length>1?"e-file-information":"e-file-size"),f.innerText=e.length>1?this.localizedTexts("totalFiles")+": "+e.length+" , "+this.localizedTexts("size")+": "+this.bytesToSize(this.getFileSize(e)):this.bytesToSize(e[0].size),this.createFormInput(e)),a.appendChild(f),sf.base.isNullOrUndefined(s.querySelector(".e-icons"))){var p=sf.base.createElement("span",{className:"e-icons",attrs:{tabindex:this.btnTabIndex}});"msie"===this.browserName&&p.classList.add("e-msie"),p.setAttribute("title",this.localizedTexts("remove")),p.setAttribute("aria-label",this.localizedTexts("remove")),s.appendChild(a),s.appendChild(p),sf.base.EventHandler.add(p,"click",this.removeFiles,this),p.classList.add(S)}var v={element:s,fileInfo:this.mergeFileInfo(e,s),index:h,isPreload:this.isPreLoadFile(this.mergeFileInfo(e,s))};this.fileListRenderEnabled&&this.dotNetRef.invokeMethodAsync("FileListRenderingEvent",v),this.listParent.appendChild(s),this.fileList.push(s)},e.prototype.formValidateFileInfo=function(e,t){var s=e.status,i=this.validatedFileSize(e.size);""===i.minSize&&""===i.maxSize||(this.addInvalidClass(t),s=""!==i.minSize?this.localizedTexts("invalidMinFileSize"):""!==i.maxSize?this.localizedTexts("invalidMaxFileSize"):s);var a=this.checkExtension(this.getFilesInArray(e))[0].status;return a===this.localizedTexts("invalidFileType")&&(this.addInvalidClass(t),s=a),s},e.prototype.addInvalidClass=function(e){e.classList.add("e-file-invalid")},e.prototype.createFormInput=function(e){var t=this.element.cloneNode(!0);t.classList.add("e-hidden-file-input");for(var s=0,i=e;s<i.length;s++){i[s].input=t}t.setAttribute("name",this.uploaderName),this.uploadWrapper.querySelector(".e-file-select").appendChild(t),"msie"!==this.browserName&&"edge"!==this.browserName&&(this.element.value="")},e.prototype.getFileSize=function(e){for(var t=0,s=0,i=e;s<i.length;s++){t+=i[s].size}return t},e.prototype.mergeFileInfo=function(e,t){for(var s={name:"",rawFile:"",size:0,status:"",type:"",validationMessages:{minSize:"",maxSize:""},statusCode:"1",list:t},i=[],a="",n=0,r=e;n<r.length;n++){var l=r[n];i.push(l.name),a=l.type}return s.name=i.join(", "),s.size=this.getFileSize(e),s.type=a,s.status=this.statusForFormUpload(e,t),s},e.prototype.statusForFormUpload=function(e,t){for(var s,i=!0,a=0,n=e;a<n.length;a++){var r=n[a];s=r.status;var l=this.validatedFileSize(r.size);""===l.minSize&&""===l.maxSize||(i=!1,s=""!==l.minSize?this.localizedTexts("invalidMinFileSize"):""!==l.maxSize?this.localizedTexts("invalidMaxFileSize"):s);var o=this.checkExtension(this.getFilesInArray(r))[0].status;o===this.localizedTexts("invalidFileType")&&(i=!1,s=o)}return i?s=this.localizedTexts("totalFiles")+": "+e.length+" , "+this.localizedTexts("size")+": "+this.bytesToSize(this.getFileSize(e)):(t.classList.add("e-file-invalid"),s=e.length>1?this.localizedTexts("invalidFileSelection"):s),s},e.prototype.createFileList=function(e,t){if(this.isSaveUrlNotConfigured||this.isValidTemplate)if(!this.isSaveUrlNotConfigured&&this.isValidTemplate){var s=t?this.filesData=this.filesData.concat(e):e;this.dotNetRef.invokeMethodAsync("CreateFileList",s,this.isForm)}else this.dotNetRef.invokeMethodAsync("CreateFileList",e,this.isForm);else if(this.createParentUL(),this.isFormUpload())this.uploadWrapper.classList.add("e-form-upload"),this.formFileList(e,this.element.files);else for(var i=0,a=e;i<a.length;i++){var n=a[i],r=sf.base.createElement("li",{className:g,attrs:{"data-file-name":n.name,"data-files-count":"1"}}),l=sf.base.createElement("span",{className:"e-file-container"}),o=sf.base.createElement("span",{className:"e-file-name",attrs:{title:n.name}});o.innerHTML=this.getFileNameOnly(n.name);var u=sf.base.createElement("span",{className:"e-file-type"}),d=this.getFileType(n.name);if(u.innerHTML="."+d,d||u.classList.add("e-hidden"),this.enableRtl){var c=sf.base.createElement("span",{className:"e-rtl-container"});c.appendChild(u),c.appendChild(o),l.appendChild(c)}else l.appendChild(o),l.appendChild(u);var h=sf.base.createElement("span",{className:"e-file-size"});h.innerHTML=this.bytesToSize(n.size),l.appendChild(h);var f=sf.base.createElement("span",{className:y});l.appendChild(f),f.innerHTML=n.status,r.appendChild(l);var p=sf.base.createElement("span",{className:" e-icons",attrs:{tabindex:this.btnTabIndex}});"msie"===this.browserName&&p.classList.add("e-msie"),p.setAttribute("title",this.localizedTexts("remove")),p.setAttribute("aria-label",this.localizedTexts("remove")),p.setAttribute("role","button"),r.appendChild(p),sf.base.EventHandler.add(p,"click",this.removeFiles,this),"2"===n.statusCode?(f.classList.add(k),p.classList.add(F),p.setAttribute("title",this.localizedTexts("delete")),p.setAttribute("aria-label",this.localizedTexts("delete")),p.setAttribute("role","button")):"1"!==n.statusCode&&(f.classList.remove(k),f.classList.add("e-validation-fails")),this.autoUpload&&"1"===n.statusCode&&""!==this.asyncSettings.saveUrl&&(f.innerHTML=""),p.classList.contains(F)||p.classList.add(S);var v={element:r,fileInfo:n,index:e.indexOf(n),isPreload:this.isPreLoadFile(n)};this.fileListRenderEnabled&&this.dotNetRef.invokeMethodAsync("FileListRenderingEvent",v),this.listParent.appendChild(r),this.fileList.push(r),this.truncateName(o);var m=this.flag;this.isPreLoadFile(n)&&(this.flag=!1,this.checkActionComplete(!0),this.flag=m)}},e.prototype.getSlicedName=function(e){var t;t=e.textContent,e.dataset.tail=t.slice(t.length-10)},e.prototype.setListToFileInfo=function(e,t){for(var s=0,i=e;s<i.length;s++){i[s].list=t}},e.prototype.truncateName=function(e){var t=e;("edge"!==this.browserName&&t.offsetWidth<t.scrollWidth||t.offsetWidth+1<t.scrollWidth)&&this.getSlicedName(t)},e.prototype.getFileType=function(e){var t,s=e.lastIndexOf(".");return s>=0&&(t=e.substring(s+1)),t||""},e.prototype.getFileNameOnly=function(e){var t=this.getFileType(e);return e.split("."+t)[0]},e.prototype.setInitialAttributes=function(){if(this.initialAttr.accept&&this.element.setAttribute("accept",this.initialAttr.accept),this.initialAttr.disabled&&this.element.setAttribute("disabled","disabled"),this.initialAttr.multiple){var e=document.createAttribute("multiple");this.element.setAttributeNode(e)}},e.prototype.filterfileList=function(e){for(var t=[],s=0;s<e.length;s++)this.getLiElement(e[s]).classList.contains(k)||t.push(e[s]);return t},e.prototype.updateStatus=function(e,t,s,i){if(void 0===i&&(i=!0),""!==t&&!sf.base.isNullOrUndefined(t)&&""!==s&&!sf.base.isNullOrUndefined(s))if(this.isSaveUrlNotConfigured)for(var a=0;a<this.filesData.length;a++)this.filesData[a].name===e.name&&(this.filesData[a].status=t,this.filesData[a].statusCode=s);else e.status=t,e.statusCode=s;if(i){var n=this.getLiElement(e);sf.base.isNullOrUndefined(n)||sf.base.isNullOrUndefined(n.querySelector("."+y))||""===t||sf.base.isNullOrUndefined(t)||(n.querySelector("."+y).textContent=t)}return e},e.prototype.getLiElement=function(e){for(var t,s=0;s<this.filesData.length;s++)(sf.base.isNullOrUndefined(this.filesData[s].id)||sf.base.isNullOrUndefined(e.id)?this.filesData[s].name===e.name:this.filesData[s].name===e.name&&this.filesData[s].id===e.id)&&(t=s);return this.fileList[t]},e.prototype.createProgressBar=function(e){var t=sf.base.createElement("span",{className:E}),s=sf.base.createElement("progressbar",{className:N,attrs:{value:"0",max:"100"}}),i=sf.base.createElement("span",{className:"e-progress-inner-wrap"});s.setAttribute("style","width: 0%");var a=sf.base.createElement("span",{className:"e-progress-bar-text"});a.textContent="0%",i.appendChild(s),t.appendChild(i),t.appendChild(a),e.querySelector(".e-file-container").appendChild(t)},e.prototype.updateProgressbar=function(e,t){if(!isNaN(Math.round(e.loaded/e.total*100))&&!sf.base.isNullOrUndefined(t.querySelector("."+N)))if(sf.base.isNullOrUndefined(this.progressInterval)||""===this.progressInterval)this.changeProgressValue(t,Math.round(e.loaded/e.total*100).toString()+"%");else{var s=Math.round(e.loaded/e.total*100)%parseInt(this.progressInterval,10);0!==s&&100!==s||this.changeProgressValue(t,Math.round(e.loaded/e.total*100).toString()+"%")}},e.prototype.changeProgressValue=function(e,t){e.querySelector("."+N).setAttribute("style","width:"+t),e.querySelector(".e-progress-bar-text").textContent=t},e.prototype.uploadInProgress=function(e,t,s,i){var a=this.getLiElement(t);if(!sf.base.isNullOrUndefined(a)||s){if(sf.base.isNullOrUndefined(a))this.cancelUploadingFile(t,e,i);else{"5"===t.statusCode&&this.cancelUploadingFile(t,e,i,a),a.querySelectorAll("."+E).length>0||!a.querySelector("."+y)||(a.querySelector("."+y).classList.add("e-upload-progress"),this.createProgressBar(a),this.updateProgressBarClasses(a,"e-upload-progress"),a.querySelector("."+y).classList.remove(A)),this.updateProgressbar(e,a);var n=a.querySelector("."+S);sf.base.isNullOrUndefined(n)||(n.classList.add(C,"e-upload-progress"),n.setAttribute("title",this.localizedTexts("abort")),n.setAttribute("aria-label",this.localizedTexts("abort")),n.classList.remove(S))}var r={e:e,operation:"upload",file:this.updateStatus(t,this.localizedTexts("inProgress"),"3"),lengthComputable:e.lengthComputable,loaded:e.loaded,total:e.total};this.progressingEnabled&&this.dotNetRef.invokeMethodAsync("ProgressEvent",r)}},e.prototype.cancelEventCallback=function(e,t,s,i){var a=this;if(i.cancel){if(e.statusCode="3",!sf.base.isNullOrUndefined(s)){var n=s.querySelector("."+C);sf.base.isNullOrUndefined(n)||(v(n),sf.base.detach(s.querySelector(".e-spinner-pane")))}}else{t.emitError=!1,t.httpRequest.abort();var r=new FormData;if("5"===e.statusCode){var l=this.element.getAttribute("name");r.append(l,e.name),r.append("cancel-uploading",e.name);var o=new sf.base.Ajax(this.asyncSettings.removeUrl,"POST",!0,null);o.emitError=!1,o.onLoad=function(t){return a.removecanceledFile(t,e),{}},o.send(r)}}},e.prototype.cancelUploadingFile=function(e,t,s,i){var a=this;if("5"===e.statusCode){var n={event:t,fileData:e,cancel:!1};this.cancelEnabled?this.dotNetRef.invokeMethodAsync("CancelingEvent",n).then((function(t){a.cancelEventCallback(e,s,i,t)})):this.cancelEventCallback(e,s,i,n)}},e.prototype.removecanceledFile=function(e,t){var s=this.getLiElement(t);if(!(sf.base.isNullOrUndefined(s)||s.querySelector("."+U)||sf.base.isNullOrUndefined(s.querySelector("."+C)))){this.updateStatus(t,this.localizedTexts("fileUploadCancel"),"5"),this.renderFailureState(e,t,s);var i=s.querySelector("."+S);sf.base.isNullOrUndefined(s)||(v(i),sf.base.detach(s.querySelector(".e-spinner-pane")));var a={event:e,response:e&&e.currentTarget?this.getResponse(e):null,operation:"cancel",file:t};this.successEnabled&&this.dotNetRef.invokeMethodAsync("SuccessEvent",a)}},e.prototype.renderFailureState=function(e,t,s){var i=this;this.updateProgressBarClasses(s,A),this.removeProgressbar(s,"failure"),sf.base.isNullOrUndefined(s.querySelector(".e-file-status"))||s.querySelector(".e-file-status").classList.add(A);var a=s.querySelector("."+C);sf.base.isNullOrUndefined(a)||(a.classList.remove(C,"e-upload-progress"),a.classList.add(S),a.setAttribute("title",this.localizedTexts("remove")),a.setAttribute("aria-label",this.localizedTexts("remove")),this.pauseButton=sf.base.createElement("span",{className:"e-icons e-file-reload-btn",attrs:{tabindex:this.btnTabIndex}}),a.parentElement.insertBefore(this.pauseButton,a),this.pauseButton.setAttribute("title",this.localizedTexts("retry")),this.pauseButton.setAttribute("aria-label",this.localizedTexts("retry")),s.querySelector("."+U).addEventListener("click",(function(e){i.reloadcanceledFile(e,t,s,!1)}),!1))},e.prototype.reloadcanceledFile=function(e,t,s,i){t.statusCode="1",t.status=this.localizedTexts("readyToUploadMessage"),i||(s.querySelector("."+y).classList.remove(A),sf.base.isNullOrUndefined(s.querySelector("."+U))||sf.base.detach(s.querySelector("."+U)),this.pauseButton=null),s.classList.add("e-restrict-retry"),this.upload([t])},e.prototype.uploadComplete=function(e,t,s){var i=e.target;if(4===i.readyState&&i.status>=200&&i.status<=299){var a=this.getLiElement(t);if(sf.base.isNullOrUndefined(a)&&(!s||sf.base.isNullOrUndefined(s)))return;if(!sf.base.isNullOrUndefined(a)){this.updateProgressBarClasses(a,k),this.removeProgressbar(a,"success");var n=a.querySelector("."+C);sf.base.isNullOrUndefined(n)||(n.classList.add(F),n.setAttribute("title",this.localizedTexts("delete")),n.setAttribute("aria-label",this.localizedTexts("delete")),n.classList.remove(C),n.classList.remove("e-upload-progress"))}this.raiseSuccessEvent(e,t)}else this.uploadFailed(e,t)},e.prototype.getResponse=function(e){var t=e.currentTarget;return{readyState:t.readyState,statusCode:t.status,statusText:t.statusText,responseText:t.responseText,headers:t.getAllResponseHeaders(),withCredentials:t.withCredentials}},e.prototype.serverRemoveIconBindEvent=function(){if(this.uploadWrapper&&this.isSaveUrlNotConfigured)for(var e=[].slice.call(this.uploadWrapper.querySelectorAll("ul li")),t=0;t<e.length;t++){var s=e[t]?e[t].querySelector(".e-icons"):null;s&&(sf.base.EventHandler.remove(s,"click",this.removeFiles),sf.base.EventHandler.add(s,"click",this.removeFiles,this))}},e.prototype.raiseSuccessEvent=function(e,t){var s=this,i=e&&e.currentTarget?this.getResponse(e):null,a=this.localizedTexts("uploadSuccessMessage"),n={e:e,response:i,operation:"upload",file:this.updateStatus(t,a,"2",!1),statusText:a};if(!this.isSaveUrlNotConfigured){var r=this.getLiElement(t);if(!sf.base.isNullOrUndefined(r)){var l=r.querySelector(".e-spinner-pane");sf.base.isNullOrUndefined(l)||(v(r),sf.base.detach(l))}}this.successEnabled?this.dotNetRef.invokeMethodAsync("SuccessEvent",n).then((function(e){s.successEventCallback(t,e)})):this.successEventCallback(t,n)},e.prototype.successEventCallback=function(e,t){this.updateStatus(e,t.statusText,"2"),this.uploadedFilesData.push(e),!this.isSaveUrlNotConfigured&&this.changeEnabled&&this.dotNetRef.invokeMethodAsync("ChangeEvent",{files:this.uploadedFilesData}),!this.isSaveUrlNotConfigured&&this.isValidTemplate&&this.dotNetRef.invokeMethodAsync("CreateFileList",this.filesData,this.isForm),this.checkActionButtonStatus(),this.sequentialUpload&&!this.showFileList&&this.uploadSequential(),this.fileList.length>0&&(this.getLiElement(e).classList.contains("e-restrict-retry")?this.getLiElement(e).classList.remove("e-restrict-retry"):(this.uploadSequential(),this.checkActionComplete(!0)))},e.prototype.uploadFailed=function(e,t){var s=this,i=this.getLiElement(t),a=e&&e.currentTarget?this.getResponse(e):null,n=this.localizedTexts("uploadFailedMessage"),r={e:e,response:a,operation:"upload",file:this.updateStatus(t,n,"0",!1),statusText:n,retryFiles:null};sf.base.isNullOrUndefined(i)||this.renderFailureState(e,t,i),this.failuredEnabled?this.dotNetRef.invokeMethodAsync("FailureEvent",r).then((function(e){s.failureEventCallback(t,e)})):this.failureEventCallback(t,r)},e.prototype.failureEventCallback=function(e,t){this.updateStatus(e,t.statusText,"0"),this.checkActionButtonStatus(),this.uploadSequential(),this.checkActionComplete(!0);var s=t.retryFiles;null!=s&&this.retry(s)},e.prototype.uploadSequential=function(){this.sequentialUpload&&(this.autoUpload?this.checkAutoUpload(this.filesData):this.uploadButtonClick())},e.prototype.checkActionComplete=function(e){e?++this.actionCompleteCount:--this.actionCompleteCount,this.raiseActionComplete()},e.prototype.raiseActionComplete=function(){if(this.filesData.length===this.actionCompleteCount&&this.flag){this.flag=!1;var e={fileData:[]};e.fileData=this.getSelectedFileStatus(this.selectedFiles),this.actionCompleteEnabled&&this.dotNetRef.invokeMethodAsync("ActionCompleteEvent",e)}},e.prototype.getSelectedFileStatus=function(e){for(var t=[],s=0,i=0;i<e.length;i++)for(var a=e[i],n=0;n<this.filesData.length;n++)this.filesData[n].name===a.name&&this.filesData[n].status===a.status&&(t[s]=this.filesData[n],++s);return t},e.prototype.updateProgressBarClasses=function(e,t){var s=e.querySelector("."+N);sf.base.isNullOrUndefined(s)||s.classList.add(t)},e.prototype.removeProgressbar=function(e,t){var s=this;sf.base.isNullOrUndefined(e.querySelector("."+E))||(this.progressAnimation=new sf.base.Animation({duration:1250}),this.progressAnimation.animate(e.querySelector("."+E),{name:"FadeOut"}),this.progressAnimation.animate(e.querySelector(".e-progress-bar-text"),{name:"FadeOut"}),setTimeout((function(){s.animateProgressBar(e,t)}),750))},e.prototype.animateProgressBar=function(e,t){"success"===t?(e.classList.add(k),sf.base.isNullOrUndefined(e.querySelector("."+y))||(e.querySelector("."+y).classList.remove("e-upload-progress"),this.progressAnimation.animate(e.querySelector("."+y),{name:"FadeIn"}),e.querySelector("."+y).classList.add(k),e.querySelector("."+y).classList.remove(A))):sf.base.isNullOrUndefined(e.querySelector("."+y))||(e.querySelector("."+y).classList.remove("e-upload-progress"),this.progressAnimation.animate(e.querySelector("."+y),{name:"FadeIn"}),e.querySelector("."+y).classList.add(A)),e.querySelector("."+E)&&sf.base.detach(e.querySelector("."+E))},e.prototype.localizedTexts=function(e){return this.localeText[e]},e.prototype.chunkUpload=function(e,t,s){var i=Math.min(this.asyncSettings.chunkSize,e.size),a={chunkIndex:0,blob:e.rawFile.slice(0,i),file:e,start:0,end:i,retryCount:0,request:null};this.sendRequest(e,a,t,s)},e.prototype.sendRequest=function(e,t,s,i){var a=this,n=new FormData;if(!e.rawFile)for(var r=0;r<this.selectedFiles.length;r++)e.id===this.selectedFiles[r].id&&(e.rawFile=this.selectedFiles[r].rawFile);var l=e.rawFile.slice(t.start,t.end);n.append("chunkFile",l,e.name),n.append(this.uploaderName,l,e.name),n.append("chunk-index",t.chunkIndex.toString()),n.append("chunkIndex",t.chunkIndex.toString());var o=Math.max(Math.ceil(e.size/this.asyncSettings.chunkSize),1);n.append("total-chunk",o.toString()),n.append("totalChunk",o.toString());var u=new sf.base.Ajax({url:this.asyncSettings.saveUrl,type:"POST",async:!0,contentType:null});u.emitError=!1,u.onLoad=function(e){return a.chunkUploadComplete(e,t,s),{}},u.onUploadProgress=function(e){return a.chunkUploadInProgress(e,t,s),{}};var d={fileData:e,customFormData:[],cancel:!1,chunkSize:0===this.asyncSettings.chunkSize?null:this.asyncSettings.chunkSize};u.beforeSend=function(s){d.currentRequest=u.httpRequest,d.currentChunkIndex=t.chunkIndex,a.currentRequestHeader&&a.updateCustomheader(u.httpRequest,a.currentRequestHeader),a.customFormDatas&&a.updateFormData(n,a.customFormDatas),0===d.currentChunkIndex?a.uploadingEnabled?a.dotNetRef.invokeMethodAsync("UploadingEvent",d).then((function(t){a.uploadingEventCallback(n,t,s,e)})):a.uploadingEventCallback(n,d,s,e):a.chunkUploadingEnabled?a.dotNetRef.invokeMethodAsync("ChunkUploadingEvent",d).then((function(t){a.uploadingEventCallback(n,t,s,e)})):a.uploadingEventCallback(n,d,s,e)},u.onError=function(e){return a.chunkUploadFailed(e,t,s),{}},u.send(n),t.request=u},e.prototype.uploadingEventCallback=function(e,t,s,i){t.cancel?this.eventCancelByArgs(s,t,i):this.updateFormData(e,t.customFormData)},e.prototype.eventCancelByArgs=function(e,t,s){var i=this;if(e.cancel=!0,"5"!==t.fileData.statusCode){var a=this.getLiElement(t.fileData);a.querySelector("."+y).innerHTML=this.localizedTexts("fileUploadCancel"),a.querySelector("."+y).classList.add(A),t.fileData.statusCode="5",t.fileData.status=this.localizedTexts("fileUploadCancel"),this.pauseButton=sf.base.createElement("span",{className:"e-icons e-file-reload-btn",attrs:{tabindex:this.btnTabIndex}});var n=a.querySelector("."+S);n.parentElement.insertBefore(this.pauseButton,n),this.pauseButton.setAttribute("title",this.localizedTexts("retry")),this.pauseButton.setAttribute("aria-label",this.localizedTexts("retry")),this.pauseButton.addEventListener("click",(function(e){i.reloadcanceledFile(e,s,a)}),!1),this.checkActionButtonStatus()}},e.prototype.checkChunkUpload=function(){return!(this.asyncSettings.chunkSize<=0||sf.base.isNullOrUndefined(this.asyncSettings.chunkSize))},e.prototype.chunkUploadComplete=function(e,t,s){var i,a=this,n=e.target;if(4===n.readyState&&n.status>=200&&n.status<300){var r=e&&e.currentTarget?this.getResponse(e):null,l=Math.max(Math.ceil(t.file.size/this.asyncSettings.chunkSize),1),o={event:e,file:t.file,chunkIndex:t.chunkIndex,totalChunk:l,chunkSize:this.asyncSettings.chunkSize,response:r};if(this.chunkSuccessEnabled&&this.dotNetRef.invokeMethodAsync("ChunkSuccessEvent",o),!sf.base.isNullOrUndefined(s)&&s||(i=this.getLiElement(t.file)),this.updateMetaData(t),t.end===t.file.size&&(t.file.statusCode="3"),"5"===t.file.statusCode){var u={event:e,fileData:t.file,cancel:!1};this.cancelEnabled?this.dotNetRef.invokeMethodAsync("CancelingEvent",u).then((function(e){a.cancelingEventCallback(t,i,n,s,e)})):this.cancelingEventCallback(t,i,n,s,u)}else{if(l-1===t.chunkIndex&&l>t.chunkIndex){var d=this.pausedData.indexOf(t);return d>=0&&this.pausedData.splice(d,1),sf.base.isNullOrUndefined(this.template)&&(sf.base.isNullOrUndefined(s)||!s)&&i&&(i&&!sf.base.isNullOrUndefined(i.querySelector("."+w))&&sf.base.detach(i.querySelector("."+w)),this.removeChunkProgressBar(t)),void this.raiseSuccessEvent(e,t.file)}"4"!==t.file.statusCode&&this.sendNextRequest(t)}}else this.chunkUploadFailed(e,t)},e.prototype.cancelingEventCallback=function(e,t,s,i,a){var n=this;if(a.cancel){e.file.statusCode="3";var r=t.querySelector("."+C);sf.base.isNullOrUndefined(t)||sf.base.isNullOrUndefined(r)||(v(r),sf.base.detach(t.querySelector(".e-spinner-pane"))),this.sendNextRequest(e)}else{e.request.emitError=!1,s.abort();var l=new FormData,o=this.element.getAttribute("name");l.append(o,e.file.name),l.append("cancel-uploading",e.file.name),l.append("cancelUploading",e.file.name);var u=new sf.base.Ajax(this.asyncSettings.removeUrl,"POST",!0,null);u.emitError=!1,u.onLoad=function(t){return n.removeChunkFile(t,e,i),{}},u.send(l)}},e.prototype.sendNextRequest=function(e){e.start=e.end,e.end+=this.asyncSettings.chunkSize,e.end=Math.min(e.end,e.file.size),e.chunkIndex+=1,this.sendRequest(e.file,e)},e.prototype.removeChunkFile=function(e,t,s){if(sf.base.isNullOrUndefined(this.template)&&sf.base.isNullOrUndefined(s)&&!s){var i=this.getLiElement(t.file),a=i.querySelector("."+C),n=a;this.updateStatus(t.file,this.localizedTexts("fileUploadCancel"),"5"),this.updateProgressBarClasses(i,A),this.removeProgressbar(i,"failure"),a&&a.classList.remove(C),a&&a.classList.add(S),a&&a.setAttribute("title",this.localizedTexts("remove")),a&&a.setAttribute("aria-label",this.localizedTexts("remove"));var r=i.querySelector("."+w);r&&r.classList.add(U),r&&r.classList.remove(w),r&&r.setAttribute("title",this.localizedTexts("retry")),r&&r.setAttribute("aria-label",this.localizedTexts("retry")),sf.base.isNullOrUndefined(i)||sf.base.isNullOrUndefined(a)||sf.base.isNullOrUndefined(i.querySelector(".e-spinner-pane"))||(v(n),sf.base.detach(i.querySelector(".e-spinner-pane")))}},e.prototype.pauseUpload=function(e,t,s){e.file.statusCode="4",e.file.status=this.localizedTexts("pause"),this.updateMetaData(e);var i={event:t||null,file:e.file,chunkIndex:e.chunkIndex,chunkCount:Math.round(e.file.size/this.asyncSettings.chunkSize),chunkSize:this.asyncSettings.chunkSize};this.abortUpload(e,s,i)},e.prototype.abortUpload=function(e,t,s){"4"!==e.file.statusCode&&(e.request.emitError=!1,e.request.httpRequest.abort());var i=this.getLiElement(e.file);if(sf.base.isNullOrUndefined(this.template)&&(sf.base.isNullOrUndefined(t)||!t)){var a=i.querySelector("."+w);a.classList.remove(w),a.classList.add("e-file-play-btn"),a.setAttribute("title",this.localizedTexts("resume")),a.setAttribute("aria-label",this.localizedTexts("resume")),a.nextElementSibling.classList.add(S),a.nextElementSibling.classList.remove(C),a.nextElementSibling.setAttribute("title",this.localizedTexts("remove")),a.nextElementSibling.setAttribute("aria-label",this.localizedTexts("remove"))}for(var n=0;n<this.pausedData.length;n++)this.pausedData[n].file.name===e.file.name&&this.pausedData.splice(n,1);this.pausedData.push(e),this.pausedEnabled&&this.dotNetRef.invokeMethodAsync("PausingEvent",s)},e.prototype.resumeUpload=function(e,t,s){var i,a=this.getLiElement(e.file);sf.base.isNullOrUndefined(a)||(i=a.querySelector(".e-file-play-btn")),sf.base.isNullOrUndefined(i)||!sf.base.isNullOrUndefined(s)&&s||(i.classList.remove("e-file-play-btn"),i.classList.add(w),i.setAttribute("title",this.localizedTexts("pause")),i.setAttribute("aria-label",this.localizedTexts("pause")),i.nextElementSibling.classList.remove(S),i.nextElementSibling.classList.add(C),i.nextElementSibling.setAttribute("title",this.localizedTexts("abort")),i.nextElementSibling.setAttribute("aria-label",this.localizedTexts("abort"))),e.file.status=this.localizedTexts("inProgress"),e.file.statusCode="3",this.updateMetaData(e);var n={event:t||null,file:e.file,chunkIndex:e.chunkIndex,chunkCount:Math.round(e.file.size/this.asyncSettings.chunkSize),chunkSize:this.asyncSettings.chunkSize};this.resumeEnabled&&this.dotNetRef.invokeMethodAsync("ResumingEvent",n);for(var r=0;r<this.pausedData.length;r++)this.pausedData[r].end===this.pausedData[r].file.size?this.chunkUploadComplete(t,e,s):this.pausedData[r].file.name===e.file.name&&(this.pausedData[r].start=this.pausedData[r].end,this.pausedData[r].end=this.pausedData[r].end+this.asyncSettings.chunkSize,this.pausedData[r].end=Math.min(this.pausedData[r].end,this.pausedData[r].file.size),this.pausedData[r].chunkIndex=this.pausedData[r].chunkIndex+1,this.sendRequest(this.pausedData[r].file,this.pausedData[r],s))},e.prototype.updateMetaData=function(e){-1===this.uploadMetaData.indexOf(e)||this.uploadMetaData.splice(this.uploadMetaData.indexOf(e),1),this.uploadMetaData.push(e)},e.prototype.removeChunkProgressBar=function(e){var t=this.getLiElement(e.file);if(!sf.base.isNullOrUndefined(t)){this.updateProgressBarClasses(t,k),this.removeProgressbar(t,"success");var s=t.querySelector("."+C);sf.base.isNullOrUndefined(s)||(s.classList.add(F),s.setAttribute("title",this.localizedTexts("delete")),s.setAttribute("aria-label",this.localizedTexts("delete")),s.classList.remove(C,"e-upload-progress"))}},e.prototype.chunkUploadFailed=function(e,t,s){var i,a=this,n=Math.max(Math.ceil(t.file.size/this.asyncSettings.chunkSize),1);!sf.base.isNullOrUndefined(this.template)||!sf.base.isNullOrUndefined(s)&&s||(i=this.getLiElement(t.file));var r=e&&e.currentTarget?this.getResponse(e):null,l={event:e,file:t.file,chunkIndex:t.chunkIndex,totalChunk:n,chunkSize:this.asyncSettings.chunkSize,cancel:!1,response:r};this.chunkFailuredEnabled?this.dotNetRef.invokeMethodAsync("ChunkFailureEvent",l).then((function(n){a.chunkFailureCallback(e,t,i,r,s,n)})):this.chunkFailureCallback(e,t,i,r,s,l)},e.prototype.chunkFailureCallback=function(e,t,s,i,a,n){var r=this;if(!n.cancel)if(t.retryCount<this.asyncSettings.retryCount)setTimeout((function(){r.retryRequest(s,t,a)}),this.asyncSettings.retryAfterDelay);else{if(!sf.base.isNullOrUndefined(s)){var l=s.querySelector("."+w)?s.querySelector("."+w):s.querySelector(".e-file-play-btn");sf.base.isNullOrUndefined(l)||(l.classList.add(U),l.classList.remove(w,"e-file-play-btn")),this.updateProgressBarClasses(s,A),this.removeProgressbar(s,"failure"),s.querySelector(".e-icons").classList.remove("e-upload-progress");var o=s.querySelector("."+C)?s.querySelector("."+C):s.querySelector("."+S);o.classList.remove(C),sf.base.isNullOrUndefined(s.querySelector("."+w))||sf.base.detach(s.querySelector("."+w)),t.start>0?(o.classList.add(F),o.setAttribute("title",this.localizedTexts("delete")),o.setAttribute("aria-label",this.localizedTexts("delete"))):(o.classList.add(S),o.setAttribute("title",this.localizedTexts("remove")),o.setAttribute("aria-label",this.localizedTexts("remove")))}t.retryCount=0;var u=t.file,d=this.localizedTexts("uploadFailedMessage"),c={e:e,response:i,operation:"upload",file:this.updateStatus(u,d,"0",!1),statusText:d,retryFiles:null};this.failuredEnabled?this.dotNetRef.invokeMethodAsync("FailureEvent",c).then((function(e){r.failureCallback(u,e)})):this.failureCallback(u,c)}},e.prototype.failureCallback=function(e,t){this.updateStatus(e,t.statusText,"0"),this.uploadSequential(),this.checkActionComplete(!0);var s=t.retryFiles;null!=s&&this.retry(s)},e.prototype.retryRequest=function(e,t,s){sf.base.isNullOrUndefined(this.template)&&(sf.base.isNullOrUndefined(s)||!s)&&e&&this.updateProgressBarClasses(e,A),t.retryCount+=1,this.sendRequest(t.file,t)},e.prototype.checkPausePlayAction=function(e){var t=e.target,s=e.target.parentElement,i=this.fileList.indexOf(s),a=this.filesData[i],n=this.getCurrentMetaData(a);t.classList.contains(w)?this.pauseUpload(n,e):t.classList.contains("e-file-play-btn")?this.resumeUpload(n,e):t.classList.contains(U)&&(n.file.status===this.localizedTexts("fileUploadCancel")?this.retryUpload(n,!1):this.retryUpload(n,!0))},e.prototype.retryUpload=function(e,t){t?(e.end=e.end+this.asyncSettings.chunkSize,e.start=e.start+this.asyncSettings.chunkSize,this.sendRequest(e.file,e)):(e.file.statusCode="1",e.file.status=this.localizedTexts("readyToUploadMessage"),this.chunkUpload(e.file)),this.getLiElement(e.file).classList.add("e-restrict-retry")},e.prototype.chunkUploadInProgress=function(e,t,s){var i=this;if("4"!==t.file.statusCode){"4"!==t.file.statusCode&&"5"!==t.file.statusCode&&(t.file.statusCode="3",t.file.status=this.localizedTexts("inProgress")),this.updateMetaData(t);var a=this.getLiElement(t.file);if(!sf.base.isNullOrUndefined(a)){var n=a.querySelector("."+U);if(sf.base.isNullOrUndefined(n)||(n.classList.add(w),n.setAttribute("title",this.localizedTexts("pause")),n.setAttribute("aria-label",this.localizedTexts("pause")),n.classList.remove(U)),!sf.base.isNullOrUndefined(a)){if(!(a.querySelectorAll("."+E).length>0)){var r=a.querySelector("."+y);sf.base.isNullOrUndefined(this.template)&&(r.classList.add("e-upload-progress"),r.classList.remove(A),this.createProgressBar(a),this.updateProgressBarClasses(a,"e-upload-progress"));var l=a.querySelector("."+S)?a.querySelector("."+S):a.querySelector("."+F);sf.base.isNullOrUndefined(l)||(l.classList.add(C),l.setAttribute("title",this.localizedTexts("abort")),l.setAttribute("aria-label",this.localizedTexts("abort")),l.classList.remove(S))}if(!isNaN(Math.round(e.loaded/e.total*100))&&sf.base.isNullOrUndefined(this.template)&&"4"!==t.file.statusCode){var o=void 0,u=Math.ceil(t.file.size/this.asyncSettings.chunkSize)-1;this.asyncSettings.chunkSize&&u&&(o=Math.round(t.chunkIndex/u*100),this.changeProgressValue(a,o.toString()+"%"))}0===t.chunkIndex&&this.checkActionButtonStatus()}if(sf.base.isNullOrUndefined(a.querySelector("."+w))&&sf.base.isNullOrUndefined(this.template)&&sf.base.isNullOrUndefined(a.querySelector("."+F))){this.pauseButton=sf.base.createElement("span",{className:"e-icons e-file-pause-btn",attrs:{tabindex:this.btnTabIndex}}),"msie"===this.browserName&&this.pauseButton.classList.add("e-msie");var d=a.querySelector("."+C);d.parentElement.insertBefore(this.pauseButton,d),this.pauseButton.setAttribute("title",this.localizedTexts("pause")),this.pauseButton.setAttribute("aria-label",this.localizedTexts("pause")),this.pauseButton.addEventListener("click",(function(e){i.checkPausePlayAction(e)}),!1)}}}},e.prototype.bytesToSize=function(e){var t=-1;if(!e)return"0.0 KB";do{e/=1024,t++}while(e>99);return t>=2&&(e*=1024,t=1),Math.max(e,0).toFixed(1)+" "+["KB","MB"][t]},e.prototype.sortFileList=function(e){for(var t=e=e||this.sortFilesList,s=[],i=0;i<t.length;i++)s.push(t[i].name);for(var a=[],n=0,r=s.sort();n<r.length;n++){var l=r[n];for(i=0;i<t.length;i++)l===t[i].name&&a.push(t[i])}return a},e.prototype.destroy=function(){this.element.value=null,this.isSaveUrlNotConfigured||this.isValidTemplate||this.clearAll(),this.unWireEvents(),this.unBindDropEvents(),this.element.removeAttribute("accept"),this.setInitialAttributes();for(var e=0,t=["aria-label","directory","webkitdirectory","tabindex"];e<t.length;e++){var s=t[e];this.element.removeAttribute(s)}},e.prototype.upload=function(e,t){var s=this;if(e=e||this.filesData,this.sequentialUpload&&(this.isFirstFileOnSelection||t)&&this.showFileList)this.sequenceUpload(e);else{var i=this.getFilesInArray(e),a={customFormData:[],currentRequest:null,cancel:!1,withCredentials:!1,filesData:i};this.beforeUploadEnabled?this.dotNetRef.invokeMethodAsync("BeforeUploadEvent",a).then((function(e){s.beforeUploadCallback(i,t,e)})):this.beforeUploadCallback(i,t,a)}},e.prototype.beforeUploadCallback=function(e,t,s){s.cancel||(this.currentRequestHeader=s.currentRequest?s.currentRequest:this.currentRequestHeader,this.customFormDatas=s.customFormData&&s.customFormData.length>0?s.customFormData:this.customFormDatas,this.withCredentials=s.withCredentials?s.withCredentials:this.withCredentials,this.uploadFiles(e,t))},e.prototype.getFilesInArray=function(e){var t=[];return e instanceof Array?t=this.getFileListData(e):t.push(e),t},e.prototype.getFileListData=function(e){var t=[];if(!sf.base.isNullOrUndefined(this.filesData)&&!sf.base.isNullOrUndefined(e))for(var s=function(s){t.push(i.filesData.filter((function(t){return t.id?t.id===e[s].id:t.name===e[s].name}))[0])},i=this,a=0;a<e.length;a++)s(a);return sf.base.isNullOrUndefined(t)?[]:t},e.prototype.uploadFiles=function(e,t){var s=this,i=[];if(""===this.asyncSettings.saveUrl||sf.base.isNullOrUndefined(this.asyncSettings.saveUrl))this.dotNetRef.invokeMethodAsync("GetFileDetails",e);else{if(!t||sf.base.isNullOrUndefined(t))if(this.multiple)i=this.filterfileList(e);else{var a=[];a.push(e[0]),i=this.filterfileList(a)}else i=e;for(var n=function(e){r.checkChunkUpload()?r.uploadFilesRequest(i,e,t):i[e]&&i[e].rawFile instanceof File&&r.getBase64(i[e].rawFile).then((function(a){s.base64String.push(a),s.uploadFilesRequest(i,e,t)}))},r=this,l=0;l<i.length;l++)n(l)}},e.prototype.uploadFilesRequest=function(e,t,s){var i,a=this,n=[],r=this.checkChunkUpload(),l=new sf.base.Ajax(this.asyncSettings.saveUrl,"POST",!0,null);l.emitError=!1,i=e.slice(0),n.push(i[t].rawFile);var o={cancel:!1,currentRequest:null,customFormData:[],fileData:i[t]},u=new FormData;if(l.beforeSend=function(s){o.currentRequest=l.httpRequest,o.currentRequest.withCredentials=a.withCredentials,o.fileData.rawFile=r?o.fileData.rawFile:a.base64String[t],a.currentRequestHeader&&a.updateCustomheader(l.httpRequest,a.currentRequestHeader),a.customFormDatas&&a.updateFormData(u,a.customFormDatas),a.uploadingEnabled?a.dotNetRef.invokeMethodAsync("UploadingEvent",o).then((function(i){o=i,a.uploadCallback(s,r,e,u,t,n,i)})):a.uploadCallback(s,r,e,u,t,n,o)},"1"===e[t].statusCode){var d=this.element.getAttribute("name");u.append(d,e[t].rawFile,e[t].name),r&&e[t].size>this.asyncSettings.chunkSize?this.chunkUpload(e[t],s,t):(l.onLoad=function(i){return o.cancel||a.uploadComplete(i,e[t],s),{}},l.onUploadProgress=function(i){return o.cancel||a.uploadInProgress(i,e[t],s,l),{}},l.onError=function(s){return a.uploadFailed(s,e[t]),{}},l.send(u))}},e.prototype.uploadCallback=function(e,t,s,i,a,n,r){t||(s[a].rawFile=r.fileData.rawFile=n[a]),r.cancel&&this.eventCancelByArgs(e,r,s[a]),this.updateFormData(i,r.customFormData)},e.prototype.spliceFiles=function(e){for(var t=this.fileList[e],s=this.getFilesData(),i=+t.getAttribute("data-files-count"),a=0,n=0;n<e;n++)a+=+this.fileList[n].getAttribute("data-files-count");for(var r=a+i-1;r>=a;r--)s.splice(r,1)},e.prototype.remove=function(e,t,s,i,a){var n=this;sf.base.isNullOrUndefined(i)&&(i=!0);var r={cancel:!1,customFormData:[],currentRequest:null,filesData:this.getFilesInArray(e),postRawFile:i};this.beforeRemoveEnabled?this.dotNetRef.invokeMethodAsync("BeforeRemoveEvent",r).then((function(i){n.beforeRemoveCallback(e,t,s,a,i)})):this.beforeRemoveCallback(e,t,s,a,r)},e.prototype.beforeRemoveCallback=function(e,t,s,i,a){var n=this,r={event:i,cancel:!1,filesData:[],customFormData:[],postRawFile:a.postRawFile,currentRequest:null};if(!a.cancel){this.currentRequestHeader=a.currentRequest,this.customFormDatas=a.customFormData;if(this.isFormUpload()&&!this.isSaveUrlNotConfigured)r.filesData=e,this.dotNetRef.invokeMethodAsync("RemovingEvent",r).then((function(t){n.removeCallback(e,t)}));else if(!this.isForm||!sf.base.isNullOrUndefined(this.asyncSettings.removeUrl)&&""!==this.asyncSettings.removeUrl||this.isSaveUrlNotConfigured){var l=[];(e=sf.base.isNullOrUndefined(e)?this.filesData:e)instanceof Array?l=e:l.push(e),r.filesData=l;for(var o=this.asyncSettings.removeUrl,u=""!==o&&!sf.base.isNullOrUndefined(o),d=function(e){c.filesData.indexOf(e);var a=c.uploadedFilesData.indexOf(e);("2"===e.statusCode||"4"===e.statusCode||"0"===e.statusCode&&-1!==a)&&u?c.removeUploadedFile(e,r,s,t):!s&&c.removingEnabled?c.dotNetRef.invokeMethodAsync("RemovingEvent",r).then((function(s){s.cancel||n.removeFilesData(e,t)})):c.removeFilesData(e,t),i&&!i.target.classList.contains(S)&&c.checkActionComplete(!1)},c=this,h=0,f=l;h<f.length;h++){d(f[h])}}else r.filesData=this.getFilesData(),this.removingEnabled?this.dotNetRef.invokeMethodAsync("RemovingEvent",r).then((function(e){e.cancel||n.clearAll()})):this.clearAll()}},e.prototype.removeCallback=function(e,t){if(!t.cancel)for(var s=!1,i=void 0,a=0,n=this.getFilesInArray(e);a<n.length;a++){var r=n[a];if(s||(i=this.fileList.indexOf(r.list)),i>-1){var l=sf.base.isNullOrUndefined(r.input)?null:r.input;l&&sf.base.detach(l),this.spliceFiles(i),sf.base.detach(this.fileList[i]),this.fileList.splice(i,1),s=!0,i=-1}}},e.prototype.clearAll=function(){var e=this;if(sf.base.isNullOrUndefined(this.listParent)&&!this.isSaveUrlNotConfigured&&!this.isValidTemplate)return"msie"!==this.browserName&&(this.element.value=""),void(this.filesData=[]);var t={cancel:!1,filesData:this.filesData};this.clearEnabled?this.dotNetRef.invokeMethodAsync("ClearingEvent",t).then((function(t){t.cancel||(e.clearData(),e.actionCompleteCount=0,e.count=-1)})):(this.clearData(),this.actionCompleteCount=0,this.count=-1)},e.prototype.getFilesData=function(e){return sf.base.isNullOrUndefined(e)?this.filesData:this.getSelectedFiles(e)},e.prototype.pause=function(e,t){e=e||this.filesData;var s=this.getFilesInArray(e);this.pauseUploading(s,t)},e.prototype.pauseUploading=function(e,t){for(var s=this.getFiles(e),i=0;i<s.length;i++)"3"===s[i].statusCode&&this.pauseUpload(this.getCurrentMetaData(s[i],null),null,t)},e.prototype.getFiles=function(e){var t=[];return sf.base.isNullOrUndefined(e)||e instanceof Array?t=e:t.push(e),t},e.prototype.resume=function(e,t){e=e||this.filesData;var s=this.getFilesInArray(e);this.resumeFiles(s,t)},e.prototype.resumeFiles=function(e,t){for(var s=this.getFiles(e),i=0;i<s.length;i++)"4"===s[i].statusCode&&this.resumeUpload(this.getCurrentMetaData(s[i],null),null,t)},e.prototype.retry=function(e,t,s){e=e||this.filesData;var i=this.getFilesInArray(e);this.retryFailedFiles(i,t,s)},e.prototype.retryFailedFiles=function(e,t,s){for(var i=this.getFiles(e),a=0;a<i.length;a++)if("5"===i[a].statusCode||"0"===i[a].statusCode)if(this.asyncSettings.chunkSize>0)this.retryUpload(this.getCurrentMetaData(i[a],null),t);else{var n=void 0;s||(n=this.fileList[this.filesData.indexOf(i[a])]),this.reloadcanceledFile(null,i[a],n,s)}},e.prototype.cancel=function(e){e=e||this.filesData;var t=this.getFilesInArray(e);this.cancelUpload(t)},e.prototype.cancelUpload=function(e){var t=this.getFiles(e);if(this.asyncSettings.chunkSize>0){for(var s=0;s<t.length;s++)if("3"===t[s].statusCode){var i=this.getCurrentMetaData(t[s],null);i.file.statusCode="5",i.file.status=this.localizedTexts("fileUploadCancel"),this.updateMetaData(i),this.showHideUploadSpinner(t[s])}}else for(s=0;s<t.length;s++)"3"===t[s].statusCode&&(t[s].statusCode="5",t[s].status=this.localizedTexts("fileUploadCancel"),this.showHideUploadSpinner(t[s]))},e.prototype.showHideUploadSpinner=function(e){var s=this.getLiElement(e);if(!sf.base.isNullOrUndefined(s)&&sf.base.isNullOrUndefined(this.template)){var i=s.querySelector("."+C);t({target:i,width:"20px"}),f(i)}},e}();return{initialize:function(e,t,s,i){t&&e&&new x(e,t,s,i).initialize()},raiseSuccessEvent:function(e,t){var s=window.sfBlazor.getCompInstance(e);sf.base.isNullOrUndefined(s)||s.raiseSuccessEvent(null,t)},serverRemoveIconBindEvent:function(e){var t=window.sfBlazor.getCompInstance(e);sf.base.isNullOrUndefined(t)||t.serverRemoveIconBindEvent()},serverFileListElement:function(e,t,s,i){var a=window.sfBlazor.getCompInstance(e);sf.base.isNullOrUndefined(a)||(a.serverUlElement(t),i||a.serverActionButtonsEventBind(s))},bytesToSize:function(e,t){var s=window.sfBlazor.getCompInstance(e);return sf.base.isNullOrUndefined(s)?"":s.bytesToSize(t)},cancel:function(e,t){var s=window.sfBlazor.getCompInstance(e);sf.base.isNullOrUndefined(s)||s.cancel(t)},clearAll:function(e){var t=window.sfBlazor.getCompInstance(e);sf.base.isNullOrUndefined(t)||t.clearAll()},createFileList:function(e,t,s){var i=window.sfBlazor.getCompInstance(e);sf.base.isNullOrUndefined(i)||i.createFileList(t,s)},getFilesData:function(e,t){var s=window.sfBlazor.getCompInstance(e);return sf.base.isNullOrUndefined(s)?null:s.getFilesData(t)},pause:function(e,t,s){var i=window.sfBlazor.getCompInstance(e);sf.base.isNullOrUndefined(i)||i.pause(t,s)},remove:function(e,t,s,i,a,n){for(var r=window.sfBlazor.getCompInstance(e),l=0;l<r.filesData.length;l++)t&&r.filesData[l].id===t[0].id&&(t[0]=r.filesData[l]);sf.base.isNullOrUndefined(r)||r.remove(t,s,i,a,n)},resume:function(e,t,s){var i=window.sfBlazor.getCompInstance(e);sf.base.isNullOrUndefined(i)||i.resume(t,s)},retry:function(e,t,s,i){var a=window.sfBlazor.getCompInstance(e);sf.base.isNullOrUndefined(a)||a.retry(t,s,i)},sortFileList:function(e,t){var s=window.sfBlazor.getCompInstance(e);return sf.base.isNullOrUndefined(s)?null:s.sortFileList(t)},upload:function(e,t,s){var i=window.sfBlazor.getCompInstance(e);sf.base.isNullOrUndefined(i)||i.upload(t,s)},propertyChanges:function(e,t,s){var i=window.sfBlazor.getCompInstance(e);sf.base.isNullOrUndefined(i)||i.propertyChanges(t,s)}}}()}}]);(async()=>{await import(`${document.baseURI}_content/Syncfusion.Blazor/scripts/syncfusion-blazor-base.min.js?v=26.1.undefined`).then(()=>{sfBlazor.loadDependencies('sfuploader');})})();