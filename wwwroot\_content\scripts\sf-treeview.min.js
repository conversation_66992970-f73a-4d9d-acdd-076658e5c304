/*!*  filename: sf-treeview.min.js 
*     version : 26.1.41 
*     Copyright Syncfusion Inc. 2001 - 2024. All rights reserved. 
*     Use of this code is subject to the terms of our license.
*     A copy of the current license can be obtained at any time by e-mailing 
*     <EMAIL>. Any infringement will be prosecuted under 
*     applicable laws. 
*/
(window.webpackJsonp=window.webpackJsonp||[]).push([[72],{"./bundles/sf-treeview.js":function(e,t,s){"use strict";s.r(t);s("./modules/sf-treeview.js")},"./modules/sf-treeview.js":function(e,t){function s(e){return(s="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}window.sfBlazor=window.sfBlazor||{},window.sfBlazor.TreeView=function(){"use strict";var e="e-list-item",t="e-list-parent",i="e-hover",n="e-icon-collapsible",o="e-icon-expandable",a="e-icons",l="e-check",r="block",d="e-active",c="e-treeview",f="e-node-focus",p="e-checkbox-wrapper",u="e-text-content",h="e-sibling",g="e-drop-in",v="e-drop-next",b="e-no-drop",m="e-disable",y=function(){function y(e,t,s,i){this.isHelperElement=!0,this.mouseDownStatus=!1,this.mouseUpStatus=!1,this.preventExpand=!1,this.keyBoardAction=!1,this.isNodeRendered=!1,this.isEdited=!1,this.startTime=0,this.endTime=0,this.isKeyUp=!1,this.timer=0,this.isParentMouseDown=!1,this.isAnimationCompleted=!0,this.prevScrollValue=0,this.element=t,this.dataId=e,this.dotNetRef=i,this.options=s,window.sfBlazor.setCompInstance(this)}return y.prototype.render=function(){this.dragStartAction=!1,this.listBaseOption={expandCollapse:!0,showIcon:!0,expandIconClass:o,expandIconPosition:"Left"},this.keyConfigs={escape:"escape",end:"end",enter:"enter",f2:"f2",home:"home",moveDown:"downarrow",moveLeft:"leftarrow",moveRight:"rightarrow",moveUp:"uparrow",ctrlDown:"ctrl+downarrow",ctrlUp:"ctrl+uparrow",ctrlEnter:"ctrl+enter",ctrlHome:"ctrl+home",ctrlEnd:"ctrl+end",ctrlA:"ctrl+A",shiftDown:"shift+downarrow",shiftUp:"shift+uparrow",shiftEnter:"shift+enter",shiftHome:"shift+home",shiftEnd:"shift+end",csDown:"ctrl+shift+downarrow",csUp:"ctrl+shift+uparrow",csEnter:"ctrl+shift+enter",csHome:"ctrl+shift+home",csEnd:"ctrl+shift+end",space:"space",shiftSpace:"shift+space",ctrlSpace:"ctrl+space"},this.animationObj=new sf.base.Animation({});var e=this.element.querySelectorAll("li");e.length>0&&!sf.base.Browser.isDevice&&e[0].setAttribute("tabindex","0"),this.setDisabledMode(this.options.disabled),this.setMultiSelect(this.options.allowMultiSelection),this.options.hasTemplate&&this.element.classList.add("e-interaction")},y.prototype.setDisabledMode=function(e){this.setDragAndDrop(this.options.allowDragAndDrop),this.wireEditingEvents(this.options.allowEditing),this.checkAllDisabled(e),e?this.unWireEvents():this.wireEvents()},y.prototype.checkAllDisabled=function(e){e?this.element.classList.add("e-disabled"):this.element.classList.remove("e-disabled")},y.prototype.updateWrap=function(e){var t=this;this.options.fullRowSelect&&sf.base.selectAll("li",e||this.element).forEach((function(e){var s=sf.base.select(".e-fullrow",e);s&&s.nextElementSibling&&(s.style.height=t.options.allowTextWrap?s.nextElementSibling.offsetHeight+"px":"")}))},y.prototype.setTextWrap=function(){this.options.allowTextWrap&&!this.element.classList.contains("e-text-wrap")?sf.base.addClass([this.element],"e-text-wrap"):!this.options.allowTextWrap&&this.element.classList.contains("e-text-wrap")&&sf.base.removeClass([this.element],"e-text-wrap"),this.updateWrap()},y.prototype.mouseDownHandler=function(e){this.mouseDownStatus=!0,(e.shiftKey||e.ctrlKey)&&e.preventDefault(),e.ctrlKey&&this.options.allowMultiSelection&&sf.base.EventHandler.add(this.element,"contextmenu",this.preventContextMenu,this)},y.prototype.mouseupHandler=function(){this.mouseUpStatus=!0},y.prototype.onMouseLeave=function(e){this.removeHover()},y.prototype.unWireEvents=function(){if(this.wireExpandOnEvent(!1),this.options.allowTextWrap){var e=this.getParentElement(this.element);"BODY"!=e.nodeName&&sf.base.EventHandler.remove(e,"mousedown mouseup mousemove",this.resizeHandler)}sf.base.EventHandler.remove(this.element,"mousedown",this.mouseDownHandler),sf.base.EventHandler.remove(this.element,"click",this.clickHandler),sf.base.EventHandler.remove(this.element,"focusin",this.focusIn),sf.base.EventHandler.remove(this.element,"focusout",this.focusOut),sf.base.EventHandler.remove(this.element,"mouseover",this.onMouseOver),sf.base.EventHandler.remove(this.element,"mouseout",this.onMouseLeave),sf.base.EventHandler.remove(this.element,"contextmenu",this.contextLongPress),sf.base.Browser.isDevice&&this.options.allowMultiSelection&&(sf.base.EventHandler.remove(this.element,"touchstart",this.touchStart),sf.base.EventHandler.remove(this.element,"touchend",this.touchEnd)),!this.options.disabled&&this.keyboardModule&&this.keyboardModule.destroy(),this.element.classList.contains("e-virtualization")&&sf.base.EventHandler.remove(this.element,"scroll wheel",this.virtualScrollHandler)},y.prototype.keyboardActionHandler=function(e){this.isKeyUp=!0,this.keyAction=e;var t=e.target,s=this.getFocusedNode();if(!t||!t.classList.contains("e-input")&&"INPUT"!=t.nodeName&&"TEXTAREA"!=t.nodeName){e.preventDefault();var i={cancel:!1,event:e},n=s.getAttribute("data-uid");this.dotNetRef.invokeMethodAsync("TriggerKeyboardEvent",i,n,e.action,e.key)}else{var o=t;"enter"===e.action?(o.blur(),s.focus(),sf.base.addClass([s],f)):"escape"===e.action&&(o.value=this.oldText,o.blur(),s.focus(),sf.base.addClass([s],f))}},y.prototype.setMultiSelect=function(e){this.options.allowMultiSelection=e,e?this.element.setAttribute("aria-multiselectable","true"):this.element.setAttribute("aria-multiselectable","false")},y.prototype.setCssClass=function(e){this.options.cssClass&&sf.base.removeClass([this.element],this.options.cssClass.split(" ")),e&&sf.base.addClass([this.element],e.split(" ")),this.options.cssClass=e},y.prototype.wireEditingEvents=function(e){var t=this;if(e&&!this.options.disabled){var s=this;this.touchEditObj=new sf.base.Touch(this.element,{tap:function(e){t.isDoubleTapped(e)&&2===e.tapCount&&(e.originalEvent.preventDefault(),s.editingHandler(e.originalEvent))}})}else this.touchEditObj&&this.touchEditObj.destroy()},y.prototype.setDragAndDrop=function(e){e&&!this.options.disabled?this.initializeDrag():this.destroyDrag()},y.prototype.setDragArea=function(e){this.options.allowDragAndDrop&&(this.dragObj.dragArea=e)},y.prototype.destroyDrag=function(){this.dragObj&&this.dropObj&&(this.dragObj.destroy(),this.dropObj.destroy())},y.prototype.scrollUp=function(e){var t=sf.base.select(".e-fullrow",this.element);e.scrollBy(0,-t.offsetHeight/2)},y.prototype.scrollDown=function(e){var t=sf.base.select(".e-fullrow",this.element);e.scrollBy(0,t.offsetHeight/2)},y.prototype.clearTimer=function(){window.clearInterval(this.timer),this.virtualEle&&(this.virtualEle.style.position="absolute")},y.prototype.initializeDrag=function(){var s,n=this;this.dragObj=new sf.base.Draggable(this.element,{enableTailMode:!0,dragArea:this.options.dropArea,distance:5,dragTarget:"."+u,helper:function(i){n.dragTarget=i.sender.target;var l=sf.base.closest(n.dragTarget,"."+c),r=sf.base.closest(n.dragTarget,"."+u);if(n.dragLi=sf.base.closest(n.dragTarget,"."+e),n.options.fullRowSelect&&!r&&n.dragTarget.classList.contains("e-fullrow")&&(r=n.dragTarget.nextElementSibling),!n.dragTarget||!i.element.isSameNode(l)||!r||n.dragTarget.classList.contains(c)||n.dragTarget.classList.contains(t)||n.dragTarget.classList.contains(e)||n.dragLi.classList.contains(m))return!1;var f=r.cloneNode(!0);if(sf.base.isNullOrUndefined(sf.base.select("div."+a,f))){var p=sf.base.createElement("div",{className:a+" "+o});f.insertBefore(p,f.children[0])}var h="e-drag-item "+c+" "+n.options.cssClass+" "+(n.options.enableRtl?"e-rtl":"");(s=sf.base.createElement("div",{className:h})).appendChild(f);var g=n.element.querySelectorAll("."+d).length;if(g>1&&n.options.allowMultiSelection&&n.dragLi.classList.contains(d)){var v=sf.base.createElement("span",{className:"e-drop-count",innerHTML:""+g});s.appendChild(v)}return document.body.appendChild(s),document.body.style.cursor="",n.dragData=n.getNodeData(n.dragLi),n.virtualEle=s,s},drag:function(e){if(n.clearTimer(),n.mouseUpStatus)return sf.base.detach(s),sf.base.removeClass([n.element],"e-dragging"),n.removeVirtualEle(),void(document.body.style.cursor="");n.dragObj.setProperties({cursorAt:{top:!sf.base.isNullOrUndefined(e.event.targetTouches)||sf.base.Browser.isDevice?60:-20}}),n.dragAction(e,s);var t,i=n.getScrollParent(e.target);if(!sf.base.isNullOrUndefined(i)){if(t=i.getBoundingClientRect(),e.event.y<=t.top+30||t.top<0&&e.event.y<=30){n.virtualEle.style.position="fixed";var o=n;n.timer=window.setInterval((function(){o.scrollUp(i)}),200)}if(e.event.y>=(t.top<0?i.clientHeight-60:t.top+i.clientHeight-60)){n.virtualEle.style.position="fixed";var a=n;n.timer=window.setInterval((function(){a.scrollDown(i)}),200)}}},dragStart:function(t){if(!sf.base.isNullOrUndefined(t.target)){sf.base.EventHandler.add(document,"scroll",n.scrollHandler,n),sf.base.addClass([n.element],"e-dragging");var i,o=sf.base.closest(t.target,"."+e);o&&(i=parseInt(o.getAttribute("aria-level"),10),sf.base.EventHandler.add(o,"mouseup",n.mouseupHandler,n)),n.element.classList.contains("e-virtualization")&&(n.dragParent=n.dragLi.parentElement);var a=n.getDragEvent(t.event,n,null,t.target,null,s,i);if(a.draggedNode.classList.contains("e-editing"))n.dragObj.intDestroy(t.event),n.dragCancelAction(s);else{n.dragStartEventArgs=t;var l=n.getXYValue(t.event,"X"),r=n.getXYValue(t.event,"Y");s.style.display="none",n.dotNetRef.invokeMethodAsync("TriggerDragStartEvent",n.updateObjectValues(a),l,r)}}},dragStop:function(t){if(sf.base.EventHandler.remove(document,"scroll",n.scrollHandler),!sf.base.isNullOrUndefined(t.target)){n.clearTimer(),sf.base.removeClass([n.element],"e-dragging");var s,o=sf.base.select("."+i,n.element);t.target.classList.contains(h)&&(t.target=sf.base.isNullOrUndefined(o)?sf.base.closest(t.target,"."+e):o),n.sibEle=sf.base.select("."+h),n.sibEle&&(s=n.sibEle.offsetTop),n.removeVirtualEle();var a=t.target;n.dropRoot=sf.base.closest(a,".e-droppable"),n.isHelperElement=!0,a&&n.dropRoot||(t.helper&&t.helper.parentNode&&sf.base.remove(t.helper),document.body.style.cursor="",n.isHelperElement=!1);var l,r=sf.base.closest(a,"."+e),d=r;d&&(n.isUp=d.offsetTop>s),sf.base.isNullOrUndefined(n.sibEle)&&(n.isUp=!0),r&&(l=parseInt(r.getAttribute("aria-level"),10),sf.base.EventHandler.remove(r,"mouseup",n.mouseupHandler),n.mouseUpStatus=!1);var c=a,f=n.getDragEvent(t.event,n,a,c,null,t.helper,l);n.dragStopEventArgs=t,f.preventTargetExpand=!1;var p=n.getXYValue(t.event,"X"),u=n.getXYValue(t.event,"Y");sf.base.isNullOrUndefined(f.dropIndicator)&&(f.dropIndicator=b,document.body.style.cursor="not-allowed");var g=n.isExternalDrop(f.draggedNode),v=n.dropRoot&&g?window.sfBlazor.getCompInstance(n.dropRoot.getAttribute("data-id")):null;n.dotNetRef.invokeMethodAsync("TriggerDragStopEvent",n.updateObjectValues(f),p,u,v?v.dotNetRef:null)}}}),this.dropObj=new sf.base.Droppable(this.element,{out:function(e){sf.base.isNullOrUndefined(e&&e.target)||e.target.classList.contains(h)||(document.body.style.cursor="not-allowed")},over:function(e){document.body.style.cursor=""}})},y.prototype.scrollHandler=function(){if(this.virtualEle){var e=parseFloat(window.getComputedStyle(this.virtualEle).getPropertyValue("top")),t=this.getScrollParent(this.element);if(!sf.base.isNullOrUndefined(e)&&!sf.base.isNullOrUndefined(t)&&e>t.clientHeight){var s=e-t.scrollTop;this.virtualEle.style.top=s+"px"}this.virtualEle.style.position="fixed"}},y.prototype.updateObjectValues=function(e){return e.clonedNode=null,e.draggedNode=null,e.draggedParentNode=null,e.dropTarget=null,e.droppedNode=null,e.target=null,e},y.prototype.dragNodeStop=function(e){this.dragParent=e.draggedParentNode,this.preventExpand=e.preventTargetExpand,(e.cancel||e.dropIndicator==b)&&(this.dragStopEventArgs.helper.parentNode&&sf.base.remove(this.dragStopEventArgs.helper),document.body.style.cursor="",this.isHelperElement=!1),this.dragStartAction=!1,this.isHelperElement&&this.dropAction(this.dragStopEventArgs)},y.prototype.dragStartActionContinue=function(e){e?(this.dragObj.intDestroy(this.dragStartEventArgs.event),this.dragCancelAction(this.virtualEle)):(this.virtualEle.style.display=r,this.dragStartAction=!0,this.dragStartEventArgs.bindEvents(sf.base.getElement(this.dragStartEventArgs.dragElement)))},y.prototype.getId=function(e){return sf.base.isNullOrUndefined(e)?null:"string"==typeof e?e:"object"===s(e)?sf.base.getElement(e).getAttribute("data-uid"):null},y.prototype.getOffsetValue=function(e,t){var s,i=e.target.classList;if("mozilla"!==sf.base.Browser.info.name||sf.base.isNullOrUndefined(i))s="Y"===t?e.event.offsetY:e.event.offsetX;else{var n=e.target.getBoundingClientRect();s=Math.ceil("Y"===t?e.event.clientY-n.top:e.event.clientX-n.left)}return s},y.prototype.dropAction=function(t){var s,i,n=this.getOffsetValue(t,"Y"),o=t.target,a=!1;if((s=window.sfBlazor.getCompInstance(this.dataId))&&s.dragTarget){var l=s.dragTarget,r=sf.base.closest(l,"."+e),f=sf.base.closest(o,"."+e);if(null==f&&o.classList.contains(c)&&(f=o.firstElementChild),sf.base.remove(t.helper),o&&!o.closest("."+c+".e-droppable"))return;if(document.body.style.cursor="",!f||f.isSameNode(r)||this.isDescendant(r,f))return;if(s.options.allowMultiSelection&&r.classList.contains(d)){var p=sf.base.selectAll("."+d,s.element);if(t.target.offsetHeight<=33&&n>t.target.offsetHeight-10&&n>6)for(var u=p.length-1;u>=0;u--)f.isSameNode(p[u])||this.isDescendant(p[u],f)||this.appendNode(o,p[u],f,t,s,n);else for(u=0;u<p.length;u++)f.isSameNode(p[u])||this.isDescendant(p[u],f)||this.appendNode(o,p[u],f,t,s,n)}else this.appendNode(o,r,f,t,s,n);i=parseInt(r.getAttribute("aria-level"),10),a=!0}var h=t.element,g=this.getDragEvent(t.event,s,o,t.target,h,null,i,a),v=this.getXYValue(t.event,"X"),b=this.getXYValue(t.event,"Y");this.dotNetRef.invokeMethodAsync("TriggerNodeDropped",this.updateObjectValues(g),v,b)},y.prototype.isDoubleTapped=function(t){var s,i=t.originalEvent.target;return i&&t.tapCount&&(1===t.tapCount?this.firstTap=sf.base.closest(i,"."+e):2===t.tapCount&&(s=sf.base.closest(i,"."+e))),this.firstTap===s},y.prototype.isDescendant=function(e,t){for(var s=t.parentNode;!sf.base.isNullOrUndefined(s);){if(s===e)return!0;s=s.parentNode}return!1},y.prototype.appendNode=function(e,t,s,i,a,l){var r=sf.base.closest(e,"."+p),d=sf.base.closest(i.target,"."+n),f=sf.base.closest(i.target,"."+o),u=this.getOffsetValue(i,"X");t.classList.contains(m)||r||!(f&&l<5||d&&u<3||f&&l>19||d&&u>19||!f&&!d)?this.dropAsChildNode(t,s,a,null,i,l,!0):"LI"===e.nodeName?this.dropAsSiblingNode(t,s,i,a):e.firstElementChild&&e.classList.contains(c)?"UL"===e.firstElementChild.nodeName&&this.dropAsSiblingNode(t,s,i,a):e.classList.contains(n)||e.classList.contains(o)?this.dropAsSiblingNode(t,s,i,a):this.dropAsChildNode(t,s,a,null,i,l)},y.prototype.dropAsSiblingNode=function(s,i,a,l){var r,d=sf.base.closest(i,"."+t),c=sf.base.closest(s,"."+t),f=sf.base.closest(c,"."+e),p=sf.base.closest(d,"."+e),u=null,g=null,v=this.getOffsetValue(a,"X"),b=this.getOffsetValue(a,"Y");a.target.offsetHeight>0&&b>a.target.offsetHeight-2?r=!1:b<2?r=!0:(a.target.classList.contains(o)||a.target.classList.contains(n))&&(b<5||v<3?r=!0:(b>15||v>17)&&(r=!1)),a.event.target.classList.contains(h)&&this.isUp&&(r=!0),p&&(u=p.getAttribute("data-uid")),f&&(g=f.getAttribute("data-uid"));var m=this.isExternalDrop(s),y=this.dropRoot?window.sfBlazor.getCompInstance(this.dataId):null,E=m?y:this,N=this.getDropArgs(s,i,g,l,u,r);E.dotNetRef.invokeMethodAsync("DropNodeAsSibling",N),this.updateAriaLevel(s)},y.prototype.updateAriaLevel=function(e){var s=this.parents(e,"."+t).length;e.setAttribute("aria-level",""+s),this.updateChildAriaLevel(sf.base.select("."+t,e),s+1)},y.prototype.updateChildAriaLevel=function(e,s){if(!sf.base.isNullOrUndefined(e))for(var i=0,n=e.querySelectorAll("li");i<n.length;i++){var o=n[i];o.setAttribute("aria-level",String(s)),this.updateChildAriaLevel(sf.base.select("."+t,o),s+1)}},y.prototype.dropAsChildNode=function(s,i,n,o,a,l,r){var d=sf.base.closest(s,"."+t),f=d?sf.base.closest(d,"."+e):null,p=sf.base.closest(i,"."+t),u=sf.base.closest(p,"."+e),h=null,g=null;u&&(h=u.getAttribute("data-uid")),f&&(g=f.getAttribute("data-uid"));var v,b=this.isExternalDrop(s),m=sf.base.closest(s,"."+c)||this.element?window.sfBlazor.getCompInstance(this.dataId):null,y=this.dropRoot?window.sfBlazor.getCompInstance(this.dropRoot.getAttribute("data-id")):null,E=b?y:this;a&&l<7&&!r?(v=this.getDropArgs(s,i,g,m,h,!0),E.dotNetRef.invokeMethodAsync("DropNodeAsSibling",v)):a&&a.target.offsetHeight>0&&l>a.target.offsetHeight-10&&!r?(v=this.getDropArgs(s,i,g,m,h,!1),E.dotNetRef.invokeMethodAsync("DropNodeAsSibling",v)):(v=this.getDropArgs(s,i,g,m),E.dotNetRef.invokeMethodAsync("DropNodeAsChild",v)),this.updateAriaLevel(s)},y.prototype.isExternalDrop=function(e){var t=!1,s=sf.base.closest(e,"."+c)?sf.base.closest(e,"."+c):this.element,i=this.dropRoot;return null==s||null==i||s.isSameNode(i)||(t=!0),t},y.prototype.getDropArgs=function(e,t,s,i,n,o){return{dragLi:e.getAttribute("data-uid"),dropLi:t.getAttribute("data-uid"),dragParentLi:s,dropParentLi:n,pre:o,srcTree:i.dotNetRef,isExternalDrag:this.isExternalDrop(e)}},y.prototype.dragCancelAction=function(e){sf.base.detach(e),sf.base.removeClass([this.element],"e-dragging"),this.dragStartAction=!1},y.prototype.removeVirtualEle=function(){var e=sf.base.select("."+h);e&&sf.base.detach(e)},y.prototype.dragAction=function(t,s){if(!sf.base.isNullOrUndefined(t.target)){var i=sf.base.closest(t.target,".e-droppable"),l=sf.base.closest(t.target,"."+u),r=sf.base.select("div."+a,s),d=this.getOffsetValue(t,"X"),f=this.getOffsetValue(t,"Y");sf.base.removeClass([r],[g,v,"e-drop-out",b]),this.removeVirtualEle(),document.body.style.cursor="";var m=t.target.classList;if(this.options.fullRowSelect&&!l&&!sf.base.isNullOrUndefined(m)&&m.contains("e-fullrow")&&(l=t.target.nextElementSibling),i){var y=sf.base.closest(t.target,"."+e),E=sf.base.closest(t.target,"."+p),N=sf.base.closest(t.target,"."+n),w=sf.base.closest(t.target,"."+o);if(!i.classList.contains(c)||l&&!y.isSameNode(this.dragLi)&&!this.isDescendant(this.dragLi,y))if(y&&t&&!w&&!N&&f<7&&!E||w&&f<5||N&&d<3){sf.base.addClass([r],v);var A=sf.base.createElement("div",{className:h}),C=this.options.fullRowSelect?1:0;y.insertBefore(A,y.children[C])}else if(y&&t&&!w&&!N&&t.target.offsetHeight>0&&f>t.target.offsetHeight-10&&!E||w&&f>19||N&&d>19){sf.base.addClass([r],v);A=sf.base.createElement("div",{className:h}),C=this.options.fullRowSelect?2:1;y.insertBefore(A,y.children[C])}else sf.base.addClass([r],g);else"LI"!==t.target.nodeName||y.isSameNode(this.dragLi)||this.isDescendant(this.dragLi,y)?t.target.classList.contains(h)?sf.base.addClass([r],v):t.target.classList.contains("e-droppable")?sf.base.addClass([r],g):sf.base.addClass([r],"e-drop-out"):(sf.base.addClass([r],v),this.renderVirtualEle(t))}else sf.base.addClass([r],b),document.body.style.cursor="not-allowed";var x,S=sf.base.closest(t.target,e);S&&(x=parseInt(S.getAttribute("aria-level"),10));var O=this.getDragEvent(t.event,this,t.target,t.target,null,s,x);O.dropIndicator&&sf.base.removeClass([r],O.dropIndicator),this.iconElement=r,this.draggingEventArgs=O;var L=this.getXYValue(t.event,"X"),T=this.getXYValue(t.event,"Y");this.dotNetRef.invokeMethodAsync("TriggerNodeDraggingEvent",this.updateObjectValues(O),L,T)}},y.prototype.nodeDragging=function(e){this.draggingEventArgs.dropIndicator&&sf.base.addClass([this.iconElement],this.draggingEventArgs.dropIndicator)},y.prototype.renderVirtualEle=function(e){var t,s=this.getOffsetValue(e,"Y");s>e.target.offsetHeight-2?t=!1:s<2&&(t=!0);var i=sf.base.createElement("div",{className:h}),n=this.options.fullRowSelect?t?1:2:t?0:1;this.dropTargetElement=e.target.children[n],e.target.insertBefore(i,e.target.children[n])},y.prototype.parents=function(e,t){for(var s=[],i=e.parentNode;!sf.base.isNullOrUndefined(i);)sf.base.matches(i,t)&&s.push(i),i=i.parentNode;return s},y.prototype.getDragEvent=function(t,s,i,n,o,l,r,d){var f=i?sf.base.closest(i,"."+e):null,p=f?this.getNodeData(f):null,u=s?s.dragLi:o,h=s?s.dragData:null,m=i?this.parents(i,"."+e):null,y=s.dragLi.parentElement,E=s.dragLi&&y?sf.base.closest(y,"."+e):null,N=null,w=null,A=[v,g,"e-drop-out",b],C=null,x=d?u:f,S=x?sf.base.closest(x,".e-list-parent"):null,O=0;if(E=s.dragLi&&null===E&&y?sf.base.closest(y,"."+c):E,E=d?this.dragParent:E,l)for(;O<4;){if(sf.base.select("."+a,l).classList.contains(A[O])){C=A[O];break}O++}if(S){for(var L=void 0,T=s.element.querySelectorAll("."+f.classList[1]),k=0;k<T.length&&(T[k]===f&&(w=k),T[k]===u&&(L=k),sf.base.isNullOrUndefined(L)||sf.base.isNullOrUndefined(w));k++);if(sf.base.isNullOrUndefined(this.sibEle)){if(L>w&&(w+=1),u.classList[1]!==f.classList[1]?w=0!==w?w===T.length-1?T.length:w+1:0:t.offsetY<=5&&0!=w&&(w-=1),C===g&&(w=0,f.classList.contains("e-has-child"))){var D=parseInt(f.getAttribute("aria-level"))+1,H=f.querySelectorAll('[aria-level="'+D+'"]');w=-1===Array.from(H).findIndex((function(e){return e==u}))?H.length:H.length-1}}else L>(w=u.classList[1]===f.classList[1]?this.isUp?w-1:w:w===T.length-1?this.isUp?w:T.length:this.isUp?w:w+1)&&(w+=1);null!=C&&C!=b||(w=null)}if(i&&(N=0===m.length?null:i.classList.contains(e)?m[0]:m[1]),f===u&&(N=f),i&&n.offsetHeight<=33&&t.offsetY<n.offsetHeight-10&&t.offsetY>6&&(N=f,!d)){r=++r;var U=N?sf.base.select(".e-list-parent",N):null;w=U?U.children.length:0}return{cancel:!1,clonedNode:l,event:t,draggedNode:u,draggedNodeData:h,droppedNode:f,droppedNodeData:p,dropIndex:w,dropLevel:r,draggedParentNode:E,dropTarget:N,dropIndicator:C,target:n}},y.prototype.editingHandler=function(s){var i=s.target;!i||i.classList.contains(c)||i.classList.contains(t)||i.classList.contains(e)||i.classList.contains(a)||i.classList.contains("e-input")||i.classList.contains("e-input-group")||this.createTextbox(sf.base.closest(i,"."+e),s)},y.prototype.createTextbox=function(e,t){this.editEventArgs=this.getEditEvent(e,null,null),sf.base.addClass([e],"e-editing"),this.isEdited=!0,this.dotNetRef.invokeMethodAsync("TriggerNodeEditingEvent",this.editEventArgs)},y.prototype.getEditEvent=function(e,t,s){return{newText:t,nodeData:this.getNodeData(e),oldText:this.oldText,innerHtml:s}},y.prototype.focusIn=function(){if("INPUT"==document.activeElement.nodeName||"TEXTAREA"==document.activeElement.nodeName){var e=document.activeElement;this.updateOldText(e.value)}this.mouseDownStatus||sf.base.addClass([this.getFocusedNode()],f),this.mouseDownStatus=!1},y.prototype.focusOut=function(){this.removeHover(),sf.base.removeClass([this.getFocusedNode()],f)},y.prototype.touchStart=function(e){this.startTime=e.timeStamp},y.prototype.touchEnd=function(t){if(this.endTime=t.timeStamp,this.endTime-this.startTime>500&&!this.dragStartAction){var s=t.target,i=sf.base.closest(s,"."+e);t.preventDefault(),this.toggleSelect(i,t,!0)}},y.prototype.getParentElement=function(e){var t=e.parentElement;return"none"!=window.getComputedStyle(t).resize||"BODY"!=t.nodeName&&(t=this.getParentElement(t)),t},y.prototype.resizeHandler=function(e){switch(e.type){case"mousedown":this.isParentMouseDown=!0;break;case"mouseup":this.isParentMouseDown=!1;break;case"mousemove":this.isParentMouseDown&&this.updateWrap()}},y.prototype.wireEvents=function(){if(!this.options.disabled){if(this.setExpandOnType(),this.options.allowTextWrap){var e=this.getParentElement(this.element);"BODY"!=e.nodeName&&sf.base.EventHandler.add(e,"mousedown mouseup mousemove",this.resizeHandler,this)}if(sf.base.EventHandler.add(this.element,"mousedown",this.mouseDownHandler,this),sf.base.EventHandler.add(this.element,"click",this.clickHandler,this),sf.base.EventHandler.add(this.element,"mouseover",this.onMouseOver,this),sf.base.EventHandler.add(this.element,"focusin",this.focusIn,this),sf.base.EventHandler.add(this.element,"focusout",this.focusOut,this),sf.base.EventHandler.add(this.element,"mouseout",this.onMouseLeave,this),sf.base.EventHandler.add(this.element,"contextmenu",this.contextLongPress,this),sf.base.Browser.isDevice&&this.options.allowMultiSelection&&(sf.base.EventHandler.add(this.element,"touchstart",this.touchStart,this),sf.base.EventHandler.add(this.element,"touchend",this.touchEnd,this)),this.options.showCheckBox){var t=sf.base.select(".e-frame",this.element);sf.base.isNullOrUndefined(t)||(sf.base.EventHandler.add(t,"mousedown",this.frameMouseHandler,this),sf.base.EventHandler.add(t,"mouseup",this.frameMouseHandler,this))}"None"!==this.options.expandOnType&&this.wireExpandOnEvent(!0),this.keyboardModule=new sf.base.KeyboardEvents(this.element,{keyAction:this.keyboardActionHandler.bind(this),keyConfigs:this.keyConfigs,eventName:"keydown"}),this.element.classList.contains("e-virtualization")&&sf.base.EventHandler.add(this.element,"scroll wheel",this.virtualScrollHandler,this)}},y.prototype.virtualScrollHandler=function(){var e=sf.base.select(".e-mask-parent",this.element),t=sf.base.select(".e-list-parent",this.element);if(Math.abs(this.prevScrollValue-this.element.scrollTop)>150){e.style.display="",t.style.visibility="hidden";var s=this.element.scrollTop;this.prevScrollValue>this.element.scrollTop&&(s-=300),e.style.transform="translate(0px,"+s+"px)",setTimeout((function(){e.style.display="none",e.style.transform="",t.style.visibility=""}),200)}this.prevScrollValue=this.element.scrollTop},y.prototype.frameMouseHandler=function(e){var t=sf.base.select(".e-ripple-container",e.target.parentElement);this.rippleMouseHandler(e,t)},y.prototype.rippleMouseHandler=function(e,t){if(t){var s=document.createEvent("MouseEvents");s.initEvent(e.type,!1,!0),t.dispatchEvent(s)}},y.prototype.setExpandOnType=function(){var e=this.options.expandOnType;this.options.expandOnType="DoubleClick"===e?sf.base.Browser.isDevice?"Click":"DoubleClick":e},y.prototype.expandHandler=function(s){var i=s.originalEvent.target;!i||i.classList.contains("e-input")||i.classList.contains(c)||i.classList.contains(t)||i.classList.contains(e)||i.classList.contains(a)||this.options.showCheckBox&&sf.base.closest(i,"."+p)||this.expandCollapseAction(sf.base.closest(i,"."+e),s)},y.prototype.expandCollapseAction=function(e,t){var s=sf.base.select("div."+a,e);s&&!s.classList.contains("e-process")&&(s.classList.contains(o)?this.expandAction(e,t):s.classList.contains(n)&&(this.tapEvent=t,this.expandArgs=this.getExpandEvent(e,t),this.dotNetRef.invokeMethodAsync("NodeCollapsingEventCallback",this.expandArgs,!1)))},y.prototype.animateHeight=function(e,t,s){var i=(s-t)*((e.duration-e.timeStamp)/e.duration)+t;e.element.parentElement.style.height=i+"px"},y.prototype.expandAction=function(e,s){if(this.expandArgs=this.getExpandEvent(e,s),this.options.allowTextWrap){var i=sf.base.select("."+t,e);this.isNodeRendered=!!i}e&&e.classList.contains("e-process")&&sf.base.removeClass([e],"e-process"),this.dotNetRef.invokeMethodAsync("TriggerNodeExpandingEvent",this.expandArgs)},y.prototype.collapseAction=function(s,i,l,r){var c=this;if(sf.base.isNullOrUndefined(i)&&(i=this.tapEvent),this.expandArgs=this.getExpandEvent(s,i),!r){var f=0,p=0,h=this,g=sf.base.select("."+t,s),v=s,b=sf.base.select("."+e+"."+d,s);if(g){var m=sf.base.select("div."+a,v);sf.base.removeClass([m],n),sf.base.addClass([m],o)}sf.base.isNullOrUndefined(s.getAttribute("aria-expanded"))||s.setAttribute("aria-expanded","false"),0===this.options.animation.collapse.duration?(g.style.display="none",h.dotNetRef.invokeMethodAsync("TriggerNodeCollapsingEvent",h.expandArgs),l&&h.triggerClickEvent(i.originalEvent,s)):this.animationObj.animate(g,{name:"None"===this.options.animation.collapse.effect&&"Enable"===sf.base.animationMode?"SlideUp":this.options.animation.collapse.effect,duration:this.options.animation.collapse.duration,timingFunction:this.options.animation.collapse.easing,begin:function(e){h.isAnimationCompleted=!1,c.element.classList.contains("e-virtualization")||(v.style.overflow="hidden"),!sf.base.isNullOrUndefined(b)&&b instanceof HTMLElement&&b.classList.add("e-animation-active"),f=sf.base.select("."+u,s).offsetHeight,p=v.offsetHeight},progress:function(e){h.animateHeight(e,f,p)},end:function(e){e.element.style.display="none",!sf.base.isNullOrUndefined(b)&&b instanceof HTMLElement&&b.classList.remove("e-animation-active"),h.dotNetRef.invokeMethodAsync("TriggerNodeCollapsingEvent",h.expandArgs),l&&h.triggerClickEvent(i.originalEvent,s),h.isAnimationCompleted=!0}})}},y.prototype.wireExpandOnEvent=function(e){var t=this;if(e){var s=this;this.touchExpandObj=new sf.base.Touch(this.element,{tap:function(e){("Click"===t.options.expandOnType||"DoubleClick"===t.options.expandOnType&&t.isDoubleTapped(e)&&2===e.tapCount)&&3!==e.originalEvent.which&&s.expandHandler(e)}})}else this.touchExpandObj&&this.touchExpandObj.destroy()},y.prototype.getNodeData=function(t,s){if(!sf.base.isNullOrUndefined(t)&&t.classList.contains(e)&&!sf.base.isNullOrUndefined(sf.base.closest(t,".e-control"))&&sf.base.closest(t,".e-control").classList.contains(c)){var i=t.getAttribute("data-uid"),n=sf.base.closest(t.parentNode,"."+e),o=n?n.getAttribute("data-uid"):null,a=t.classList.contains(d),l="true"===t.getAttribute("aria-expanded"),r=null===t.getAttribute("aria-expanded"),f=null;return this.options.showCheckBox&&(f=sf.base.select("."+p,t).getAttribute("aria-checked")),{id:i,text:null,parentID:o,selected:a,expanded:l,isChecked:f,hasChildren:r}}return{id:"",text:"",parentID:"",selected:!1,expanded:!1,isChecked:"",hasChildren:!1}},y.prototype.getExpandEvent=function(e,t){var s=this.getNodeData(e);return{isInteracted:!sf.base.isNullOrUndefined(t),nodeData:s,event:t,isLoaded:null!=e.querySelector("ul"),nodeLevel:parseInt(e.getAttribute("aria-level"),10)}},y.prototype.updateSpinnerClass=function(){var e=this.element.querySelector(".e-icons-spinner");e&&sf.base.removeClass([e],"e-icons-spinner")},y.prototype.expandedNode=function(s){var i=this,l=this.element.querySelector('[data-uid="'+s.nodeData.id+'"]');this.focussedElement=l;var c=sf.base.select("."+t,l);if(c){c.classList.remove("e-display-none");var f=sf.base.select("div."+a,l);this.expandArgs=this.getExpandEvent(l,s.event);var p=l,h=sf.base.select("."+e+"."+d,l),g=0,v=0,b=this;this.setHeight(p,c),0===this.options.animation.expand.duration?(sf.base.removeClass([f],o),sf.base.addClass([f],n),b.updateSpinnerClass(),b.dotNetRef.invokeMethodAsync("TriggerNodeExpandedEvent",b.expandArgs),c.style.display=r,p.style.display=r,p.style.overflow="",p.style.height="",sf.base.removeClass([f],"e-icons-spinner"),this.options.allowTextWrap&&(!this.isNodeRendered||this.isEdited?(this.isEdited=!1,this.updateWrap(c)):this.isNodeRendered&&this.updateWrap())):this.animationObj.animate(c,{name:"None"===this.options.animation.expand.effect&&"Enable"===sf.base.animationMode?"SlideDown":this.options.animation.expand.effect,duration:this.options.animation.expand.duration,timingFunction:this.options.animation.expand.easing,begin:function(e){b.isAnimationCompleted=!1,i.element.classList.contains("e-virtualization")||(p.style.overflow="hidden"),!sf.base.isNullOrUndefined(h)&&h instanceof HTMLElement&&h.classList.add("e-animation-active"),g=p.offsetHeight,v=sf.base.select("."+u,l).offsetHeight},progress:function(e){sf.base.removeClass([f],o),sf.base.addClass([f],n),e.element.style.display=r,b.animateHeight(e,g,v)},end:function(e){e.element.style.display=r,!sf.base.isNullOrUndefined(h)&&h instanceof HTMLElement&&h.classList.remove("e-animation-active"),b.updateSpinnerClass(),b.dotNetRef.invokeMethodAsync("TriggerNodeExpandedEvent",b.expandArgs),c.style.display=r,p.style.display=r,p.style.overflow="",p.style.height="",sf.base.removeClass([f],"e-icons-spinner"),i.options.allowTextWrap&&(!i.isNodeRendered||i.isEdited?(i.isEdited=!1,i.updateWrap(c)):i.isNodeRendered&&i.updateWrap()),b.isAnimationCompleted=!0}})}if(!c){this.expandArgs=this.getExpandEvent(l,s.event),this.updateSpinnerClass(),this.dotNetRef.invokeMethodAsync("TriggerNodeExpandedEvent",this.expandArgs)}this.setHover(this.getFocusedNode())},y.prototype.setHeight=function(e,t){t.style.display=r,t.style.visibility="hidden",e.style.height=e.offsetHeight+"px",t.style.display="none",t.style.visibility=""},y.prototype.collapsedNode=function(e){var t=this.element.querySelector('[data-uid="'+e.nodeData.id+'"]');this.focussedElement=t;var s=t.querySelector("ul");s&&(s.style.display="none",s.classList.add("e-display-none")),t.style.overflow="",t.style.height="",this.expandArgs=this.getExpandEvent(t,null);var i=sf.base.select("div."+a,t);sf.base.removeClass([i],n),sf.base.addClass([i],o),this.dotNetRef.invokeMethodAsync("TriggerNodeCollapsedEvent",this.expandArgs)},y.prototype.preventContextMenu=function(e){e.preventDefault()},y.prototype.contextLongPress=function(t){var s=t.target,i={event:t,node:null},n=sf.base.closest(s,"."+e);this.dotNetRef.invokeMethodAsync("TriggerNodeClickingEvent",i,n.getAttribute("data-uid"),this.getXYValue(i.event,"X"),this.getXYValue(i.event,"Y"))},y.prototype.clickHandler=function(s){this.tapEvent=s;var i=s.target,r=!1;if(sf.base.EventHandler.remove(this.element,"contextmenu",this.preventContextMenu),i){if("INPUT"==i.nodeName||"TEXTAREA"==i.nodeName){var d=i;this.updateOldText(d.value)}var c=i.classList,f=sf.base.closest(i,"."+e);if(f){if(3!==s.which){var u=sf.base.select(".e-ripple-element",f),h=sf.base.select("."+a,f);if(this.removeHover(),this.focussedElement=f,this.setFocusElement(f),this.options.showCheckBox&&!f.classList.contains(m)){var g=sf.base.closest(i,"."+p);if(!sf.base.isNullOrUndefined(g)){var v=sf.base.select(".e-frame",g);return this.validateCheckNode(g,v.classList.contains(l),f,s),void this.triggerClickEvent(s,f)}}c.contains(o)?this.expandAction(f,s):c.contains(n)?(this.expandArgs=this.getExpandEvent(f,s),this.dotNetRef.invokeMethodAsync("NodeCollapsingEventCallback",this.expandArgs,!0),r=!0):u&&h?h.classList.contains("e-ripple")&&h.classList.contains(o)?this.expandAction(f,s):h.classList.contains("e-ripple")&&h.classList.contains(n)?(this.collapseAction(f,s,!0),r=!0):c.contains(t)||c.contains(e)||this.toggleSelect(f,s,!1):c.contains(t)||c.contains(e)||this.toggleSelect(f,s,!1)}r||this.triggerClickEvent(s,f)}}},y.prototype.getXYValue=function(e,t){var s,i=e.changedTouches;if(!(s="X"===t?i?i[0].clientX:e.clientX:i?i[0].clientY:e.clientY)&&"focus"===e.type&&e.target){var n=e.target.getBoundingClientRect();s=n?"X"===t?n.left:n.top:null}return Math.ceil(s)},y.prototype.triggerClickEvent=function(e,t){var s={event:e,node:null};this.dotNetRef.invokeMethodAsync("TriggerNodeClickingEvent",s,t.getAttribute("data-uid"),this.getXYValue(e,"X"),this.getXYValue(e,"Y"))},y.prototype.getCheckEvent=function(e,t,s){return{action:t,isInteracted:!sf.base.isNullOrUndefined(s),nodeData:this.getNodeData(e)}},y.prototype.validateCheckNode=function(t,s,i,n){var o=sf.base.closest(t,"."+e),a=s?"false":"true";sf.base.isNullOrUndefined(a)||t.setAttribute("aria-checked",a);var l=this.getCheckEvent(o,s?"uncheck":"check",n);this.dotNetRef.invokeMethodAsync("TriggerNodeCheckingEvent",l)},y.prototype.toggleSelect=function(e,t,s){e.classList.contains(m)||(this.options.allowMultiSelection&&(t&&t.ctrlKey||s)&&e.classList.contains(d)?this.unselectNode(e,t,s):(this.selectNode(e,t,s),this.options.allowMultiSelection&&t&&(t.ctrlKey||t.shiftKey)&&(this.setFocusElement(e),this.focussedElement=e)))},y.prototype.unselectNode=function(e,t,s){var i=this.getSelectEvent(e,"un-select",t,s,[]);this.dotNetRef.invokeMethodAsync("TriggerNodeSelectingEvent",i)},y.prototype.getSelectEvent=function(e,t,s,i,n){var o=this.getNodeData(e);return{action:t,isInteracted:!sf.base.isNullOrUndefined(s),nodeData:o,isMultiSelect:i,isCtrKey:!(sf.base.isNullOrUndefined(s)||!s.ctrlKey),isShiftKey:!(sf.base.isNullOrUndefined(s)||!s.shiftKey),nodes:n}},y.prototype.selectNode=function(t,s,i){var n;if(sf.base.isNullOrUndefined(t)||!this.options.allowMultiSelection&&t.classList.contains(d)&&!sf.base.isNullOrUndefined(s))return this.setFocusElement(t),void(this.focussedElement=t);var o=[];if(this.options.allowMultiSelection&&s&&s.shiftKey){var a=sf.base.selectAll("."+e+"."+d,this.element),l=a?a.length:0;this.startNode=l>0?a[l-1]:t;var r=Array.prototype.slice.call(sf.base.selectAll("."+e,this.element)),c=r.indexOf(this.startNode),f=r.indexOf(t);c>f&&(c=(n=[f,c])[0],f=n[1]);for(var p=c;p<=f;p++){var u=r[p];sf.base.isVisible(u)&&!u.classList.contains(m)&&o.push(u.getAttribute("data-uid"))}}else this.startNode=t;var h=this.getSelectEvent(t,"select",s,i,o);this.dotNetRef.invokeMethodAsync("TriggerNodeSelectingEvent",h)},y.prototype.setFocusElement=function(e){if(!sf.base.isNullOrUndefined(e)){var t=this.getFocusedNode();t&&(sf.base.removeClass([t],f),sf.base.Browser.isDevice||t.setAttribute("tabindex","-1")),sf.base.addClass([e],f),sf.base.Browser.isDevice||e.setAttribute("tabindex","0"),this.focussedElement=e,this.updateIdAttr(t,e)}},y.prototype.updateIdAttr=function(e,t){this.element.removeAttribute("aria-activedescendant");var s=this.element.querySelectorAll("[id=_active]");s[0]!=e&&s.forEach((function(e){e.removeAttribute("id")})),e&&e.removeAttribute("id"),t.setAttribute("id",this.element.id+"_active"),this.element.setAttribute("aria-activedescendant",this.element.id+"_active")},y.prototype.getFocusedNode=function(){var t,s;return s=sf.base.select("."+e+'[tabindex="0"]',this.element),!this.isKeyUp&&sf.base.isNullOrUndefined(s)&&(s=sf.base.select("."+e+"."+d,this.element)),sf.base.isNullOrUndefined(s)&&(s=this.focussedElement?this.focussedElement:sf.base.select("."+e+"."+f,this.element)),sf.base.isNullOrUndefined(s)&&(t=sf.base.select("."+e,this.element)),sf.base.isNullOrUndefined(s)?sf.base.isNullOrUndefined(t)?this.element.firstElementChild:t:s},y.prototype.setFullRow=function(e){(e?sf.base.addClass:sf.base.removeClass)([this.element],"e-fullrow-wrap"),this.options.fullRowSelect=e},y.prototype.onMouseOver=function(s){var i=s.target,n=i.classList,o=sf.base.closest(i,"."+e);!o||n.contains(t)||n.contains(e)?this.removeHover():o&&!o.classList.contains(m)&&this.setHover(o)},y.prototype.setHover=function(e){e.classList.contains(i)||(this.removeHover(),sf.base.addClass([e],i))},y.prototype.removeHover=function(){var e=sf.base.selectAll("."+i,this.element);e&&e.length&&sf.base.removeClass(e,i)},y.prototype.checkNode=function(e){var t=this.getFocusedNode(),s=sf.base.select("."+p,t),i=sf.base.select(" .e-frame",s).classList.contains(l);t.classList.contains(m)||0!==t.getElementsByClassName("e-checkbox-disabled").length||this.validateCheckNode(s,i,t,e)},y.prototype.openNode=function(s,i){var l=this.getFocusedNode(),r=sf.base.select("div."+a,l);if(s){if(!r)return;r.classList.contains(o)?this.expandAction(l,i):this.focusNextNode(l,!0)}else if(r&&r.classList.contains(n))this.tapEvent=i,this.expandArgs=this.getExpandEvent(l,i),this.dotNetRef.invokeMethodAsync("NodeCollapsingEventCallback",this.expandArgs,!1);else{var d=sf.base.closest(sf.base.closest(l,"."+t),"."+e);if(!d)return;d.classList.contains(m)||(this.setNodeFocus(l,d),this.navigateToFocus(!0),d.focus())}},y.prototype.getScrollParent=function(e){return sf.base.isNullOrUndefined(e)?null:e.scrollHeight>e.clientHeight?e:this.getScrollParent(e.parentElement)},y.prototype.navigateToFocus=function(e){var t=this.getFocusedNode().querySelector("."+u),s=t.getBoundingClientRect(),i=this.getScrollParent(this.element);if(!sf.base.isNullOrUndefined(i)){var n=i.getBoundingClientRect();s.bottom>n.bottom?i.scrollTop+=s.bottom-n.bottom:s.top<n.top&&(i.scrollTop-=n.top-s.top)}this.isVisibleInViewport(t)||t.scrollIntoView(e)},y.prototype.isVisibleInViewport=function(e){var t=e.getBoundingClientRect();return t.top>=0&&t.left>=0&&t.bottom<=(window.innerHeight||document.documentElement.clientHeight)&&t.right<=(window.innerWidth||document.documentElement.clientWidth)},y.prototype.setNodeFocus=function(e,t){sf.base.removeClass([e],f),sf.base.Browser.isDevice||e.setAttribute("tabindex","-1"),t.classList.contains(m)||(this.focussedElement=t,sf.base.addClass([t],f),sf.base.Browser.isDevice||t.setAttribute("tabindex","0"),this.updateIdAttr(e,t))},y.prototype.focusNextNode=function(e,t){var s=t?this.getNextNode(e):this.getPrevNode(e);if(this.setNodeFocus(e,s),this.navigateToFocus(!t),s.classList.contains(m)){var i=s.lastChild;null==s.previousSibling&&s.classList.contains("e-level-1")?this.focusNextNode(s,!0):null==s.nextSibling&&s.classList.contains("e-node-collapsed")||null==s.nextSibling&&i.classList.contains(u)?this.focusNextNode(s,!1):this.focusNextNode(s,t)}s.focus()},y.prototype.shiftKeySelect=function(e,t){if(this.options.allowMultiSelection){var s=this.getFocusedNode(),i=e?this.getNextNode(s):this.getPrevNode(s);this.removeHover(),this.setFocusElement(i),this.focussedElement=i,this.toggleSelect(i,t,!1),this.navigateToFocus(!e)}else this.navigateNode(e)},y.prototype.getNextNode=function(e){var t,s=this.liList.indexOf(e);do{if(s++,t=this.liList[s],sf.base.isNullOrUndefined(t))return e}while(!sf.base.isVisible(t));return t},y.prototype.getPrevNode=function(e){var t,s=this.liList.indexOf(e);do{if(s--,t=this.liList[s],sf.base.isNullOrUndefined(t))return e}while(!sf.base.isVisible(t));return t},y.prototype.getRootNode=function(){var e,t=0;do{e=this.liList[t],t++}while(!sf.base.isVisible(e));return e},y.prototype.getEndNode=function(){var e,t=this.liList.length-1;do{e=this.liList[t],t--}while(!sf.base.isVisible(e));return e},y.prototype.navigateNode=function(e){this.focusNextNode(this.getFocusedNode(),e)},y.prototype.updateOldText=function(e){this.oldText=e},y.prototype.onPropertyChanged=function(e){for(var t=0,s=Object.keys(e);t<s.length;t++){switch(s[t]){case"showCheckBox":this.options.showCheckBox=e.showCheckBox;break;case"allowDragAndDrop":this.setDragAndDrop(e.allowDragAndDrop);break;case"allowTextWrap":this.options.allowTextWrap=e.allowTextWrap,this.setTextWrap();break;case"allowEditing":this.wireEditingEvents(e.allowEditing);break;case"disabled":this.options.disabled!==e.disabled&&(this.options.disabled=e.disabled,this.setDisabledMode(e.disabled));break;case"dragArea":this.setDragArea(e.dropArea);break;case"cssClass":this.setCssClass(e.cssClass);break;case"fullRowSelect":this.setFullRow(e.fullRowSelect);break;case"expandOnType":this.options.expandOnType=e.expandOnType,this.wireExpandOnEvent(!1),this.setExpandOnType(),"None"===this.options.expandOnType||this.options.disabled||this.wireExpandOnEvent(!0);break;case"enableRtl":this.options.enableRtl=e.enableRtl,(this.options.enableRtl?sf.base.addClass:sf.base.removeClass)([this.element],"e-rtl");break;case"animation":this.options.animation=e.animation}}},y.prototype.navigateRootNode=function(e){var t=this.getFocusedNode(),s=e?this.getRootNode():this.getEndNode();s.classList.contains(m)||(this.setNodeFocus(t,s),this.navigateToFocus(e))},y.prototype.selectGivenNodes=function(e){for(var t=0,s=e;t<s.length;t++){var i=s[t];i.classList.contains(m)||this.selectNode(i,null,!0)}},y.prototype.beginEdit=function(e){var t=this.element.querySelector('[data-uid="'+e+'"]');sf.base.isNullOrUndefined(t)||this.options.disabled||this.createTextbox(t,null)},y.prototype.ensureVisible=function(e){var t=this.element.querySelector('[data-uid="'+e+'"]');sf.base.isNullOrUndefined(t)||setTimeout((function(){t.scrollIntoView(!0)}),450)},y.prototype.nodeCollapse=function(e){var t=this.element.querySelector('[data-uid="'+e+'"]');this.collapseAction(t,null,!1)},y.prototype.nodeExpand=function(e){var t=this.element.querySelector('[data-uid="'+e+'"]');this.expandAction(t,null)},y.prototype.nodeSelection=function(t){var s=[];if(t)for(var i=sf.base.selectAll("."+e,this.element),n=new Set(t),o=0,a=i;o<a.length;o++){var l=a[o],r=l.closest(".e-list-item").getAttribute("data-uid");n.has(r)&&s.push(l)}var c=sf.base.selectAll("."+e+"."+d,this.element);sf.base.removeClass(c,d),sf.base.addClass(s,d)},y.prototype.nodeCheck=function(t,s){var i=[],n=[];if(t)for(var o=sf.base.selectAll(".e-frame",this.element),a=new Set(t),r=new Set(s),d=0,c=o;d<c.length;d++){var f=c[d],p=f.closest("."+e).getAttribute("data-uid");a.has(p)&&i.push(f),r.size&&r.has(p)&&n.push(f)}var u=sf.base.selectAll("."+l,this.element);sf.base.removeClass(u,l);var h=sf.base.selectAll(".e-stop",this.element);sf.base.removeClass(h,"e-stop"),sf.base.addClass(i,l),sf.base.addClass(n,"e-stop")},y.prototype.KeyActionHandler=function(t,s){this.liList=Array.prototype.slice.call(sf.base.selectAll("."+e,this.element));var i=this.element.querySelector('[data-uid="'+s+'"]'),n=sf.base.isNullOrUndefined(i)?this.getFocusedNode():i;switch(t.action){case"space":this.options.showCheckBox?this.checkNode(this.keyAction):this.toggleSelect(n,this.keyAction,!1);break;case"moveRight":this.keyBoardAction=!0,this.openNode(!this.options.enableRtl,this.keyAction);break;case"moveLeft":this.keyBoardAction=!0,this.openNode(this.options.enableRtl,this.keyAction);break;case"shiftDown":this.shiftKeySelect(!0,this.keyAction);break;case"moveDown":case"ctrlDown":case"csDown":this.navigateNode(!0);break;case"shiftUp":this.shiftKeySelect(!1,this.keyAction);break;case"moveUp":case"ctrlUp":case"csUp":this.navigateNode(!1);break;case"home":case"shiftHome":case"ctrlHome":case"csHome":this.navigateRootNode(!0);break;case"end":case"shiftEnd":case"ctrlEnd":case"csEnd":this.navigateRootNode(!1);break;case"enter":case"ctrlEnter":case"shiftEnter":case"csEnter":case"shiftSpace":case"ctrlSpace":this.toggleSelect(n,this.keyAction,!1);break;case"f2":this.options.allowEditing&&!n.classList.contains(m)&&this.createTextbox(n,this.keyAction);break;case"ctrlA":if(this.options.allowMultiSelection){var o=sf.base.selectAll("."+e+":not(."+d+")",this.element);this.selectGivenNodes(o)}}this.isKeyUp=!1;var a=this;setTimeout((function(){a.keyBoardAction&&(a.setHover(a.getFocusedNode()),a.keyBoardAction=!1)}),100)},y}();return{initialize:function(e,t,s,i){new y(e,t,s,i);var n=window.sfBlazor.getCompInstance(e);n.render(),t&&!sf.base.isNullOrUndefined(n)&&n.options.allowTextWrap&&n.updateWrap(),document.getElementById(t.id)&&n.dotNetRef.invokeMethodAsync("CreatedEvent",null)},updateTextWrap:function(e){var t=window.sfBlazor.getCompInstance(e);!sf.base.isNullOrUndefined(t)&&t.options.allowTextWrap&&t.updateWrap()},dataSourceChanged:function(e){var t=window.sfBlazor.getCompInstance(e);sf.base.isNullOrUndefined(t)||(t.unWireEvents(),t.wireEvents())},collapseAction:function(e,t){var s=window.sfBlazor.getCompInstance(e);if(!sf.base.isNullOrUndefined(s)){var i=s.element.querySelector('[data-uid="'+t+'"]');s.collapseAction(i,null,!1)}},NodeCollapseAction:function(e,t,s,i){var n=window.sfBlazor.getCompInstance(e);if(!sf.base.isNullOrUndefined(n)){var o=n.element.querySelector('[data-uid="'+t+'"]');n.collapseAction(o,null,i,s)}},expandAction:function(e,t){var s=window.sfBlazor.getCompInstance(e);if(!sf.base.isNullOrUndefined(s)){var i=s.element.querySelector('[data-uid="'+t+'"]');s.expandAction(i,null)}},expandedNode:function(e,t){var s=window.sfBlazor.getCompInstance(e);sf.base.isNullOrUndefined(s)||setTimeout((function(){s.expandedNode(t)}),10)},collapsedNode:function(e,t){var s=window.sfBlazor.getCompInstance(e);sf.base.isNullOrUndefined(s)||s.collapsedNode(t)},KeyActionHandler:function(e,t,s){var i=window.sfBlazor.getCompInstance(e);sf.base.isNullOrUndefined(i)||setTimeout((function(){i.KeyActionHandler(t,s)}),i.isAnimationCompleted?0:i.animationObj.duration)},setMultiSelect:function(e,t){var s=window.sfBlazor.getCompInstance(e);sf.base.isNullOrUndefined(s)||s.setMultiSelect(t)},dragStartActionContinue:function(e,t){var s=window.sfBlazor.getCompInstance(e);sf.base.isNullOrUndefined(s)||s.dragStartActionContinue(t)},dragNodeStop:function(e,t){var s=window.sfBlazor.getCompInstance(e);sf.base.isNullOrUndefined(s)||s.dragNodeStop(t)},nodeDragging:function(e,t){var s=window.sfBlazor.getCompInstance(e);sf.base.isNullOrUndefined(s)||s.nodeDragging(t)},setFocus:function(e,t){if(!sf.base.isNullOrUndefined(e)&&!sf.base.isNullOrUndefined(t)){var s=document.getElementById(e.id);s.focus(),s.setSelectionRange(0,s.value.length)}},nodeEdited:function(e){sf.base.isNullOrUndefined(e)||sf.base.removeClass([e],"e-editing")},updateOldText:function(e,t){var s=window.sfBlazor.getCompInstance(e);sf.base.isNullOrUndefined(s)||s.updateOldText(t)},updateSpinnerClass:function(e){var t=window.sfBlazor.getCompInstance(e);sf.base.isNullOrUndefined(t)||t.updateSpinnerClass()},onPropertyChanged:function(e,t){var s=window.sfBlazor.getCompInstance(e);sf.base.isNullOrUndefined(s)||s.onPropertyChanged(t)},beginEdit:function(e,t){var s=window.sfBlazor.getCompInstance(e);sf.base.isNullOrUndefined(s)||s.beginEdit(t)},ensureVisible:function(e,t){var s=window.sfBlazor.getCompInstance(e);sf.base.isNullOrUndefined(s)||s.ensureVisible(t)},nodeCollapse:function(e,t){var s=window.sfBlazor.getCompInstance(e);sf.base.isNullOrUndefined(s)||s.nodeCollapse(t)},nodeExpand:function(e,t){var s=window.sfBlazor.getCompInstance(e);sf.base.isNullOrUndefined(s)||s.nodeExpand(t)},nodeSelection:function(e,t){var s=window.sfBlazor.getCompInstance(e);sf.base.isNullOrUndefined(s)||s.nodeSelection(t)},nodeCheck:function(e,t,s){var i=window.sfBlazor.getCompInstance(e);sf.base.isNullOrUndefined(i)||i.nodeCheck(t,s)},getAriaLevel:function(e,t){var s=window.sfBlazor.getCompInstance(e),i=0;if(!sf.base.isNullOrUndefined(s)){var n=s.element.querySelector('[data-uid="'+t.nodeData.id+'"]');i=parseInt(n.getAttribute("aria-level"),10)}return i}}}()}}]);(async()=>{await import(`${document.baseURI}_content/Syncfusion.Blazor/scripts/syncfusion-blazor-base.min.js?v=26.1.undefined`).then(()=>{sfBlazor.loadDependencies('sftreeview');})})();