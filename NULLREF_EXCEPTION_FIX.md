# NullReferenceException 修復報告

## 🔍 問題診斷

### 原始錯誤
```
System.NullReferenceException: Object reference not set to an instance of an object.
at Program.<>c.<<<Main>$>b__0_10>d.MoveN<PERSON>t() in Program.cs:line 229
```

### 根本原因
1. **返回類型不匹配**：`GetUserInfoFromSsoTokenAsync` 方法簽名是 `Task<UserInfo?>`，但內部直接返回 `null`
2. **異常處理不完整**：`GetUserBasicProfileFromApi` 方法可能拋出異常，但沒有被正確捕獲
3. **API 調用失敗**：SSO API 調用可能因為網路問題或認證失敗而拋出異常

## 🔧 修復內容

### 1. 修正返回類型問題
**問題**：
```csharp
// 錯誤：直接返回 null，但方法簽名是 Task<UserInfo?>
return null;
```

**修復**：
```csharp
// 正確：返回 Task.FromResult<UserInfo?>(null)
return Task.FromResult<UserInfo?>(null);
```

### 2. 增強異常處理
**修復前**：
```csharp
var userProfile = ssoService.GetUserBasicProfileFromApi(ssoToken);
```

**修復後**：
```csharp
JObject? userProfile = null;

try
{
    userProfile = ssoService.GetUserBasicProfileFromApi(ssoToken);
}
catch (Exception apiEx)
{
    logger.LogError(apiEx, "Failed to call SSO API");
    return Task.FromResult<UserInfo?>(null);
}
```

### 3. 改善錯誤日誌
添加更詳細的錯誤日誌記錄：
```csharp
logger.LogWarning("SSO token validation failed. Error code: {ErrorCode}", 
    userProfile?["ERROR_CODE"]?.ToString() ?? "null");
```

### 4. 添加必要的 using 指令
```csharp
using Newtonsoft.Json.Linq;
```

## 📋 修改的檔案

### Program.cs
**修改內容**：
- 修正 `GetUserInfoFromSsoTokenAsync` 方法的返回類型處理
- 添加 SSO API 調用的異常處理
- 改善錯誤日誌記錄
- 添加 `Newtonsoft.Json.Linq` using 指令

**關鍵變更**：
1. **第 426, 437 行**：修正 null 返回值
2. **第 424-432 行**：添加 API 調用異常處理
3. **第 434-437 行**：改善錯誤日誌

## 🧪 測試步驟

### 1. 基本功能測試
1. 重新啟動應用程式
2. 登入 YCRS 系統
3. 嘗試下載檔案
4. 確認不再出現 NullReferenceException

### 2. 異常情況測試
1. **無效 SSO Token**：
   - 清除瀏覽器 cookies
   - 嘗試直接訪問下載 URL
   - 應該看到 401 錯誤而不是 500 錯誤

2. **SSO API 失敗**：
   - 如果 SSO API 無法連接
   - 應該看到適當的錯誤訊息而不是異常

### 3. 日誌檢查
查看應用程式日誌，應該看到：
- 成功情況：`"Download request authenticated: ... by user ..."`
- 失敗情況：`"Failed to call SSO API"` 或 `"SSO token validation failed"`

## 🔍 故障排除

### 如果仍然出現 NullReferenceException
1. **檢查堆疊追蹤**：確認異常發生的確切行號
2. **檢查日誌**：查看是否有相關的錯誤訊息
3. **檢查 SSO Token**：確認瀏覽器中是否有有效的 `PUBLIC_APP_USER_SSO_TOKEN` cookie

### 如果下載仍然失敗
1. **檢查 SSO API 連接**：確認能夠連接到 `https://eipapi.e-land.gov.tw`
2. **檢查認證資訊**：確認 `app_private_id` 和 `app_private_passwd` 正確
3. **檢查 Token 有效性**：確認 SSO Token 沒有過期

## 📝 技術細節

### 異常處理流程
1. **API 調用異常**：捕獲並記錄，返回 null
2. **Token 驗證失敗**：記錄錯誤碼，返回 null
3. **Profile 解析失敗**：記錄警告，返回 null
4. **其他異常**：由外層 catch 處理

### 返回值處理
```csharp
// 所有錯誤情況都返回相同格式
return Task.FromResult<UserInfo?>(null);

// 成功情況返回用戶資訊
return Task.FromResult(new UserInfo { ... });
```

## ✅ 預期結果

修復後，系統應該：
- ✅ 不再出現 NullReferenceException
- ✅ 正確處理 SSO API 調用失敗的情況
- ✅ 提供清楚的錯誤日誌
- ✅ 在異常情況下返回適當的 HTTP 狀態碼（401, 403, 404）

## 🔧 後續改進建議

1. **快取機制**：考慮快取有效的 SSO Token 驗證結果
2. **重試機制**：為 SSO API 調用添加重試邏輯
3. **監控告警**：為 SSO API 失敗添加監控告警
4. **效能優化**：考慮異步處理 SSO 驗證

修復已完成，建議立即測試以確認問題已解決。
