using System.Text.RegularExpressions;

namespace Intra2025.Services
{
    /// <summary>
    /// 資料遮罩服務，用於保護敏感個人資料
    /// </summary>
    public class DataMaskingService
    {
        /// <summary>
        /// 遮罩身分證字號，顯示格式：A12****678
        /// </summary>
        /// <param name="idNumber">身分證字號</param>
        /// <returns>遮罩後的身分證字號</returns>
        public static string MaskIdNumber(string? idNumber)
        {
            if (string.IsNullOrWhiteSpace(idNumber))
                return string.Empty;

            // 移除空白字元
            idNumber = idNumber.Trim();

            // 檢查是否為有效的身分證字號格式（1個英文字母 + 9個數字）
            if (!IsValidIdNumberFormat(idNumber))
                return idNumber; // 如果格式不正確，直接返回原值

            // 遮罩中間4位數字：A12****678
            if (idNumber.Length == 10)
            {
                return $"{idNumber.Substring(0, 3)}****{idNumber.Substring(7, 3)}";
            }

            return idNumber;
        }

        /// <summary>
        /// 遮罩姓名，顯示格式：王*明 或 歐陽**
        /// </summary>
        /// <param name="name">姓名</param>
        /// <returns>遮罩後的姓名</returns>
        public static string MaskName(string? name)
        {
            if (string.IsNullOrWhiteSpace(name))
                return string.Empty;

            name = name.Trim();

            if (name.Length <= 1)
                return name;

            if (name.Length == 2)
            {
                // 兩個字的姓名：王*
                return $"{name[0]}*";
            }
            else if (name.Length == 3)
            {
                // 三個字的姓名：王*明
                return $"{name[0]}*{name[2]}";
            }
            else
            {
                // 四個字以上的姓名：歐陽**
                return $"{name.Substring(0, 2)}{"*".PadRight(name.Length - 2, '*')}";
            }
        }

        /// <summary>
        /// 遮罩電話號碼，顯示格式：09****1234
        /// </summary>
        /// <param name="phoneNumber">電話號碼</param>
        /// <returns>遮罩後的電話號碼</returns>
        public static string MaskPhoneNumber(string? phoneNumber)
        {
            if (string.IsNullOrWhiteSpace(phoneNumber))
                return string.Empty;

            // 移除所有非數字字元
            var digitsOnly = Regex.Replace(phoneNumber, @"[^\d]", "");

            if (digitsOnly.Length < 6)
                return phoneNumber; // 太短的號碼不遮罩

            if (digitsOnly.Length <= 10)
            {
                // 手機號碼格式：09****1234
                var start = digitsOnly.Substring(0, 2);
                var end = digitsOnly.Substring(digitsOnly.Length - 4);
                var middle = new string('*', digitsOnly.Length - 6);
                return $"{start}{middle}{end}";
            }

            return phoneNumber;
        }

        /// <summary>
        /// 遮罩地址，只顯示縣市和區域
        /// </summary>
        /// <param name="address">地址</param>
        /// <returns>遮罩後的地址</returns>
        public static string MaskAddress(string? address)
        {
            if (string.IsNullOrWhiteSpace(address))
                return string.Empty;

            address = address.Trim();

            // 尋找縣市和區的位置
            var countyMatch = Regex.Match(address, @"(台北市|新北市|桃園市|台中市|台南市|高雄市|基隆市|新竹市|嘉義市|宜蘭縣|新竹縣|苗栗縣|彰化縣|南投縣|雲林縣|嘉義縣|屏東縣|台東縣|花蓮縣|澎湖縣|金門縣|連江縣)");
            var districtMatch = Regex.Match(address, @"[^縣市]*[區鄉鎮市]");

            if (countyMatch.Success)
            {
                var county = countyMatch.Value;
                if (districtMatch.Success)
                {
                    var district = districtMatch.Value;
                    return $"{county}{district}***";
                }
                return $"{county}***";
            }

            // 如果找不到縣市，只顯示前幾個字
            if (address.Length > 6)
            {
                return $"{address.Substring(0, 6)}***";
            }

            return address;
        }

        /// <summary>
        /// 檢查身分證字號格式是否有效
        /// </summary>
        /// <param name="idNumber">身分證字號</param>
        /// <returns>是否為有效格式</returns>
        private static bool IsValidIdNumberFormat(string idNumber)
        {
            if (string.IsNullOrWhiteSpace(idNumber) || idNumber.Length != 10)
                return false;

            // 檢查第一個字元是否為英文字母
            if (!char.IsLetter(idNumber[0]))
                return false;

            // 檢查後9個字元是否為數字
            for (int i = 1; i < 10; i++)
            {
                if (!char.IsDigit(idNumber[i]))
                    return false;
            }

            return true;
        }

        /// <summary>
        /// 根據使用者權限決定是否需要遮罩
        /// </summary>
        /// <param name="isAdmin">是否為管理員</param>
        /// <param name="isOwner">是否為資料所有者</param>
        /// <returns>是否需要遮罩</returns>
        public static bool ShouldMaskData(bool isAdmin, bool isOwner = false)
        {
            // 管理員或資料所有者可以看到完整資料
            return !(isAdmin || isOwner);
        }
    }
}
