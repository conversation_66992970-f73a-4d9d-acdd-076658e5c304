﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Text.Json;
using System.Text.Json.Serialization;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Hosting;
using static Intra2025.Components.Base.JsonDataLoader;

namespace Intra2025.Components.Base
{
    public class JsonDataLoader
    {

        private readonly IWebHostEnvironment env;

        // Constructor to inject IWebHostEnvironment
        public JsonDataLoader(IWebHostEnvironment env)
        {
            this.env = env;
        }

        // Load categories from JSON file
        public List<Category> LoadCategoriesFromJson(string kind)  //傳入kind(類別)=> A: 兒少關懷服務通報 B:溫馨關懷表
        {
            var filePath = Path.Combine(env.ContentRootPath, "App_Data", "Config", "ChildCareKind.json");

            if (!File.Exists(filePath))
            {
                throw new FileNotFoundException("The JSON file was not found.", filePath);
            }

            var jsonString = File.ReadAllText(filePath);
            var options = new JsonSerializerOptions
            {
                MaxDepth = 64, // 限制 JSON 深度，防止深度巢狀攻擊
                PropertyNameCaseInsensitive = true // 允許不區分大小寫的屬性名稱匹配
            };
            var categories = JsonSerializer.Deserialize<List<Category>>(jsonString, options);

            if (categories == null)
            {
                throw new InvalidDataException("Failed to deserialize the JSON data into a list of categories.");
            }

            var filteredCaseBelongs = categories.Where(cb => cb.Id?.StartsWith(kind) == true).ToList();

            return filteredCaseBelongs;
        }

        public async Task<List<Category>> LoadCategoriesFromJsonAsync(string kind)  //傳入kind(類別)=> A: 兒少關懷服務通報 B:溫馨關懷表
        {
            var filePath = Path.Combine(env.ContentRootPath, "App_Data", "Config", "ChildCareKind.json");

            if (!File.Exists(filePath))
            {
                throw new FileNotFoundException("The JSON file was not found.", filePath);
            }

            var jsonString = await File.ReadAllTextAsync(filePath);
            var options = new JsonSerializerOptions
            {
                MaxDepth = 64, // 限制 JSON 深度，防止深度巢狀攻擊
                PropertyNameCaseInsensitive = true // 允許不區分大小寫的屬性名稱匹配
            };
            var categories = JsonSerializer.Deserialize<List<Category>>(jsonString, options);

            if (categories == null)
            {
                throw new InvalidDataException("Failed to deserialize the JSON data into a list of categories.");
            }

            var filteredCaseBelongs = categories.Where(cb => cb.Id?.StartsWith(kind) == true).ToList();

            return filteredCaseBelongs;
        }

        public async Task<List<CaseBelong>> LoadCaseBelongFromJsonAsync()
        {
            var filePath = Path.Combine(env.ContentRootPath, "App_Data", "Config", "CaseBelong.json");

            if (!File.Exists(filePath))
            {
                throw new FileNotFoundException("The JSON file was not found.", filePath);
            }

            var jsonString = await File.ReadAllTextAsync(filePath);
            var options = new JsonSerializerOptions
            {
                MaxDepth = 64, // 限制 JSON 深度，防止深度巢狀攻擊
                PropertyNameCaseInsensitive = true // 允許不區分大小寫的屬性名稱匹配
            };
            var caseBelongs = JsonSerializer.Deserialize<List<CaseBelong>>(jsonString, options);

            if (caseBelongs == null)
            {
                throw new InvalidDataException("Failed to deserialize the JSON data into a list of categories.");
            }

            return caseBelongs;
        } //讀取12個戶所資料

        public List<CaseBelong> LoadCaseBelongFromJson()
        {
            var filePath = Path.Combine(env.ContentRootPath, "App_Data", "Config", "CaseBelong.json");

            if (!File.Exists(filePath))
            {
                throw new FileNotFoundException("The JSON file was not found.", filePath);
            }

            var jsonString = File.ReadAllText(filePath);
            var options = new JsonSerializerOptions
            {
                MaxDepth = 64, // 限制 JSON 深度，防止深度巢狀攻擊
                PropertyNameCaseInsensitive = true // 允許不區分大小寫的屬性名稱匹配
            };
            var caseBelongs = JsonSerializer.Deserialize<List<CaseBelong>>(jsonString, options);

            if (caseBelongs == null)
            {
                throw new InvalidDataException("Failed to deserialize the JSON data into a list of categories.");
            }

            return caseBelongs;
        }  //讀取12個戶所資料

        public async Task<List<string>> LoadRecipientsFromJsonAsync()
        {
            // JSON 檔案路徑
            var jsonFilePath = Path.Combine(env.ContentRootPath, "App_Data", "Config", "EmailList.json");

            if (!File.Exists(jsonFilePath))
            {
                throw new FileNotFoundException($"檔案未找到：{jsonFilePath}");
            }

            var jsonContent = await File.ReadAllTextAsync(jsonFilePath);
            var options = new JsonSerializerOptions
            {
                MaxDepth = 64, // 限制 JSON 深度，防止深度巢狀攻擊
                PropertyNameCaseInsensitive = true // 允許不區分大小寫的屬性名稱匹配
            };
            var emailEntries = JsonSerializer.Deserialize<List<Recipient>>(jsonContent, options);

            // 過濾符合類別的有效電子郵件
            var recipients = new List<string>();
            if (emailEntries != null)
            {
                foreach (var entry in emailEntries)
                {
                    if (!string.IsNullOrEmpty(entry.Email))
                    {
                        recipients.Add(entry.Email);
                    }
                }
            }
            return recipients;
        } //讀取【守護寶貝即時通】要通知發送的MAIL清單

        public async Task<List<string>> GetAdminAccountsAsync(string module = "")
        {
            var jsonFilePath = Path.Combine(env.ContentRootPath, "Config", "sysconfig.json");
            try
            {
                var json = await File.ReadAllTextAsync(jsonFilePath);
                var options = new JsonSerializerOptions
                {
                    MaxDepth = 64, // 限制 JSON 深度，防止深度巢狀攻擊
                    PropertyNameCaseInsensitive = true // 允許不區分大小寫的屬性名稱匹配
                };
                var data = JsonSerializer.Deserialize<Dictionary<string, object>>(json, options);
                
                if (data?["AdminAccounts"] is JsonElement adminElement)
                {
                    if (string.IsNullOrEmpty(module))
                    {
                        // 返回所有管理者帳號（去重）
                        var allAdmins = new HashSet<string>();
                        foreach (var prop in adminElement.EnumerateObject())
                        {
                            foreach (var admin in prop.Value.EnumerateObject())
                            {
                                allAdmins.Add(admin.Name);
                            }
                        }
                        return allAdmins.ToList();
                    }
                    else
                    {
                        // 返回特定模組的管理者帳號
                        if (adminElement.TryGetProperty(module, out var moduleAdmins))
                        {
                            return moduleAdmins.EnumerateObject()
                                .Select(x => x.Name)
                                .ToList();
                        }
                    }
                }
                
                return new List<string>();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"讀取帳號列表時發生錯誤: {ex.Message}");
                return new List<string>();
            }
        } //讀取【管理者】的清單

        public List<string> GetAdminAccounts(string module = "")
        {
            // 同步版本
            return GetAdminAccountsAsync(module).GetAwaiter().GetResult();
        } //讀取【管理者】的清單

        public class Category
        {
            [JsonPropertyName("id")]
            public string? Id { get; set; }

            [JsonPropertyName("name")]
            public string? Name { get; set; }
        } //定義【個案類型】

        public class CaseBelong
        {
            [JsonPropertyName("id")]
            public string? Id { get; set; }

            [JsonPropertyName("name")]
            public string? Name { get; set; }
        } //定義【所屬戶所】

        public class Recipient
        {
            [JsonPropertyName("orga")]
            public string? Orga { get; set; }
            [JsonPropertyName("name")]
            public string? Name { get; set; }
            [JsonPropertyName("email")]
            public string? Email { get; set; }
            [JsonPropertyName("JobInf")]
            public string? JobInf { get; set; }
            [JsonPropertyName("memo")]
            public string? Memo { get; set; }
        } //定義【Mail】的資料格式
    }
}
