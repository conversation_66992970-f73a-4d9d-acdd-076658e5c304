﻿@page "/ReportList"
@layout ReportLayout
@using Intra2025.Components.Base
@using Intra2025.Models.Reports.Report
@using Intra2025.Services
@using Intra2025.Models
@using Intra2025.Data
@using Intra2025.Components.Layout
@using Microsoft.EntityFrameworkCore
@inject IConfiguration Configuration
@inject LaunchSettingsService LaunchSettings
@inject HttpClient Http
@inject ReportDbContext DbContext
@inherits BasePageComponent

@{
    string all = "ALL";
}
<title>規劃報告書資料庫</title>

<head>
    <link href="modals.css" rel="stylesheet" />
</head>


<div style="display: flex;justify-content: center;align-items: center;">
    <div class="loader"></div>
</div>

<div style="justify-content: center;align-items: center;width:90%">
    <div class="category-section">
        <button class="category-button" id="btnAll" @onclick="() => LoadReports(all)">全部 (@allReports.Where(r => r.State
                        == 1).Count())</button>
        @foreach (var category in categories)
        {
            <button class="category-button" @onclick="() => LoadReports(category.Key)">
                @category.Key (@category.Value)
            </button>
        }
    </div>
</div>

<div class="search-section">
    <div class="search-bar">
        <span>年度:</span><select @bind="selectedYear">
            <option value="">全部</option>
            @foreach (var year in years)
            {
                <option value="@year">@year</option>
            }
        </select>
    </div>
    <div class="search-bar">
        <input type="text" @bind="searchTerm" placeholder="搜尋計畫名稱或內容簡述" @onkeydown="HandleKeyDown">
        <button @onclick="Search">搜尋</button>
    </div>
    <button class="clear" @onclick="Clear">清除</button>
</div>
<div style="justify-content: center;align-items: center;">
    <table>
        <thead>
            <tr>
                <th style="width:5%">年度</th>
                <th style="width:10%">主管單位</th>
                <th style="width:50%">計畫名稱</th>
                <th style="width:10%">規劃階段期間</th>
                @*  <th style="width:5%">點閱數</th> *@
            </tr>
        </thead>
        <tbody>
            @foreach (var report in pagedReports)
            {
                <tr>
                    <td>@report.PlanYear</td>
                    <td>@report.PowerDepart</td>
                    <td>
                        <span class="hover one" @onclick="() => ShowDetails(report)"><a> @report.Topic </a></span>
                    </td>
                    <td>
                        @((report.Begindate != null && report.Begindate != DateTime.MinValue)
                                            ? $"{report.Begindate.Value.Year - 1911:D3}/{report.Begindate.Value.Month:D2}"
                                            : "無資料") ~
                        @((report.Enddate != null && report.Enddate != DateTime.MinValue)
                                            ? $"{report.Enddate.Value.Year - 1911:D3}/{report.Enddate.Value.Month:D2}"
                                            : "無資料")
                    </td>

                    @*  <td>@report.Readcount</td> *@
                </tr>
            }
        </tbody>
    </table>

    <div class="pagination">
        <div class="pagination-controls">
            <button @onclick="PreviousPage" disabled="@(currentPage == 1)">上一頁</button>
            <span>第 @currentPage 頁</span><span>【共 @totalPages 頁】(案件總數量: @totalRecordCount)</span>
            <button @onclick="NextPage" disabled="@(currentPage == totalPages)">下一頁</button>
        </div>
    </div>
</div>

@if (showModal)
{
    <div class="modal">
        <div class="modalM-content">
            <span class="close" @onclick="CloseDetails">&times;</span>
            <h2>計畫詳細資訊</h2>
            <table class="detail-table">
                <tr>
                    <td style="width:15%">計畫編號</td>
                    <td>@selectedReport?.Sno </td>
                </tr>
                <tr>
                    <td style="width:15%">年度</td>
                    <td>@selectedReport?.PlanYear </td>
                </tr>
                <tr>
                    <td>主管單位</td>
                    <td>@selectedReport?.PowerDepart</td>
                </tr>
                <tr>
                    <td>聯絡資訊</td>
                    <td>@selectedReport?.Tel</td>
                </tr>
                <tr>
                    <td>計畫名稱</td>
                    <td>@selectedReport?.Topic</td>
                </tr>
                <tr>
                    <td>規劃期間</td>
                    <td>@selectedReport?.Begindate?.ToString("yyyy/M")~@selectedReport?.Enddate?.ToString("yyyy/M")</td>
                </tr>
                <tr>
                    <td>計畫內容</td>
                    <td>@selectedReport?.Content</td>
                </tr>
                <tr>
                    <td>委託單位</td>
                    <td>@selectedReport?.Consign</td>
                </tr>
                <tr>
                    <td>經費</td>
                    <td>@selectedReport?.Fund 元</td>
                </tr>
                <tr>
                    <td>附檔下載</td>
                    <td>
                        @if (reportFiles.TryGetValue(selectedReport?.Sno.ToString() ?? "0", out var files))
                        {
                            string fileName = "";
                            int i = 1;
                            @foreach (var file in files)
                            {
                                if (@file.Filedesc.Equals(""))
                                    fileName = file.Filename;
                                else
                                    fileName = file.Filedesc;
                                @(i.ToString() + ".")

                                <a href="@file.Filepath" style=" display: inline-block;padding-bottom: 10px;">@fileName</a>
                                i++;
                                <br />
                            }
                        }
                        else
                        {
                            <span>無附檔</span>
                        }
                    </td>
                </tr>
                <tr>
                    <td>紙本位置</td>
                    <td>@selectedReport?.PaperLocation</td>
                </tr>
                @* <tr>
                    <td>點閱數</td>
                    <td>@selectedReport?.Readcount </td>
                </tr> *@
            </table>
        </div>
    </div>
}

@code {
    private Dictionary<string, int> categories = new Dictionary<string, int>();
    private List<ELS_REPORTC> allReports = new List<ELS_REPORTC>();
    private List<ELS_REPORTC> filteredReports = new List<ELS_REPORTC>();
    private List<ELS_REPORTC> pagedReports = new List<ELS_REPORTC>();

    private List<int> years = new List<int>();
    private Dictionary<string, List<ELS_REPORT_FILEC>> reportFiles = new Dictionary<string, List<ELS_REPORT_FILEC>>();
    private int totalRecordCount = 0; // 總記錄數
    private string selectedCategory = "";
    private string searchTerm = "";
    private int currentPage = 1;
    private int pageSize = 10;
    private int totalPages => (int)Math.Ceiling((double)filteredReports.Count / pageSize);
    private string? apiBaseUrl;

    protected override void OnInitialized()
    {
        apiBaseUrl = LaunchSettings.GetApplicationUrl();
    }

    protected override async Task OnInitializedAsync()
    {
        await base.OnInitializedAsync();

        // 基本權限檢查：確保使用者已登入
        if (!_userState.IsInitialized || string.IsNullOrEmpty(_userState.Account))
        {
            NavigationManager.NavigateTo("https://eip.e-land.gov.tw", true);
            return;
        }

        // 根據使用者權限載入報告資料
        if (_userState.DepCode.Substring(0, 3).Equals("113"))
        {
            // 管理單位「計畫處」可以看到所有資料
            allReports = await DbContext.ELS_REPORT
                .Where(r => r.PlanYear.HasValue) // 篩選掉 PlanYear 為 NULL 的報告
                .Where(r => !string.IsNullOrEmpty(r.Topic)) // 篩選掉 Topic 為 NULL 或空字串的值
                .Where(r => r.State == 1) // 只顯示已發布的報告
                .ToListAsync();
        }
        else
        {
            // 一般使用者只能看到自己單位的資料
            allReports = await DbContext.ELS_REPORT
                .Where(r => r.PlanYear.HasValue) // 篩選掉 PlanYear 為 NULL 的報告
                .Where(r => r.Postdepartid == _userState.DepCode.Substring(0, 3)) // 比對部門代碼
                .Where(r => !string.IsNullOrEmpty(r.Topic)) // 篩選掉 Topic 為 NULL 或空字串的值
                .Where(r => r.State == 1) // 只顯示已發布的報告
                .ToListAsync();
        }

        years = allReports
        .Where(r => r.PlanYear.HasValue) // 篩選掉 NULL 值
        .Select(r => r.PlanYear ?? 0) // 將可空型別轉換為非可空型別
        .Distinct()
        .OrderByDescending(y => y)
        .ToList();

        //抓取報告檔
        var files = await DbContext.ELS_REPORT_FILES.ToListAsync();
        foreach (var file in files)
        {
            if (!reportFiles.ContainsKey(file.Bbssno ?? ""))
            {
                reportFiles[file.Bbssno ?? ""] = new List<ELS_REPORT_FILEC>();
            }
            reportFiles[file.Bbssno ?? ""].Add(file);
        }

        totalRecordCount = allReports.Count(r => r.State == 1);

        CalculateCategoryCounts();
        FilterReports();
    }

    private void CalculateCategoryCounts()
    {
        categories = allReports
        .Where(r => !string.IsNullOrEmpty(r.PowerDepart)) // 篩選掉 NULL 或空字串
        .Where(r => r.State == 1)
        .GroupBy(r => r.PowerDepart ?? "未知")
        .ToDictionary(g => g.Key, g => g.Count());
    }

    private void LoadReports(string category)
    {
        Console.WriteLine($"Loading reports for category: {category}");
        selectedCategory = category;
        currentPage = 1;
        FilterReports();
    }

    private void Search()
    {
        currentPage = 1;
        FilterReports();
    }

    private void Clear()
    {
        searchTerm = "";
        FilterReports();
    }

    private void FilterReports()
    {
        filteredReports = allReports
        .Where(r => string.IsNullOrEmpty(selectedCategory) || selectedCategory == "ALL" || r.PowerDepart == selectedCategory)
        .Where(r => string.IsNullOrEmpty(selectedYear) || r.PlanYear.ToString() == selectedYear)
        .Where(r => r.State == 1)
        .Where(r => string.IsNullOrEmpty(searchTerm) ||
        (!string.IsNullOrEmpty(r.Topic) && r.Topic.Contains(searchTerm, StringComparison.OrdinalIgnoreCase)) ||
        (!string.IsNullOrEmpty(r.Content) && r.Content.Contains(searchTerm, StringComparison.OrdinalIgnoreCase)))
        .OrderByDescending(r => r.PlanYear)
        .ToList();

        //Console.WriteLine($"Filtered reports count: {filteredReports.Count}");
        UpdatePagedReports();
    }

    private void UpdatePagedReports()
    {
        pagedReports = filteredReports
        .Skip((currentPage - 1) * pageSize)
        .Take(pageSize)
        .ToList();
        //Console.WriteLine($"Paged reports count: {pagedReports.Count}");
    }

    private void PreviousPage()
    {
        if (currentPage > 1)
        {
            currentPage--;
            UpdatePagedReports();
        }
    }

    private void NextPage()
    {
        if (currentPage < totalPages)
        {
            currentPage++;
            UpdatePagedReports();
        }
    }

    private string selectedYear
    {
        get => _selectedYear;
        set
        {
            if (_selectedYear != value)
            {
                _selectedYear = value;
                FilterReports();
            }
        }
    }

    private string _selectedYear = "";

    private bool showModal = false;
    private ELS_REPORTC? selectedReport;
    private string modalContent = string.Empty;

    private async Task ShowDetails(ELS_REPORTC report)
    {
        selectedReport = report;
        if (selectedReport != null)
        {
            selectedReport.Readcount = selectedReport.Readcount ?? 0; // 如果 Readcount為null，則設置為 0
            selectedReport.Readcount++;
            DbContext.ELS_REPORT.Update(selectedReport);
            await DbContext.SaveChangesAsync();
        }
        showModal = true;
    }

    [JSInvokable]
    private void CloseDetails()
    {
        showModal = false;
    }

    private void HandleKeyDown(KeyboardEventArgs args)
    {
        if (args.Key == "Enter")
        {
            Search();
        }
    }
}


<script>
    window.addEventListener('click', function (event) {
        if (event.target.classList.contains('modal')) {
            DotNet.invokeMethodAsync('BSSR_Intra', 'CloseDetails');
        }
    });
</script>