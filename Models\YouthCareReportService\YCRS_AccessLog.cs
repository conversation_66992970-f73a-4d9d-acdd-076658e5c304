﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Intra2025.Models.YouthCareReportService
{
    public class YCRS_AccessLog
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int Id { get; set; } // 系統流水號

        public string? Sn { get; set; } // 資料序號

        [Required]
        [MaxLength(50)]
        public string? Action { get; set; } // 動作

        [Required]
        [MaxLength(50)]
        public string? Account { get; set; } // 操作人員  [Required]

        [MaxLength(50)]
        public string? IP { get; set; } // 來源IP

        [Required]
        public DateTime Timestamp { get; set; } // 操作時間
    }
}
