/*!*  filename: sf-floating-action-button.min.js 
*     version : 26.1.41 
*     Copyright Syncfusion Inc. 2001 - 2024. All rights reserved. 
*     Use of this code is subject to the terms of our license.
*     A copy of the current license can be obtained at any time by e-mailing 
*     <EMAIL>. Any infringement will be prosecuted under 
*     applicable laws. 
*/
(window.webpackJsonp=window.webpackJsonp||[]).push([[27],{"./bundles/sf-floating-action-button.js":function(t,i,e){"use strict";e.r(i);e("./modules/sf-floating-action-button.js")},"./modules/sf-floating-action-button.js":function(t,i){window.sfBlazor=window.sfBlazor||{},window.sfBlazor.Fab=function(){"use strict";var t=function(){function t(t){window.sfBlazor=window.sfBlazor,this.updateContext(t),window.sfBlazor.setCompInstance(this),this.initialize()}return t.prototype.updateContext=function(t){sf.base.extend(this,this,t)},t.prototype.initialize=function(){this.checkTarget(),this.setClientProps(!0),this.visible&&this.element.classList.remove("e-fab-hidden")},t.prototype.checkTarget=function(){this.targetEle=null,sf.base.isNullOrUndefined(this.target)||"string"!=typeof this.target||(this.targetEle=document.querySelector(this.target)),this.isFixed=sf.base.isNullOrUndefined(this.targetEle),!this.isFixed&&this.targetEle.appendChild(this.element),this.isFixed?this.element.classList.add("e-fab-fixed"):this.element.classList.remove("e-fab-fixed")},t.prototype.setClientProps=function(t){t&&(this.fabPosition=this.isFixed?"Fixed":"Absolute")},t.prototype.validateTarget=function(t){this.updateContext(t),this.checkTarget(),this.setClientProps(!0)},t.prototype.setPosition=function(t){this.updateContext(t),this.setClientProps(!1)},t}();return{initialize:function(i){return i.dataId?new t(i).fabPosition:{}},validateTarget:function(t){if(t.dataId){var i=window.sfBlazor.getCompInstance(t.dataId);return i.validateTarget(t),i.fabPosition}return{}},setPosition:function(t){if(t.dataId){var i=window.sfBlazor.getCompInstance(t.dataId);return i.setPosition(t),i.fabPosition}return{}},destroy:function(t){t&&window.sfBlazor.getCompInstance(t).destroy()}}}()}}]);(async()=>{await import(`${document.baseURI}_content/Syncfusion.Blazor/scripts/syncfusion-blazor-base.min.js?v=26.1.undefined`).then(()=>{})})();