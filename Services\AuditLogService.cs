using Intra2025.Components.Base;
using Intra2025.Models.Security;
using Microsoft.Extensions.Logging;

namespace Intra2025.Services
{
    public class AuditLogService
    {
        private readonly ILogger<AuditLogService> _logger;
        private readonly UserState _userState;
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly SecurityEventLogService _securityEventLogService;

        public AuditLogService(ILogger<AuditLogService> logger, UserState userState,
            IHttpContextAccessor httpContextAccessor, SecurityEventLogService securityEventLogService)
        {
            _logger = logger;
            _userState = userState;
            _httpContextAccessor = httpContextAccessor;
            _securityEventLogService = securityEventLogService;
        }

        public void Log(string action, string details)
        {
            var ipAddress = _httpContextAccessor.HttpContext?.Connection?.RemoteIpAddress?.ToString() ?? "N/A";
            _logger.LogInformation("Audit Log: User={User}, Action={Action}, IP={IpAddress}, Details={Details}",
                _userState.Account, action, ipAddress, details);
        }

        /// <summary>
        /// 記錄敏感資料存取
        /// </summary>
        public async Task LogSensitiveDataAccessAsync(string dataType, string operation, string details = "")
        {
            // 記錄到傳統稽核日誌
            Log($"SENSITIVE_DATA_ACCESS_{operation}", $"DataType: {dataType}, Details: {details}");

            // 記錄到安全事件日誌
            await _securityEventLogService.LogSensitiveDataAccessAsync(dataType, operation);
        }

        /// <summary>
        /// 記錄存取被拒絕事件
        /// </summary>
        public async Task LogAccessDeniedAsync(string resource, string reason, string details = "")
        {
            // 記錄到傳統稽核日誌
            Log("ACCESS_DENIED", $"Resource: {resource}, Reason: {reason}, Details: {details}");

            // 記錄到安全事件日誌
            await _securityEventLogService.LogAccessDeniedAsync(resource, reason);
        }

        /// <summary>
        /// 記錄檔案操作
        /// </summary>
        public async Task LogFileOperationAsync(string operation, string fileName, string filePath = "")
        {
            // 記錄到傳統稽核日誌
            Log($"FILE_{operation}", $"FileName: {fileName}, Path: {filePath}");

            // 記錄到安全事件日誌
            var eventType = operation.ToUpper() switch
            {
                "DOWNLOAD" => SecurityEventType.FILE_DOWNLOAD,
                "UPLOAD" => SecurityEventType.FILE_UPLOAD,
                _ => SecurityEventType.DATA_VIEW
            };

            await _securityEventLogService.LogCustomEventAsync(
                eventType,
                SecurityEventSeverity.Information,
                "FileOperation",
                new Dictionary<string, object>
                {
                    ["Operation"] = operation,
                    ["FileName"] = fileName,
                    ["FilePath"] = filePath
                });
        }
    }
}
