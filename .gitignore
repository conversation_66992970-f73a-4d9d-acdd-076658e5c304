# Visual Studio
.vs/
*.suo
*.user
*.userosscache
*.sln.docstates
*.builds
*.pidb
*.log
*.scc

# Build results
[Dd]ebug/
[Rr]elease/
[Bb]in/
[Oo]bj/

# Packages
*.nupkg
*.snupkg
*.dll
*.exe
*.pdb

# Rider
.idea/

# Other
*.bak
*.tmp
*.swp

# User-specific files
*.user

# Visual Studio Code
.vscode/

# Azure Functions
local.settings.json

# Test Results
TestResults/

# npm
node_modules/

# Logs
logs/

# App_Data
App_Data/

# wwwroot/uploads
wwwroot/uploads/

# wwwroot/temp
wwwroot/temp/

# wwwroot/exports
wwwroot/exports/

# Sensitive configuration files (if not handled by other means)
appsettings.Development.json
appsettings.json

# Project-specific files
Intra2025.csproj.user
