(window.webpackJsonp=window.webpackJsonp||[]).push([[77],{"./bundles/svgbase.js":function(t,e,i){"use strict";i.r(e);i("./modules/svgbase.js")},"./modules/svgbase.js":function(t,e){function i(t){return(i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}window.sf=window.sf||{},window.sf.svgbase=function(t){"use strict";var e=function(){function t(t){this.svgLink="http://www.w3.org/2000/svg",this.rootId=t}return t.prototype.getOptionValue=function(t,e){return t[e]},t.prototype.createSvg=function(t){return sf.base.isNullOrUndefined(t.id)&&(t.id=this.rootId+"_svg"),this.svgObj=document.getElementById(t.id),sf.base.isNullOrUndefined(document.getElementById(t.id))&&(this.svgObj=document.createElementNS(this.svgLink,"svg")),this.svgObj=this.setElementAttributes(t,this.svgObj),this.setSVGSize(t.width,t.height),this.svgObj},t.prototype.setSVGSize=function(t,e){var i=document.getElementById(this.rootId),r=sf.base.isNullOrUndefined(i)?null:i.getBoundingClientRect();sf.base.isNullOrUndefined(this.width)||this.width<=0?this.svgObj.setAttribute("width",t?t.toString():r.width.toString()):this.svgObj.setAttribute("width",this.width.toString()),sf.base.isNullOrUndefined(this.height)||this.height<=0?this.svgObj.setAttribute("height",e?e.toString():"450"):this.svgObj.setAttribute("height",this.height.toString())},t.prototype.drawPath=function(t){var e=document.getElementById(t.id);return null===e&&(e=document.createElementNS(this.svgLink,"path")),e=this.setElementAttributes(t,e)},t.prototype.drawLine=function(t){var e=document.getElementById(t.id);return null===e&&(e=document.createElementNS(this.svgLink,"line")),e=this.setElementAttributes(t,e)},t.prototype.drawRectangle=function(t){var e=document.getElementById(t.id);return null===e&&(e=document.createElementNS(this.svgLink,"rect")),e=this.setElementAttributes(t,e)},t.prototype.drawCircle=function(t){var e=document.getElementById(t.id);return null===e&&(e=document.createElementNS(this.svgLink,"circle")),e=this.setElementAttributes(t,e)},t.prototype.drawPolyline=function(t){var e=document.getElementById(t.id);return null===e&&(e=document.createElementNS(this.svgLink,"polyline")),e=this.setElementAttributes(t,e)},t.prototype.drawEllipse=function(t){var e=document.getElementById(t.id);return null===e&&(e=document.createElementNS(this.svgLink,"ellipse")),e=this.setElementAttributes(t,e)},t.prototype.drawPolygon=function(t){var e=document.getElementById(t.id);return null===e&&(e=document.createElementNS(this.svgLink,"polygon")),e=this.setElementAttributes(t,e)},t.prototype.drawImage=function(t){var e=document.createElementNS(this.svgLink,"image");return e.setAttributeNS(null,"height",t.height.toString()),e.setAttributeNS(null,"width",t.width.toString()),e.setAttributeNS("http://www.w3.org/1999/xlink","href",t.href),e.setAttributeNS(null,"x",t.x.toString()),e.setAttributeNS(null,"y",t.y.toString()),e.setAttributeNS(null,"id",t.id),e.setAttributeNS(null,"visibility",t.visibility),sf.base.isNullOrUndefined(this.getOptionValue(t,"clip-path"))||e.setAttributeNS(null,"clip-path",this.getOptionValue(t,"clip-path")),sf.base.isNullOrUndefined(t.preserveAspectRatio)||e.setAttributeNS(null,"preserveAspectRatio",t.preserveAspectRatio),e},t.prototype.createText=function(t,e){var i=document.createElementNS(this.svgLink,"text");return i=this.setElementAttributes(t,i),sf.base.isNullOrUndefined(e)||(i.textContent=e),i},t.prototype.createTSpan=function(t,e){var i=document.createElementNS(this.svgLink,"tspan");return i=this.setElementAttributes(t,i),sf.base.isNullOrUndefined(e)||(i.textContent=e),i},t.prototype.createTitle=function(t){var e=document.createElementNS(this.svgLink,"title");return e.textContent=t,e},t.prototype.createDefs=function(){return document.createElementNS(this.svgLink,"defs")},t.prototype.createClipPath=function(t){var e=document.createElementNS(this.svgLink,"clipPath");return e=this.setElementAttributes(t,e)},t.prototype.createForeignObject=function(t){var e=document.createElementNS(this.svgLink,"foreignObject");return e=this.setElementAttributes(t,e)},t.prototype.createGroup=function(t){var e=document.createElementNS(this.svgLink,"g");return e=this.setElementAttributes(t,e)},t.prototype.createPattern=function(t,e){var i=document.createElementNS(this.svgLink,e);return i=this.setElementAttributes(t,i)},t.prototype.createRadialGradient=function(t,e,i){var r;if(sf.base.isNullOrUndefined(t[0].colorStop))r=t[0].color.toString();else{var o={id:this.rootId+"_"+e+"radialGradient",cx:i.cx+"%",cy:i.cy+"%",r:i.r+"%",fx:i.fx+"%",fy:i.fy+"%"};this.drawGradient("radialGradient",o,t),r="url(#"+this.rootId+"_"+e+"radialGradient)"}return r},t.prototype.createLinearGradient=function(t,e,i){var r;if(sf.base.isNullOrUndefined(t[0].colorStop))r=t[0].color.toString();else{var o={id:this.rootId+"_"+e+"linearGradient",x1:i.x1+"%",y1:i.y1+"%",x2:i.x2+"%",y2:i.y2+"%"};this.drawGradient("linearGradient",o,t),r="url(#"+this.rootId+"_"+e+"linearGradient)"}return r},t.prototype.drawGradient=function(t,e,i){var r=this.createDefs(),o=document.createElementNS(this.svgLink,t);o=this.setElementAttributes(e,o);for(var s=0;s<i.length;s++){var n=document.createElementNS(this.svgLink,"stop");n.setAttribute("offset",i[s].colorStop),n.setAttribute("stop-color",i[s].color),n.setAttribute("stop-opacity",i[s].opacity?i[s].opacity:"1"),sf.base.isNullOrUndefined(i[s].style)||(n.style.cssText=i[s].style),o.appendChild(n)}return r.appendChild(o),r},t.prototype.drawClipPath=function(t){var e=this.createDefs(),i=this.createClipPath({id:t.id});t.id=t.id+"_Rect";var r=this.drawRectangle(t);return i.appendChild(r),e.appendChild(i),e},t.prototype.drawCircularClipPath=function(t){var e=this.createDefs(),i=this.createClipPath({id:t.id});t.id=t.id+"_Circle";var r=this.drawCircle(t);return i.appendChild(r),e.appendChild(i),e},t.prototype.setElementAttributes=function(t,e){for(var i=Object.keys(t),r=0;r<i.length;r++)"style"===i[r]?e.style.cssText=t[i[r]]:e.setAttribute(i[r],t[i[r]]);return e},t.prototype.createCanvas=function(){return null},t}(),r=function(){function t(t){this.rootId=t}return t.prototype.getOptionValue=function(t,e){return t[e]},t.prototype.createCanvas=function(t){var e=document.createElement("canvas");return e.setAttribute("id",this.rootId+"_canvas"),this.ctx=e.getContext("2d"),this.canvasObj=e,this.setCanvasSize(t.width,t.height),this.canvasObj},t.prototype.setCanvasSize=function(t,e){var i=document.getElementById(this.rootId),r=sf.base.isNullOrUndefined(i)?null:i.getBoundingClientRect();sf.base.isNullOrUndefined(this.width)?this.canvasObj.setAttribute("width",t?t.toString():r.width.toString()):this.canvasObj.setAttribute("width",this.width.toString()),sf.base.isNullOrUndefined(this.height)?this.canvasObj.setAttribute("height",e?e.toString():"450"):this.canvasObj.setAttribute("height",this.height.toString())},t.prototype.setAttributes=function(t){this.ctx.lineWidth=this.getOptionValue(t,"stroke-width");var e=this.getOptionValue(t,"stroke-dasharray");if(!sf.base.isNullOrUndefined(e)){var i=e.split(",");this.ctx.setLineDash([parseInt(i[0],10),parseInt(i[1],10)])}this.ctx.strokeStyle=this.getOptionValue(t,"stroke")},t.prototype.drawLine=function(t){this.ctx.save(),this.ctx.beginPath(),this.ctx.lineWidth=this.getOptionValue(t,"stroke-width"),this.ctx.strokeStyle=t.stroke,this.ctx.moveTo(t.x1,t.y1),this.ctx.lineTo(t.x2,t.y2),this.ctx.stroke(),this.ctx.restore()},t.prototype.drawRectangle=function(t,e){var i=this.ctx,r=t.rx;return this.ctx.save(),this.ctx.beginPath(),e&&this.ctx.translate(e[0],e[1]),this.ctx.globalAlpha=this.getOptionValue(t,"opacity"),this.setAttributes(t),this.ctx.rect(t.x,t.y,t.width,t.height),null!==r&&r>=0?this.drawCornerRadius(t):("none"===t.fill&&(t.fill="transparent"),this.ctx.fillStyle=t.fill,this.ctx.fillRect(t.x,t.y,t.width,t.height),this.ctx.stroke()),this.ctx.restore(),this.ctx=i,this.canvasObj},t.prototype.drawCornerRadius=function(t){var e=t.rx,i=t.x,r=t.y,o=t.width,s=t.height;"none"===t.fill&&(t.fill="transparent"),this.ctx.fillStyle=t.fill,o<2*e&&(e=o/2),s<2*e&&(e=s/2),this.ctx.beginPath(),this.ctx.moveTo(i+o-e,r),this.ctx.arcTo(i+o,r,i+o,r+s,e),this.ctx.arcTo(i+o,r+s,i,r+s,e),this.ctx.arcTo(i,r+s,i,r,e),this.ctx.arcTo(i,r,i+o,r,e),this.ctx.closePath(),this.ctx.fill(),this.ctx.stroke()},t.prototype.drawPath=function(t,e){var i=t.d.split(" "),r=this.getOptionValue(t,"stroke-width"),o=this.ctx,s=!0;this.ctx.save(),this.ctx.beginPath(),e&&this.ctx.translate(e[0],e[1]),this.ctx.globalAlpha=t.opacity?t.opacity:this.getOptionValue(t,"fill-opacity"),this.setAttributes(t);for(var n=0;n<i.length;n+=3){var a=parseFloat(i[n+1]),l=parseFloat(i[n+2]);switch(i[n]){case"M":t.innerR||t.cx||this.ctx.moveTo(a,l);break;case"L":t.innerR||this.ctx.lineTo(a,l);break;case"Q":var h=parseFloat(i[n+3]),d=parseFloat(i[n+4]);this.ctx.quadraticCurveTo(a,l,h,d),n+=2;break;case"C":var p=parseFloat(i[n+3]),c=parseFloat(i[n+4]),f=parseFloat(i[n+5]),y=parseFloat(i[n+6]);this.ctx.bezierCurveTo(a,l,p,c,f,y),n+=4;break;case"A":t.innerR?s&&(this.ctx.arc(t.x,t.y,t.radius,t.start,t.end,t.counterClockWise),this.ctx.arc(t.x,t.y,t.innerR,t.end,t.start,!t.counterClockWise),s=!1):t.cx?this.ctx.arc(t.cx,t.cy,t.radius,0,2*Math.PI,t.counterClockWise):(this.ctx.moveTo(t.x,t.y),this.ctx.arc(t.x,t.y,t.radius,t.start,t.end,t.counterClockWise),this.ctx.lineTo(t.x,t.y)),n+=5;break;case"z":case"Z":this.ctx.closePath(),n-=2}}return"none"!==t.fill&&void 0!==t.fill&&(this.ctx.fillStyle=t.fill,this.ctx.fill()),r>0&&this.ctx.stroke(),this.ctx.restore(),this.ctx=o,this.canvasObj},t.prototype.createText=function(t,e,i,r,o,s){var n=this.getOptionValue(t,"font-weight");sf.base.isNullOrUndefined(n)||"regular"!==n.toLowerCase()||(n="normal");var a=this.getOptionValue(t,"font-size"),l=this.getOptionValue(t,"font-family"),h=this.getOptionValue(t,"font-style").toLowerCase()+" "+n+" "+a+" "+l,d=this.getOptionValue(t,"text-anchor"),p=void 0!==t.opacity?t.opacity:1;if("middle"===d&&(d="center"),this.ctx.save(),this.ctx.fillStyle=t.fill,this.ctx.font=h,this.ctx.textAlign=d,this.ctx.globalAlpha=p,t.baseline&&(this.ctx.textBaseline=t.baseline),!s){this.ctx.translate(t.x+0+(i||0),t.y+(r||0)),this.ctx.rotate(t.labelRotation*Math.PI/180)}return this.ctx.fillText(e,s?t.x:0,s?o:0),this.ctx.restore(),this.canvasObj},t.prototype.drawCircle=function(t,e){var i=this.ctx;return this.ctx.save(),this.ctx.beginPath(),this.ctx.arc(t.cx,t.cy,t.r,0,2*Math.PI),this.ctx.fillStyle=t.fill,this.ctx.globalAlpha=t.opacity,this.ctx.fill(),e&&this.ctx.translate(e[0],e[1]),this.setAttributes(t),this.ctx.stroke(),this.ctx.restore(),this.ctx=i,this.canvasObj},t.prototype.drawPolyline=function(t){this.ctx.save(),this.ctx.beginPath();for(var e=t.points.split(" "),i=0;i<e.length-1;i++){var r=e[i].split(","),o=parseFloat(r[0]),s=parseFloat(r[1]);0===i?this.ctx.moveTo(o,s):this.ctx.lineTo(o,s)}this.ctx.lineWidth=this.getOptionValue(t,"stroke-width"),this.ctx.strokeStyle=t.stroke,this.ctx.stroke(),this.ctx.restore()},t.prototype.drawEllipse=function(t,e){var i=this.ctx,r=Math.max(t.rx,t.ry),o=t.rx/r,s=t.ry/r;this.ctx.save(),this.ctx.beginPath(),this.ctx.translate(t.cx,t.cy),e&&this.ctx.translate(e[0],e[1]),this.ctx.save(),this.ctx.scale(o,s),this.ctx.arc(0,0,r,0,2*Math.PI,!1),this.ctx.fillStyle=t.fill,this.ctx.fill(),this.ctx.restore(),this.ctx.lineWidth=this.getOptionValue(t,"stroke-width"),this.ctx.strokeStyle=t.stroke,this.ctx.stroke(),this.ctx.restore(),this.ctx=i},t.prototype.drawImage=function(t){this.ctx.save();var e=new Image;sf.base.isNullOrUndefined(t.href)||(e.src=t.href,this.ctx.drawImage(e,t.x,t.y,t.width,t.height)),this.ctx.restore()},t.prototype.createLinearGradient=function(t){var e;return sf.base.isNullOrUndefined(t[0].colorStop)||(e=this.ctx.createLinearGradient(0,0,0,this.canvasObj.height)),this.setGradientValues(t,e)},t.prototype.createRadialGradient=function(t){var e;return sf.base.isNullOrUndefined(t[0].colorStop)||(e=this.ctx.createRadialGradient(0,0,0,0,0,this.canvasObj.height)),this.setGradientValues(t,e)},t.prototype.setGradientValues=function(t,e){var i;if(sf.base.isNullOrUndefined(t[0].colorStop))i=t[0].color.toString();else{for(var r=0;r<=t.length-1;r++){var o=t[r].color,s=t[r].colorStop.slice(0,-1),n=parseInt(s,10)/100;e.addColorStop(n,o)}i=e.toString()}return i},t.prototype.setElementAttributes=function(t,e){for(var i=Object.keys(t),r=Object.keys(t).map((function(e){return t[e]})),o=0;o<i.length;o++)e.setAttribute(i[o],r[o]);return null},t.prototype.updateCanvasAttributes=function(t){this.setElementAttributes(t,this.canvasObj);var e=this.ctx;if(!sf.base.isNullOrUndefined(this.dataUrl)){var i=new Image;i.onload=function(){e.drawImage(i,0,0)},i.src=this.dataUrl}},t.prototype.clearRect=function(t){this.ctx.restore(),this.ctx.clearRect(t.x,t.y,t.width,t.height)},t.prototype.createGroup=function(){return null},t.prototype.drawClipPath=function(){return null},t.prototype.drawCircularClipPath=function(){return null},t.prototype.canvasClip=function(t){this.ctx.save(),this.ctx.fillStyle="transparent",this.ctx.rect(t.x,t.y,t.width,t.height),this.ctx.fill(),this.ctx.clip()},t.prototype.canvasRestore=function(){this.ctx.restore()},t.prototype.drawPolygon=function(){return null},t.prototype.createDefs=function(){return null},t.prototype.createClipPath=function(){return null},t.prototype.createSvg=function(){return null},t}();function o(t){var e;switch(t){case"Highcontrast":case"HighContrast":e={tooltipFill:"#ffffff",tooltipBoldLabel:"#000000",tooltipLightLabel:"#000000",tooltipHeaderLine:"#969696",textStyle:{fontFamily:"Segoe UI",color:"#000000",fontWeight:null}};break;case"MaterialDark":case"FabricDark":case"BootstrapDark":e={tooltipFill:"MaterialDark"===t?"#F4F4F4":"FabricDark"===t?"#A19F9D":"#F0F0F0",tooltipBoldLabel:"MaterialDark"===t?"rgba(18, 18, 18, 1)":"FabricDark"===t?"#DADADA":"#1A1A1A",tooltipLightLabel:"MaterialDark"===t?"rgba(18, 18, 18, 1)":"FabricDark"===t?"#DADADA":"#1A1A1A",tooltipHeaderLine:"#9A9A9A",textStyle:"MaterialDark"===t?{fontFamily:"Roboto",color:"rgba(18, 18, 18, 1)",fontWeight:null}:"FabricDark"===t?{fontFamily:"Segoe UI",color:"#DADADA",fontWeight:null}:{fontFamily:"Helvetica",color:"#1A1A1A",fontWeight:null}};break;case"Bootstrap4":e={tooltipFill:"#212529",tooltipBoldLabel:"#F9FAFB",tooltipLightLabel:"#F9FAFB",tooltipHeaderLine:"rgba(255, 255, 255, 0.2)",textStyle:{fontFamily:"Helvetica",color:"#F9FAFB",fontWeight:null}};break;case"Tailwind":e={tooltipFill:"#111827",tooltipBoldLabel:"#F9FAFB",tooltipLightLabel:"#F9FAFB",tooltipHeaderLine:"#6B7280",textStyle:{fontFamily:"Inter",color:"#F9FAFB",fontWeight:null}};break;case"TailwindDark":e={tooltipFill:"#E9ECEF",tooltipBoldLabel:"#1F2937",tooltipLightLabel:"#1F2937",tooltipHeaderLine:"#9CA3AF",textStyle:{fontFamily:"Inter",color:"#1F2937",fontWeight:null}};break;case"Bootstrap5":e={tooltipFill:"#212529",tooltipBoldLabel:"#F9FAFB",tooltipLightLabel:"#F9FAFB",tooltipHeaderLine:"#9CA3AF",textStyle:{fontFamily:"Helvetica",color:"#F9FAFB",fontWeight:null}};break;case"Bootstrap5Dark":e={tooltipFill:"#E9ECEF",tooltipBoldLabel:"#212529",tooltipLightLabel:"#212529",tooltipHeaderLine:"#ADB5BD",textStyle:{fontFamily:"Helvetica",color:"#212529",fontWeight:null}};break;case"Fluent":e={tooltipFill:"#FFFFFF",tooltipBoldLabel:"#323130",tooltipLightLabel:"#323130",tooltipHeaderLine:"#D2D0CE",textStyle:{fontFamily:"Segoe UI",color:"#323130",fontWeight:null}};break;case"FluentDark":e={tooltipFill:"#323130",tooltipBoldLabel:"#F3F2F2",tooltipLightLabel:"#F3F2F1",tooltipHeaderLine:"#3B3A39",textStyle:{fontFamily:"Segoe UI",color:"#F3F2F1",fontWeight:null}};break;case"Fluent2":e={tooltipFill:"#FFFFFF",tooltipBoldLabel:"#242424",tooltipLightLabel:"#242424",tooltipHeaderLine:"#D2D0CE",textStyle:{fontFamily:"Segoe UI",color:"#242424",fontWeight:null}};break;case"Fluent2Dark":e={tooltipFill:"#292929",tooltipBoldLabel:"#FFFFFF",tooltipLightLabel:"#FFFFFF",tooltipHeaderLine:"#3B3A39",textStyle:{fontFamily:"Segoe UI",color:"#FFFFFF",fontWeight:null}};break;case"Material3":e={tooltipFill:"#313033",tooltipBoldLabel:"#F4EFF4",tooltipLightLabel:"#F4EFF4",tooltipHeaderLine:"#F4EFF4",textStyle:{fontFamily:"Roboto",color:"#F4EFF4",fontWeight:null}};break;case"Material3Dark":e={tooltipFill:"#E6E1E5",tooltipBoldLabel:"#313033",tooltipLightLabel:"#313033",tooltipHeaderLine:"#313033",textStyle:{fontFamily:"Roboto",color:"#313033",fontWeight:null}};break;default:e={tooltipFill:"Material"===t?"#000816":"Fabric"===t?"#FFFFFF":"#212529",tooltipBoldLabel:"Material"===t?"rgba(249, 250, 251, 1)":"Fabric"===t?"#333333":"#F9FAFB",tooltipLightLabel:"Material"===t?"rgba(249, 250, 251, 1)":"Fabric"===t?"#333333":"#F9FAFB",tooltipHeaderLine:"Fabric"===t?"#D2D0CE":"#ffffff",textStyle:"Material"===t?{fontFamily:"Roboto",color:"rgba(249, 250, 251, 1)",fontWeight:null}:"Fabric"===t?{fontFamily:"Segoe UI",color:"#333333",fontWeight:null}:{fontFamily:"Helvetica",color:"#F9FAFB",fontWeight:null}}}return e}var s,n=(s=function(t,e){return(s=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)e.hasOwnProperty(i)&&(t[i]=e[i])})(t,e)},function(t,e){function i(){this.constructor=t}s(t,e),t.prototype=null===e?Object.create(e):(i.prototype=e.prototype,new i)});function a(t,e,i){var r=t||"",o=document.getElementById("chartmeasuretext");if(null===o&&(o=sf.base.createElement("text",{id:"chartmeasuretext"}),document.body.appendChild(o)),"string"==typeof t&&(t.indexOf("<")>-1||t.indexOf(">")>-1)){for(var s=t.split(" "),n=0;n<s.length;n++)-1===s[n].indexOf("<br/>")&&(s[n]=s[n].replace(/[<>]/g,"&"));t=s.join(" ")}o.innerHTML=r.indexOf("<br>")>-1||r.indexOf("<br/>")>-1?r:t,o.style.position="fixed",o.style.fontSize=e.size||i.size,o.style.fontStyle=e.fontStyle||i.fontStyle,o.style.fontFamily=e.fontFamily||i.fontFamily,o.style.visibility="hidden",o.style.top="-100",o.style.left="0",o.style.whiteSpace="nowrap",o.style.lineHeight="normal";var a=o.clientWidth,l=o.clientHeight,h=o.style.fontWeight;return o.style.fontWeight=e.fontWeight||i.fontWeight,new d("bold"===o.style.fontWeight&&"normal"===h?Math.max(a,o.clientWidth):o.clientWidth,"bold"===o.style.fontWeight&&"normal"===h?Math.max(l,o.clientHeight):o.clientHeight)}function l(t,e,i,r,o){return void 0===r&&(r=0),void 0===o&&(o=0),t>=i.x-r&&t<=i.x+i.width+r&&e>=i.y-o&&e<=i.y+i.height+o}function h(t,e,i,r,o,s,n,a,l,h,d){void 0===d&&(d="");var p="",c=i.x,f=i.y,y=i.x+i.width,u=i.y+i.height;return s?(p=(p=p.concat("M "+c+" "+(f+e)+" Q "+c+" "+f+" "+(c+t)+" "+f+"  L "+(y-t)+" "+f+" Q "+y+" "+f+" "+y+" "+(f+e))).concat(" L "+y+" "+(u-e)+" Q "+y+" "+u+" "+(y-t)+" "+u),0!==o&&(p="RangeNavigator"===d?r.x-o>y/2?(p=p.concat(" L "+(r.x+o)+" "+u)).concat(" L "+(l+o)+" "+(u+o)+" L "+r.x+" "+u):(p=p.concat(" L "+r.x+" "+u)).concat(" L "+(l-o)+" "+(u+o)+" L "+(r.x-o)+" "+u):(p=p.concat(" L "+(r.x+o)+" "+u)).concat(" L "+l+" "+(u+o)+" L "+(r.x-o)+" "+u)),p=r.x-o>c||0===o?p.concat(" L "+(c+t)+" "+u+" Q "+c+" "+u+" "+c+" "+(u-e)+" z"):p.concat(" L "+c+" "+(u+e)+" z")):n?p=(p=(p=(p=(p=p.concat("M "+c+" "+(f+e)+" Q "+c+" "+f+" "+(c+t)+" "+f+" L "+(r.x-o)+" "+f)).concat(" L "+l+" "+r.y)).concat(" L "+(r.x+o)+" "+f)).concat(" L "+(y-t)+" "+f+" Q "+y+" "+f+" "+y+" "+(f+e))).concat(" L "+y+" "+(u-e)+" Q "+y+" "+u+" "+(y-t)+" "+u+" L "+(c+t)+" "+u+" Q "+c+" "+u+" "+c+" "+(u-e)+" z"):a?(p=(p=p.concat("M "+c+" "+(f+e)+" Q "+c+" "+f+" "+(c+t)+" "+f)).concat(" L "+(y-t)+" "+f+" Q "+y+" "+f+" "+y+" "+("RangeNavigator"===d?0:f+e)+" L "+y+" "+("RangeNavigator"===d?0:r.y-o)),p="RangeNavigator"===d?p.concat(" L "+(y+o)+" 0"):p.concat(" L "+(y+o)+" "+h),p=(p=(p="RangeNavigator"===d?p.concat(" L "+y+" "+(r.y-e)):p.concat(" L "+y+" "+(r.y+o))).concat(" L "+y+" "+(u-e)+" Q "+y+" "+u+" "+(y-t)+" "+u)).concat(" L "+(c+t)+" "+u+" Q "+c+" "+u+" "+c+" "+(u-e)+" z")):(p=p.concat("M "+(c+t)+" "+f+" Q "+c+" "+f+" "+c+" "+("RangeNavigator"===d?0:f+e)+" L "+c+" "+("RangeNavigator"===d?0:r.y-o)),p="RangeNavigator"===d?p.concat(" L "+(c-o)+" 0"):p.concat(" L "+(c-o)+" "+h),p=(p=(p="RangeNavigator"===d?p.concat(" L "+c+" "+(r.y-e)):p.concat(" L "+c+" "+(r.y+o))).concat(" L "+c+" "+(u-e)+" Q "+c+" "+u+" "+(c+t)+" "+u)).concat(" L "+(y-t)+" "+u+" Q "+y+" "+u+" "+y+" "+(u-e)+" L "+y+" "+(f+e)+" Q "+y+" "+f+" "+(y-t)+" "+f+" z")),p}var d=function(t,e){this.width=t,this.height=e},p=function(t,e,i,r){this.x=t,this.y=e,this.width=i,this.height=r},c=function(t,e){this.isRight=e,this.isBottom=t},f=function(t){this.id=t},y=function(t){function e(e,i,r,o,s,n,a,l){void 0===n&&(n="");var h=t.call(this,e)||this;return h.transform="",h.baseLine="auto",h.labelRotation=0,h.x=i,h.y=r,h.anchor=o,h.text=s,h.transform=n,h.baseLine=a,h.labelRotation=l,h}return n(e,t),e}(f);function u(t){return document.getElementById(t)}function g(t){var e=u(t);e&&sf.base.remove(e)}function m(t,i,r,o,s,n,a){var l=new e(""),h=x(t,r,i,s,o),d=l["draw"+h.functionName](h.renderOption);return d.setAttribute("role",n),d.setAttribute("aria-label",a),d}function x(t,e,i,r,o){var s,n="Path",a=e.width,l=e.height,h=t.x,d=t.y,p=t.x+-a/2,c=t.y+-l/2;switch(i){case"Circle":case"Bubble":n="Ellipse",sf.base.merge(r,{rx:a/2,ry:l/2,cx:h,cy:d});break;case"Plus":s="M "+p+" "+d+" L "+(h+a/2)+" "+d+" M "+h+" "+(d+l/2)+" L "+h+" "+(d+-l/2),sf.base.merge(r,{d:s,stroke:r.fill});break;case"Cross":s="M "+p+" "+(d+-l/2)+" L "+(h+a/2)+" "+(d+l/2)+" M "+p+" "+(d+l/2)+" L "+(h+a/2)+" "+(d+-l/2),sf.base.merge(r,{d:s,stroke:r.fill});break;case"HorizontalLine":s="M "+p+" "+d+" L "+(h+a/2)+" "+d,sf.base.merge(r,{d:s,stroke:r.fill});break;case"VerticalLine":s="M "+h+" "+(d+l/2)+" L "+h+" "+(d+-l/2),sf.base.merge(r,{d:s,stroke:r.fill});break;case"Diamond":s="M "+p+" "+d+" L "+h+" "+(d+-l/2)+" L "+(h+a/2)+" "+d+" L "+h+" "+(d+l/2)+" L "+p+" "+d+" z",sf.base.merge(r,{d:s});break;case"Rectangle":s="M "+p+" "+(d+-l/2)+" L "+(h+a/2)+" "+(d+-l/2)+" L "+(h+a/2)+" "+(d+l/2)+" L "+p+" "+(d+l/2)+" L "+p+" "+(d+-l/2)+" z",sf.base.merge(r,{d:s});break;case"Triangle":s="M "+p+" "+(d+l/2)+" L "+h+" "+(d+-l/2)+" L "+(h+a/2)+" "+(d+l/2)+" L "+p+" "+(d+l/2)+" z",sf.base.merge(r,{d:s});break;case"InvertedTriangle":s="M "+(h+a/2)+" "+(d-l/2)+" L "+h+" "+(d+l/2)+" L "+(h-a/2)+" "+(d-l/2)+" L "+(h+a/2)+" "+(d-l/2)+" z",sf.base.merge(r,{d:s});break;case"Pentagon":for(var f=void 0,y=void 0,u=0;u<=5;u++)f=a/2*Math.cos(Math.PI/180*(72*u)),y=l/2*Math.sin(Math.PI/180*(72*u)),s=0===u?"M "+(h+f)+" "+(d+y)+" ":s.concat("L "+(h+f)+" "+(d+y)+" ");s=s.concat("Z"),sf.base.merge(r,{d:s});break;case"Image":n="Image",sf.base.merge(r,{href:o,height:l,width:a,x:p,y:c})}return{renderOption:r,functionName:n}}var b=function(t){function e(e,i,r,o,s,n,a){var l=t.call(this,e)||this;return l.opacity=s,l.fill=i,l.stroke=o,l["stroke-width"]=r,l["stroke-dasharray"]=n,l.d=a,l}return n(e,t),e}(f);function v(t,i,r,o,s){var n,a=new e("");n={id:t.id,x:t.x,y:t.y,fill:r,"font-size":i.size||s.size,"font-style":i.fontStyle||s.fontStyle,"font-family":i.fontFamily||s.fontFamily,"font-weight":i.fontWeight||s.fontWeight,"text-anchor":t.anchor,transform:t.transform,opacity:i.opacity,"dominant-baseline":t.baseLine};var l="string"==typeof t.text?t.text:t.text[0],h=a.createText(n,l);return o&&o.appendChild(h),h}var w,F=function(t,e){this.x=t,this.y=e},S=(w=function(t,e){return(w=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)e.hasOwnProperty(i)&&(t[i]=e[i])})(t,e)},function(t,e){function i(){this.constructor=t}w(t,e),t.prototype=null===e?Object.create(e):(i.prototype=e.prototype,new i)}),L=function(t,e,r,o){var s,n=arguments.length,a=n<3?e:null===o?o=Object.getOwnPropertyDescriptor(e,r):o;if("object"===("undefined"==typeof Reflect?"undefined":i(Reflect))&&"function"==typeof Reflect.decorate)a=Reflect.decorate(t,e,r,o);else for(var l=t.length-1;l>=0;l--)(s=t[l])&&(a=(n<3?s(a):n>3?s(e,r,a):s(e,r))||a);return n>3&&a&&Object.defineProperty(e,r,a),a},P=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return S(e,t),L([sf.base.Property(null)],e.prototype,"size",void 0),L([sf.base.Property("")],e.prototype,"color",void 0),L([sf.base.Property("Segoe UI")],e.prototype,"fontFamily",void 0),L([sf.base.Property("Normal")],e.prototype,"fontWeight",void 0),L([sf.base.Property("Normal")],e.prototype,"fontStyle",void 0),L([sf.base.Property(1)],e.prototype,"opacity",void 0),e}(sf.base.ChildProperty),T=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return S(e,t),L([sf.base.Property("")],e.prototype,"color",void 0),L([sf.base.Property(1)],e.prototype,"width",void 0),L([sf.base.Property("")],e.prototype,"dashArray",void 0),e}(sf.base.ChildProperty),A=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return S(e,t),L([sf.base.Property(0)],e.prototype,"x",void 0),L([sf.base.Property(0)],e.prototype,"y",void 0),L([sf.base.Property(0)],e.prototype,"width",void 0),L([sf.base.Property(0)],e.prototype,"height",void 0),e}(sf.base.ChildProperty),O=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return S(e,t),L([sf.base.Property(0)],e.prototype,"x",void 0),L([sf.base.Property(0)],e.prototype,"y",void 0),e}(sf.base.ChildProperty),k=function(t){function i(e,i){return t.call(this,e,i)||this}return S(i,t),i.prototype.preRender=function(){this.allowServerDataBinding=!1,this.initPrivateVariable(),this.isCanvas||this.removeSVG(),this.createTooltipElement()},i.prototype.initPrivateVariable=function(){this.renderer=new e(this.element.id),this.themeStyle=o(this.theme),this.formattedText=[],this.padding=5,this.highlightPadding=3,this.areaMargin=10,this.isFirst=!0,this.markerPoint=[]},i.prototype.removeSVG=function(){var t=document.getElementById(this.element.id+"_svg"),e=document.getElementById(this.element.id+"parent_template");this.blazorTemplate&&sf.base.resetBlazorTemplate(this.element.id+"parent_template_blazorTemplate"),t&&t.parentNode&&sf.base.remove(t),e&&e.parentNode&&sf.base.remove(e)},i.prototype.render=function(){if(this.fadeOuted=!1,this.template)this.updateTemplateFn(),this.createTemplate(this.areaBounds,this.location);else{this.renderText(this.isFirst);var t={cancel:!1,name:"tooltipRender",tooltip:this};this.trigger("tooltipRender",t);var e=this.renderTooltipElement(this.areaBounds,this.location);this.drawMarker(e.isBottom,e.isRight,this.markerSize)}this.trigger("loaded",{tooltip:this});var i=document.getElementById("chartmeasuretext");i&&sf.base.remove(i),this.allowServerDataBinding=!0},i.prototype.createTooltipElement=function(){if(this.textElements=[],!this.template||this.shared){this.enableRTL&&this.element.setAttribute("dir","ltr");var t=this.renderer.createSvg({id:this.element.id+"_svg"});this.element.appendChild(t);var e=document.getElementById(this.element.id+"_group");e||(e=this.renderer.createGroup({id:this.element.id+"_group"})).setAttribute("transform","translate(0,0)"),t.appendChild(e);var i=this.renderer.drawPath({id:this.element.id+"_path","stroke-width":"Fabric"!==this.theme&&"Fluent"!==this.theme&&"Fluent2"!==this.theme||this.border.width?this.border.width:1,fill:this.fill||this.themeStyle.tooltipFill,opacity:("TailwindDark"===this.theme||"Tailwind"===this.theme||"Bootstrap5"===this.theme||"Bootstrap5Dark"===this.theme||this.theme.indexOf("Fluent2")>-1)&&.75===this.opacity?1:this.opacity,stroke:this.border.color||("Fabric"===this.theme||"Fluent"===this.theme||"Fluent2"===this.theme?"#D2D0CE":this.border.color)});e.appendChild(i)}},i.prototype.drawMarker=function(t,e,i){if(this.shapes.length<=0)return null;var r,o=0,s=this.renderer.createGroup({id:this.element.id+"_trackball_group"}),n=u(this.element.id+"_group");if(!n)return null;for(var a=(this.enableRTL?this.elementSize.width-i/2:2*this.marginX+i/2)+(e?this.arrowPadding:0),l=0,h=this.shapes;l<h.length;l++){var p=h[l];if("None"!==p){if(r=new b(this.element.id+"_Trackball_"+o,this.palette[o],1,"#cccccc",1,null),this.markerPoint[o]){var c=0;this.header.indexOf("<br")>-1&&(c=this.header.split(/<br.*?>/g).length+o);var f=this.formattedText&&this.formattedText.length>=2?this.getTooltipTextContent(this.formattedText[1])+", "+this.getTooltipTextContent(this.formattedText[0]):"";s.appendChild(m(new F(a,this.markerPoint[o]-this.padding+(t?this.arrowPadding:c)),p,new d(i,i),"",r,"img",f))}o++}}n.appendChild(s)},i.prototype.renderTooltipElement=function(t,e){var i,r=u(this.element.id),o=new F(0,0),s=new F(0,0),n=u(this.element.id+"_svg"),l=u(this.element.id+"_group"),d=u(this.element.id+"_path"),f=!1,y=!1,g=!1,m=0,x=0;if(!sf.base.isNullOrUndefined(l)){if(""!==this.header&&(this.elementSize.height+=this.marginY),this.isFixed){var b=this.elementSize.width+2*this.marginX,v=this.elementSize.height+2*this.marginY;i=new p(e.x,e.y,b,v)}else this.content.length>1?(i=this.sharedTooltipLocation(t,this.location.x,this.location.y),f=!0):(i=this.tooltipLocation(t,e,o,s),this.inverted?(m=(y=i.x<e.x+this.clipBounds.x)?0:this.arrowPadding,this.allowHighlight&&(i.x+=y?this.highlightPadding:-2*this.highlightPadding)):(g=!(f=i.y<e.y+this.clipBounds.y),x=f?0:this.arrowPadding));if(""!==this.header){var w=2,S=0,L=this.isWrap?this.wrappedText:this.header;if(this.isWrap&&"string"==typeof L&&(L.indexOf("<")>-1||L.indexOf(">")>-1))w=L.split("<br>").length;this.header.indexOf("<br")>-1&&(S=5*(this.header.split(/<br.*?>/g).length-1));var P=sf.base.extend({},this.textStyle,null,!0).properties,T=a(this.isWrap?this.wrappedText:this.header,P,this.themeStyle.textStyle).height+this.marginY*w+(g?this.arrowPadding:0)+(this.isWrap?5:S),A="M "+(3*this.marginX+(y||f||g?0:this.arrowPadding))+" "+T+"L "+(i.width+(y||f||g?0:this.arrowPadding)-2*this.marginX)+" "+T,O=this.renderer.drawPath({id:this.element.id+"_header_path","stroke-width":1,fill:null,opacity:"Material3"===this.theme?.2:.8,stroke:this.themeStyle.tooltipHeaderLine,d:A});l.appendChild(O)}var k=this.border.width/2,C=new p(k+m,k+x,i.width-k,i.height-k);if(l.setAttribute("opacity","1"),!this.enableAnimation||this.isFirst||this.crosshair?this.updateDiv(r,i.x,i.y):this.animateTooltipDiv(r,i),n.setAttribute("height",(i.height+this.border.width+(this.inverted?0:this.arrowPadding)+5).toString()),n.setAttribute("width",(i.width+this.border.width+(this.inverted?this.arrowPadding:0)+5).toString()),n.setAttribute("opacity","1"),sf.base.isNullOrUndefined(this.tooltipPlacement)||(f=this.tooltipPlacement.indexOf("Top")>-1,g=this.tooltipPlacement.indexOf("Bottom")>-1,y=this.tooltipPlacement.indexOf("Left")>-1),d.setAttribute("d",h(this.rx,this.ry,C,o,this.arrowPadding,f,g,y,s.x,s.y,this.controlName)),this.enableShadow&&"Bootstrap4"!==this.theme||this.theme.indexOf("Fluent2")>-1){var E=this.element.id+"_shadow";"Tailwind"===this.theme||"TailwindDark"===this.theme||"Bootstrap5"===this.theme||"Bootstrap5Dark"===this.theme?d.setAttribute("box-shadow","0px 1px 2px rgba(0, 0, 0, 0.06), 0px 1px 3px rgba(0, 0, 0, 0.1)"):d.setAttribute("filter",sf.base.Browser.isIE?"":"url(#"+E+")");var N='<filter id="'+E+'" height="130%"><feGaussianBlur in="SourceAlpha" stdDeviation="3"/>';this.theme.indexOf("Fluent2")>-1?N+='<feOffset dx="-1" dy="3.6" result="offsetblur"/><feComponentTransfer><feFuncA type="linear" slope="0.2"/>':N+='<feOffset dx="3" dy="3" result="offsetblur"/><feComponentTransfer><feFuncA type="linear" slope="0.5"/>',N+='</feComponentTransfer><feMerge><feMergeNode/><feMergeNode in="SourceGraphic"/></feMerge></filter>';var B=this.renderer.createDefs();B.setAttribute("id",this.element.id+"SVG_tooltip_definition"),l.appendChild(B),B.innerHTML=N}var z="Fabric"!==this.theme&&"Fluent"!==this.theme&&"Fluent2"!==this.theme||this.border.color?this.border.color:"#D2D0CE";d.setAttribute("stroke",z),sf.base.isNullOrUndefined(this.border.dashArray)||d.setAttribute("stroke-dasharray",this.border.dashArray),this.changeText(new F(m,x),g,!y&&!f&&!g),this.revert&&(this.inverted=!this.inverted,this.revert=!1)}return new c(g,!y&&!f&&!g)},i.prototype.changeText=function(t,e,i){var r=document.getElementById(this.element.id+"_text");e&&r.setAttribute("transform","translate(0,"+this.arrowPadding+")"),i&&r.setAttribute("transform","translate("+this.arrowPadding+" 0)")},i.prototype.findFormattedText=function(){this.formattedText=[],""!==this.header.replace(/<b>/g,"").replace(/<\/b>/g,"").trim()&&(this.formattedText=this.formattedText.concat(this.header)),this.formattedText=this.formattedText.concat(this.content)},i.prototype.renderText=function(t){var e,i,r,o,s,n=0,l=0,h=0,p="properties",c=sf.base.extend({},this.textStyle,null,!0)[p],f=u(this.element.id+"_group"),m="";this.findFormattedText(),this.isWrap=!1;var x="rtl"===document.body.getAttribute("dir")&&!this.enableRTL?"end":"start";this.leftSpace=this.areaBounds.x+this.location.x,this.rightSpace=this.areaBounds.x+this.areaBounds.width-this.leftSpace;var b=this.header.replace(/<b>/g,"").replace(/<\/b>/g,"").trim(),w=this.header.indexOf("<b>")>-1&&this.header.indexOf("</b>")>-1,F=a(this.formattedText[0],c,this.themeStyle.textStyle).width+2*this.marginX+this.arrowPadding,S=this.location.x-F<this.location.x,L=this.areaBounds.x+this.areaBounds.width<this.location.x+F,P=""!==b?this.marginY:0,T=!0,A=!0;this.markerPoint=[];var O,k=this.shapes.length>0?10:0,C=this.shapes.length>0?5:0,E="400",N=this.themeStyle.tooltipLightLabel,B=22/parseFloat("12px")*parseFloat(c.size),z=[],R=0;t&&!this.isCanvas||(g(this.element.id+"_text"),g(this.element.id+"_header_path"),g(this.element.id+"_trackball_group"),g(this.element.id+"SVG_tooltip_definition")),"Chart"===this.controlName&&parseFloat("12px")<parseFloat(c.size)&&(R=parseFloat(c.size)-parseFloat("12px"));for(var M=new y(this.element.id+"_text",2*this.marginX,R+2*this.marginY+2*this.padding+(2===this.marginY?"RangeNavigator"===this.controlName?5:3:0),x,""),D=v(M,c,c.color||this.themeStyle.tooltipBoldLabel,f,this.themeStyle.textStyle),_=1===this.formattedText.length&&this.formattedText[0].indexOf(" : <b>")>-1,I=""!==this.header,W=I&&w?16:13,j=0,H=this.formattedText.length;j<H;j++)if(r=this.formattedText[j].replace(/<(b|strong)>/g,"<b>").replace(/<\/(b|strong)>/g,"</b>").split(/<br.*?>/g),this.isTextWrap&&this.header!==this.formattedText[j]&&-1===this.formattedText[j].indexOf("<br")&&(O=Math.round(this.leftSpace>this.rightSpace?this.leftSpace/W:this.rightSpace/W),r=this.formattedText[j].match(new RegExp(".{1,"+O+"}","g"))),0===j&&!_&&this.isTextWrap&&(this.leftSpace<F||S)&&(this.rightSpace<F||L)&&(O=Math.round(this.leftSpace>this.rightSpace?this.leftSpace/W:this.rightSpace/W),r=(""!==b?b:this.formattedText[j]).match(new RegExp(".{1,"+O+"}","g")),this.wrappedText=w?"<b>"+r.join("<br>")+"</b>":r.join("<br>"),this.isWrap=r.length>1),""!==r[0]){0===j&&""!==b||this.markerPoint.push((""!==b?this.marginY:0)+M.y+n-(0!==R?R/this.markerSize*(parseFloat(c.size)/this.markerSize):0));for(var U=0,V=r.length;U<V;U++){e=r[U].replace(/<b>/g,"<br><b>").replace(/<\/b>/g,"</b><br>").replace(/:/g,this.enableRTL?"<br>‎: <br>":"<br>‎:<br>").split("<br>"),this.enableRTL&&e.length>0&&r[U].match(/:/g)&&(e[0]=e[0].trim(),e.reverse()),h=0,A=!0,n+=B;for(var G=0,Q=e.length;G<Q;G++)if(o=e[G],this.enableRTL&&""!==o&&this.isRTLText(o)&&(o=o.concat("‎")),/\S/.test(o)||""===o||(o=" "),!A&&" "===o||""!==o.replace(/<b>/g,"").replace(/<\/b>/g,"").trim()){h+=" "!==o?4:0,A&&!T?(this.header.indexOf("<br")>-1&&0!==j&&(P+=this.header.split(/<br.*?>/g).length),s={x:2*this.marginX+(k+C),dy:B+(A?P:0),fill:""},P=null):s=T&&A?{x:""===b?2*this.marginX+(k+C):2*this.marginX+(this.isWrap?k+C:0)}:{},A=!1,i=this.renderer.createTSpan(s,""),D.appendChild(i),o.indexOf("<b>")>-1||w&&0===G&&0===j&&(I||this.isWrap)?(E="600",N=this.themeStyle.tooltipBoldLabel,m="font-weight:"+E,c.fontWeight=E,i.setAttribute("fill",this.textStyle.color||N)):(m="600"===E?"font-weight:"+E:"",c.fontWeight=E,i.setAttribute("fill",this.textStyle.color||N)),(o.indexOf("</b>")>-1||w&&G===Q-1&&0===j&&(I||this.isWrap))&&(E="Normal",N=this.themeStyle.tooltipLightLabel),""!==m&&(i.style.fontWeight=m.split("font-weight:")[1],i.style.color=i.getAttribute("fill")),i.style.fontFamily="inherit",i.style.fontStyle="inherit",i.style.fontSize=this.header===this.formattedText[j]?c.size:this.textStyle.size,i.style.fontWeight=this.header===this.formattedText[j]&&-1===(this.header.indexOf("<b>")||this.header.indexOf("</b>"))?this.textStyle.fontWeight||"600":o.indexOf("<b>")>-1||o.indexOf("</b>")>-1?"bold":this.textStyle.fontWeight||c.fontWeight;var X=sf.base.extend({},this.textStyle,null,!0)[p];X.fontWeight=i.style.fontWeight,T=!1,i.textContent=o=this.getTooltipTextContent(o),h+=a(o,X,this.themeStyle.textStyle).width}h-=4,l=Math.max(l,h),z.push(h)}}this.elementSize=new d(l+(l>0?2*this.marginX:0),n),this.elementSize.width+=k+C;var Y=D.childNodes[0];if(""!==b&&Y&&!this.isWrap){c.fontWeight="600";var q=(this.elementSize.width+2*this.padding)/2-a(b,c,this.themeStyle.textStyle).width/2;Y.setAttribute("x",q.toString())}this.renderContentRTL(D,I,k+C,z)},i.prototype.renderContentRTL=function(t,e,i,r){if(this.enableRTL)for(var o=void 0,s=e?1:0,n=0;n<t.childNodes.length;n++)o=t.childNodes[n],e&&!(n>0)||sf.base.isNullOrUndefined(o.getAttribute("x"))||(o.setAttribute("x",(this.elementSize.width-(i+r[s])).toString()),s++)},i.prototype.getTooltipTextContent=function(t){var e=t.match(/<[a-zA-Z\/](.|\n)*?>/g);if(sf.base.isNullOrUndefined(e))return t;for(var i=this.isRTLText(t),r=0;r<e.length;r++)this.isValidHTMLElement(e[r].replace("<","").replace("/","").replace(">","").trim())&&(t=t.replace(e[r],i?"‎":""));return t},i.prototype.isValidHTMLElement=function(t){return"[object HTMLUnknownElement]"!==document.createElement(t).toString()},i.prototype.isRTLText=function(t){return/[\u0590-\u07FF\u200F\u202B\u202E\uFB1D-\uFDFD\uFE70-\uFEFC]/.test(t)},i.prototype.createTemplate=function(t,e){var i={cancel:!1,name:"tooltipRender",tooltip:this};this.trigger("tooltipRender",i);var r=document.getElementById(this.element.id);this.isCanvas&&this.removeSVG();var o=r.firstElementChild;if(o&&sf.base.remove(o),i.cancel)sf.base.remove(u(this.element.id+"_tooltip"));else{var s=sf.base.createElement("div",{id:this.element.id+"parent_template"}),n=void 0;if("Chart"===this.controlName&&this.shared)for(var a=0;a<this.data.length;a++){var l=this.templateFn(this.data[a],this.controlInstance,s.id,s.id+"_blazorTemplate","");0===a?n=l:l.length>1?n[a].outerHTML=l[a].outerHTML||l[a].textContent:n[n.length-1].outerHTML+=l[0].outerHTML}else n=this.templateFn(this.data,this.controlInstance,s.id,s.id+"_blazorTemplate","");for(;n&&n.length>0;)sf.base.isBlazor()||1===n.length?(s.appendChild(n[0]),n=null):s.appendChild(n[0]);r.appendChild(s);var h=this.isCanvas?s:this.element,p=h.getBoundingClientRect();this.padding=0,this.elementSize=new d(p.width,p.height);var c=this.shared?this.sharedTooltipLocation(t,this.location.x,this.location.y):this.tooltipLocation(t,e,new F(0,0),new F(0,0));if(!this.enableAnimation||this.isFirst||this.crosshair?this.updateDiv(h,c.x,c.y):this.animateTooltipDiv(this.element,c),this.blazorTemplate){var f=function(){var i=u(y.element.id).getBoundingClientRect();y.elementSize=new d(i.width,i.height);var r=y.tooltipLocation(t,e,new F(0,0),new F(0,0));y.updateDiv(u(y.element.id),r.x,r.y)},y=this;f.bind(y,t,e),sf.base.updateBlazorTemplate(this.element.id+"parent_template_blazorTemplate",this.blazorTemplate.name,this.blazorTemplate.parent,void 0,f)}}},i.prototype.sharedTooltipLocation=function(t,e,i){var r=this.elementSize.width+2*this.marginX,o=this.elementSize.height+2*this.marginY,s=new p(e+2*this.padding,i-o-this.padding,r,o);return s.y<t.y&&(s.y+=s.height+2*this.padding),s.y+s.height>t.y+t.height&&(s.y=Math.max(t.y+t.height-(s.height+2*this.padding),t.y)),s.x+s.width>t.x+t.width&&(s.x=t.x+this.location.x-(s.width+4*this.padding)),s.x<t.x&&(s.x=t.x),s},i.prototype.getCurrentPosition=function(t,e,i,r){var o=this.tooltipPlacement,s=this.clipBounds.x,n=this.clipBounds.y,a=this.offset,l=this.elementSize.width+2*this.marginX,h=this.elementSize.height+2*this.marginY,d=new F(e.x,e.y);return"Top"===o||"Bottom"===o?(d=new F(d.x+s-this.elementSize.width/2-this.padding,d.y+n-this.elementSize.height-2*this.padding-this.arrowPadding-a),i.x=r.x=l/2,"Bottom"===o&&(d.y=e.y+n+a),t.x+t.width<d.x+l?(d.x=t.width>l?t.x+t.width-l+6:t.x,i.x=r.x=t.width>l?t.x+e.x-d.x:e.x):t.x>d.x&&(d.x=t.x,i.x=r.x=e.x)):(d=new F(d.x+s+a,d.y+n-this.elementSize.height/2-this.padding),i.y=r.y=h/2,"Left"===o&&(d.x=e.x+s-a-(l+this.arrowPadding)),t.y+t.height<d.y+h?(d.y=t.height>h?t.y+t.height-h+6:t.y,i.y=r.y=t.height>h?t.y+e.y-d.y:e.y):t.y>d.y&&(d.y=t.y,i.y=r.y=e.y)),new p(d.x,d.y,l,h)},i.prototype.tooltipLocation=function(t,e,i,r){if(!sf.base.isNullOrUndefined(this.tooltipPlacement))return this.getCurrentPosition(t,e,i,r);var o=new F(e.x,e.y),s=this.elementSize.width+2*this.marginX,n=this.elementSize.height+2*this.marginY,a=this.offset,h=this.clipBounds.x,d=this.clipBounds.y,c=t.x,f=t.y;return this.outOfBounds=!1,this.inverted?(o=new F(o.x+h+a,o.y+d-this.elementSize.height/2-this.padding),i.y=r.y=n/2,(o.x+s+this.arrowPadding>c+t.width||this.isNegative)&&(o.x=(e.x>t.width+t.x?t.width:e.x)+h-a-(s+this.arrowPadding)),o.x<c&&(o.x=(e.x<0?0:e.x)+h+a),o.x+s+this.arrowPadding>c+t.width&&(o.x=(e.x>t.width+t.x?t.width:e.x)+h-a-(s+this.arrowPadding)),o.y<=f&&(i.y-=f-o.y,r.y-=f-o.y,o.y=f),o.y+n>=f+t.height&&(i.y+=o.y+n-(f+t.height),r.y+=o.y+n-(f+t.height),o.y-=o.y+n-(f+t.height)),i.y+this.arrowPadding>n-this.ry&&(i.y=n-this.ry-this.arrowPadding,r.y=n),i.y-this.arrowPadding<this.ry&&(i.y=r.y=this.ry+this.arrowPadding),"Chart"===this.controlName&&((o.y+i.y<this.areaMargin+this.arrowPadding||t.y+t.height-(o.y+i.y)<this.areaMargin+this.arrowPadding)&&(this.outOfBounds=!0),l(o.x,o.y,t)&&!this.outOfBounds||(this.inverted=!this.inverted,o=new F(e.x+h-this.padding-this.elementSize.width/2,e.y+d-this.elementSize.height-2*this.padding-a-this.arrowPadding),this.revert=!0,r.x=i.x=s/2,r.y=i.y=0,(o.y<f||this.isNegative)&&(o.y=(e.y<0?0:e.y)+a+d),o.y+this.arrowPadding+n>f+t.height&&(o.y=Math.min(e.y,f+t.height)+d-this.elementSize.height-2*this.padding-a-this.arrowPadding),r.x=s/2,o.x<c&&(r.x-=c-o.x,i.x-=c-o.x,o.x=c),o.x+s>t.width+c&&(i.x+=o.x+s-(t.width+c),r.x+=o.x+s-(t.width+c),o.x-=o.x+s-(t.width+c)),this.arrowPadding+i.x>s-this.rx&&(r.x=s-this.rx-this.arrowPadding,i.x=s-this.rx-this.arrowPadding),i.x-this.arrowPadding<this.rx&&(i.x=r.x=this.rx+this.arrowPadding)))):(o=new F(o.x+h-this.elementSize.width/2-this.padding,o.y+d-this.elementSize.height-2*(this.allowHighlight?this.highlightPadding:this.padding)-this.arrowPadding-a),i.x=r.x=s/2,(o.y<f||this.isNegative)&&"Progressbar"!==this.controlName&&(o.y=(e.y<0?0:e.y)+d+a),o.y+n+this.arrowPadding>f+t.height&&(o.y=Math.min(e.y,f+t.height)+d-this.elementSize.height-2*this.padding-this.arrowPadding-a),(o.x+s>c+t.width&&o.y<f||this.isNegative)&&"Progressbar"!==this.controlName&&(o.y=(e.y<0?0:e.y)+d+a),r.x=s/2,o.x<c&&"Progressbar"!==this.controlName&&(i.x-=c-o.x,r.x-=c-o.x,o.x=c),o.x+s>c+t.width&&"Progressbar"!==this.controlName&&(i.x+=o.x+s-(c+t.width),r.x+=o.x+s-(c+t.width),o.x-=o.x+s-(c+t.width)),o.x<c&&"Progressbar"!==this.controlName&&(i.x-=c-o.x,r.x-=c-o.x,o.x=c),i.x+this.arrowPadding>s-this.rx&&(i.x=s-this.rx-this.arrowPadding,r.x=s-this.rx-this.arrowPadding),i.x-this.arrowPadding<this.rx&&(i.x=r.x=this.rx+this.arrowPadding),"Chart"===this.controlName&&((t.x+t.width-(o.x+i.x)<this.areaMargin+this.arrowPadding||o.x+i.x<this.areaMargin+this.arrowPadding)&&(this.outOfBounds=!0),this.template&&o.y<0&&(o.y=e.y+d+a),l(o.x,o.y,t)&&!this.outOfBounds||(this.inverted=!this.inverted,this.revert=!0,o=new F(e.x+a+h,e.y+d-this.elementSize.height/2-this.padding),r.x=i.x=0,r.y=i.y=n/2,(o.x+this.arrowPadding+s>c+t.width||this.isNegative)&&(o.x=(e.x>c+t.width?t.width:e.x)+h-a-(this.arrowPadding+s)),o.x<c&&(o.x=(e.x<0?0:e.x)+a+h),o.y<=f&&(r.y-=f-o.y,i.y-=f-o.y,o.y=f),o.y+n>=t.height+f&&(i.y+=o.y+n-(t.height+f),r.y+=o.y+n-(t.height+f),o.y-=o.y+n-(t.height+f)),this.arrowPadding+i.y>n-this.ry&&(i.y=n-this.arrowPadding-this.ry,r.y=n),i.y-this.arrowPadding<this.ry&&(i.y=this.arrowPadding+this.ry,r.y=0)))),new p(o.x,o.y,s,n)},i.prototype.animateTooltipDiv=function(t,e){var i,r=this,o=parseFloat(t.style.left),s=parseFloat(t.style.top);new sf.base.Animation({}).animate(t,{duration:0===this.duration&&"Enable"===sf.base.animationMode?300:this.duration,progress:function(n){i=n.timeStamp/n.duration,t.style.animation=null,"Chart"===r.controlName&&r.shared&&!r.enableRTL?(t.style.transition=sf.base.isBlazor()?"transform 0.3s":"transform 0.1s",t.style.transform="translate("+(o+i*(e.x-o))+"px,"+(s+i*(e.y-s))+"px)",t.style.left="",t.style.top=""):(t.style.left=o+i*(e.x-o)+"px",t.style.top=s+i*(e.y-s)+"px",t.style.transform="RangeNavigator"===r.controlName?t.style.transform:"")},end:function(i){r.updateDiv(t,e.x,e.y),r.trigger("animationComplete",{tooltip:r})}})},i.prototype.updateDiv=function(t,e,i){"Chart"!==this.controlName||!this.shared||this.crosshair||this.enableRTL?(t.style.left=e+"px",t.style.top=i+"px",t.style.transform="RangeNavigator"===this.controlName?t.style.transform:""):(t.style.transform="translate("+e+"px,"+i+"px)",t.style.left="",t.style.top="")},i.prototype.updateTemplateFn=function(){if(this.template)try{"function"!=typeof this.template&&document.querySelectorAll(this.template).length?this.templateFn=sf.base.compile(document.querySelector(this.template).innerHTML.trim()):this.templateFn=sf.base.compile(this.template)}catch(t){this.templateFn=sf.base.compile(this.template)}},i.prototype.fadeOut=function(){var t=this,e=this.isCanvas&&!this.template?u(this.element.id+"_svg"):u(this.element.id),i=u(this.element.id);if(e){var r=e.firstChild;if(r.nodeType!==Node.ELEMENT_NODE&&(r=e.firstElementChild),this.isCanvas&&!this.template&&(r=document.getElementById(this.element.id+"_group")?document.getElementById(this.element.id+"_group"):r),!r)return null;var o=parseFloat(r.getAttribute("opacity"));o=sf.base.isNullOrUndefined(o)?1:o,new sf.base.Animation({}).animate(r,{duration:200,progress:function(e){t.progressAnimation(r,o,e.timeStamp/e.duration)},end:function(){t.fadeOuted=!0,t.endAnimation(r),i.style.transition=""}})}},i.prototype.progressAnimation=function(t,e,i){t.style.animation="",t.setAttribute("opacity",(e-i).toString())},i.prototype.endAnimation=function(t){t.setAttribute("opacity","0"),this.template&&(t.style.display="none"),this.trigger("animationComplete",{tooltip:this})},i.prototype.getPersistData=function(){return this.addOnPersist([])},i.prototype.getModuleName=function(){return"tooltip"},i.prototype.destroy=function(){t.prototype.destroy.call(this),this.element.classList.remove("e-tooltip")},i.prototype.onPropertyChanged=function(t,e){this.blazorTemplate&&sf.base.resetBlazorTemplate(this.element.id+"parent_template_blazorTemplate"),this.isFirst=!1,this.render()},L([sf.base.Property(!1)],i.prototype,"enable",void 0),L([sf.base.Property(!1)],i.prototype,"shared",void 0),L([sf.base.Property(!1)],i.prototype,"crosshair",void 0),L([sf.base.Property(!1)],i.prototype,"enableShadow",void 0),L([sf.base.Property(null)],i.prototype,"fill",void 0),L([sf.base.Property("")],i.prototype,"header",void 0),L([sf.base.Property(.75)],i.prototype,"opacity",void 0),L([sf.base.Complex({size:"12px",fontWeight:null,color:null,fontStyle:"Normal",fontFamily:null},P)],i.prototype,"textStyle",void 0),L([sf.base.Property(null)],i.prototype,"template",void 0),L([sf.base.Property(!0)],i.prototype,"enableAnimation",void 0),L([sf.base.Property(300)],i.prototype,"duration",void 0),L([sf.base.Property(!1)],i.prototype,"inverted",void 0),L([sf.base.Property(!1)],i.prototype,"isNegative",void 0),L([sf.base.Complex({color:null,width:null},T)],i.prototype,"border",void 0),L([sf.base.Property([])],i.prototype,"content",void 0),L([sf.base.Property(10)],i.prototype,"markerSize",void 0),L([sf.base.Complex({x:0,y:0},O)],i.prototype,"clipBounds",void 0),L([sf.base.Property([])],i.prototype,"palette",void 0),L([sf.base.Property([])],i.prototype,"shapes",void 0),L([sf.base.Complex({x:0,y:0},O)],i.prototype,"location",void 0),L([sf.base.Property(0)],i.prototype,"offset",void 0),L([sf.base.Property(4)],i.prototype,"rx",void 0),L([sf.base.Property(4)],i.prototype,"ry",void 0),L([sf.base.Property(5)],i.prototype,"marginX",void 0),L([sf.base.Property(5)],i.prototype,"marginY",void 0),L([sf.base.Property(7)],i.prototype,"arrowPadding",void 0),L([sf.base.Property(null)],i.prototype,"data",void 0),L([sf.base.Property("Material")],i.prototype,"theme",void 0),L([sf.base.Complex({x:0,y:0,width:0,height:0},A)],i.prototype,"areaBounds",void 0),L([sf.base.Property(null)],i.prototype,"availableSize",void 0),L([sf.base.Property()],i.prototype,"blazorTemplate",void 0),L([sf.base.Property(!1)],i.prototype,"isCanvas",void 0),L([sf.base.Property(!1)],i.prototype,"isTextWrap",void 0),L([sf.base.Property(!1)],i.prototype,"isFixed",void 0),L([sf.base.Property(null)],i.prototype,"tooltipPlacement",void 0),L([sf.base.Property(null)],i.prototype,"controlInstance",void 0),L([sf.base.Property("")],i.prototype,"controlName",void 0),L([sf.base.Event()],i.prototype,"tooltipRender",void 0),L([sf.base.Event()],i.prototype,"loaded",void 0),L([sf.base.Event()],i.prototype,"animationComplete",void 0),L([sf.base.Property(!1)],i.prototype,"enableRTL",void 0),L([sf.base.Property(!1)],i.prototype,"allowHighlight",void 0),i=L([sf.base.NotifyPropertyChanges],i)}(sf.base.Component);return t.AreaBounds=A,t.CanvasRenderer=r,t.CustomizeOption=f,t.PathOption=b,t.Rect=p,t.Side=c,t.Size=d,t.SvgRenderer=e,t.TextOption=y,t.TextStyle=P,t.ToolLocation=O,t.Tooltip=k,t.TooltipBorder=T,t.TooltipLocation=F,t.calculateShapes=x,t.drawSymbol=m,t.findDirection=h,t.getElement=u,t.getTooltipThemeColor=o,t.measureText=a,t.removeElement=g,t.textElement=v,t.withInAreaBounds=l,t},sf.svgbase=sf.svgbase({})}}]);(async()=>{await import(`${document.baseURI}_content/Syncfusion.Blazor/scripts/syncfusion-blazor-base.min.js?v=26.1.undefined`).then(()=>{})})();