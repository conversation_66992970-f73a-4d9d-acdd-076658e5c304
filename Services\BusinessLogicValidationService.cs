using Intra2025.Data;
using Intra2025.Models;
using Intra2025.Models.YouthCareReportService;
using Microsoft.EntityFrameworkCore;

namespace Intra2025.Services
{
    /// <summary>
    /// 業務邏輯驗證服務，確保所有業務操作符合規則
    /// </summary>
    public class BusinessLogicValidationService
    {
        private readonly YCRSDbContext _context;
        private readonly BusinessRulesConfigService _config;

        public BusinessLogicValidationService(YCRSDbContext context, BusinessRulesConfigService config)
        {
            _context = context;
            _config = config;
        }

        /// <summary>
        /// 驗證狀態變更操作的業務邏輯
        /// </summary>
        public async Task<ValidationResult> ValidateStatusChangeAsync<T>(T record, string userId, bool isAdmin, string userUnitCode) 
            where T : class
        {
            var result = new ValidationResult();

            try
            {
                // 檢查記錄是否存在
                if (record == null)
                {
                    result.IsValid = false;
                    result.ErrorMessage = "記錄不存在，無法變更狀態。";
                    return result;
                }

                // 權限檢查
                var caseBelongId = GetCaseBelongId(record);
                if (!isAdmin && caseBelongId != userUnitCode)
                {
                    result.IsValid = false;
                    result.ErrorMessage = "您沒有權限變更此記錄的狀態。";
                    return result;
                }

                // 檢查記錄是否已被其他使用者修改
                var currentRecord = await GetCurrentRecordFromDatabase(record);
                if (currentRecord == null)
                {
                    result.IsValid = false;
                    result.ErrorMessage = "記錄已被刪除，無法變更狀態。";
                    return result;
                }

                // 檢查並發修改
                if (!CheckConcurrencyToken(record, currentRecord))
                {
                    result.IsValid = false;
                    result.ErrorMessage = "記錄已被其他使用者修改，請重新載入後再試。";
                    return result;
                }

                // 業務規則檢查
                var businessRuleResult = ValidateStatusChangeBusinessRules(record, currentRecord);
                if (!businessRuleResult.IsValid)
                {
                    return businessRuleResult;
                }

                result.IsValid = true;
                return result;
            }
            catch (Exception ex)
            {
                result.IsValid = false;
                result.ErrorMessage = "驗證過程中發生錯誤，請稍後再試。";
                result.Exception = ex;
                return result;
            }
        }

        /// <summary>
        /// 驗證刪除操作的業務邏輯
        /// </summary>
        public async Task<ValidationResult> ValidateDeleteAsync<T>(string recordId, string userId, bool isAdmin, string userUnitCode) 
            where T : class
        {
            var result = new ValidationResult();

            try
            {
                // 檢查記錄是否存在
                var record = await GetRecordByIdAsync<T>(recordId);
                if (record == null)
                {
                    result.IsValid = false;
                    result.ErrorMessage = "記錄不存在，無法刪除。";
                    return result;
                }

                // 權限檢查
                var caseBelongId = GetCaseBelongId(record);
                if (!isAdmin && caseBelongId != userUnitCode)
                {
                    result.IsValid = false;
                    result.ErrorMessage = "您沒有權限刪除此記錄。";
                    return result;
                }

                // 檢查是否已確認（根據配置決定是否可以刪除已確認的記錄）
                var isChecked = GetIsCheckedStatus(record);
                if (isChecked && !_config.AllowDeleteConfirmedRecords)
                {
                    result.IsValid = false;
                    result.ErrorMessage = "已確認的記錄無法刪除。";
                    return result;
                }

                // 檢查是否有相關聯的資料（根據配置決定是否檢查）
                if (_config.CheckRelatedDataBeforeDelete)
                {
                    var hasRelatedData = await CheckRelatedDataAsync(recordId);
                    if (hasRelatedData)
                    {
                        result.IsValid = false;
                        result.ErrorMessage = "此記錄有相關聯的資料，無法刪除。請先處理相關資料。";
                        return result;
                    }
                }

                result.IsValid = true;
                return result;
            }
            catch (Exception ex)
            {
                result.IsValid = false;
                result.ErrorMessage = "驗證過程中發生錯誤，請稍後再試。";
                result.Exception = ex;
                return result;
            }
        }

        /// <summary>
        /// 驗證編輯操作的業務邏輯
        /// </summary>
        public ValidationResult ValidateEditAccess<T>(T record, string userId, bool isAdmin, string userUnitCode) 
            where T : class
        {
            var result = new ValidationResult();

            try
            {
                if (record == null)
                {
                    result.IsValid = false;
                    result.ErrorMessage = "記錄不存在，無法編輯。";
                    return result;
                }

                // 權限檢查
                var caseBelongId = GetCaseBelongId(record);
                if (!isAdmin && caseBelongId != userUnitCode)
                {
                    result.IsValid = false;
                    result.ErrorMessage = "您沒有權限編輯此記錄。";
                    return result;
                }

                // 檢查是否已確認（已確認的記錄不能編輯）
                var isChecked = GetIsCheckedStatus(record);
                if (isChecked)
                {
                    result.IsValid = false;
                    result.ErrorMessage = "已確認的記錄無法編輯。";
                    return result;
                }

                result.IsValid = true;
                return result;
            }
            catch (Exception ex)
            {
                result.IsValid = false;
                result.ErrorMessage = "驗證過程中發生錯誤，請稍後再試。";
                result.Exception = ex;
                return result;
            }
        }

        #region Private Helper Methods

        private string? GetCaseBelongId<T>(T record) where T : class
        {
            var property = typeof(T).GetProperty("CaseBelongId");
            return property?.GetValue(record)?.ToString();
        }

        private bool GetIsCheckedStatus<T>(T record) where T : class
        {
            var property = typeof(T).GetProperty("IsChecked");
            return property?.GetValue(record) as bool? ?? false;
        }

        private async Task<T?> GetRecordByIdAsync<T>(string id) where T : class
        {
            if (typeof(T) == typeof(YCRS_ChildRecord))
            {
                return await _context.YCRS_ChildRecord.FindAsync(id) as T;
            }
            else if (typeof(T) == typeof(YCRS_CareRecord))
            {
                return await _context.YCRS_CareRecord.FindAsync(id) as T;
            }
            
            return null;
        }

        private async Task<T?> GetCurrentRecordFromDatabase<T>(T record) where T : class
        {
            var idProperty = typeof(T).GetProperty("Id");
            var id = idProperty?.GetValue(record)?.ToString();
            
            if (string.IsNullOrEmpty(id))
                return null;

            return await GetRecordByIdAsync<T>(id);
        }

        private bool CheckConcurrencyToken<T>(T originalRecord, T currentRecord) where T : class
        {
            // 簡單的並發檢查，可以根據需要擴展
            // 這裡可以比較時間戳或版本號
            return true; // 暫時返回 true，實際實作需要根據具體需求
        }

        private ValidationResult ValidateStatusChangeBusinessRules<T>(T record, T currentRecord) where T : class
        {
            var result = new ValidationResult { IsValid = true };

            // 檢查當前狀態是否允許變更
            var currentIsChecked = GetIsCheckedStatus(currentRecord);
            var newIsChecked = GetIsCheckedStatus(record);

            // 業務規則 1: 檢查記錄的時效性
            var createDate = GetCreateDate(currentRecord);
            if (createDate.HasValue && _config.MaxEditableDays > 0)
            {
                var daysSinceCreation = (DateTime.Now - createDate.Value).Days;

                // 如果記錄超過設定天數，需要特殊處理
                if (daysSinceCreation > _config.MaxEditableDays)
                {
                    // 這個檢查會在上層的權限檢查中處理
                    // 這裡只是記錄，不阻止操作
                }
            }

            // 業務規則 2: 狀態變更的合理性檢查
            if (currentIsChecked && !newIsChecked)
            {
                // 檢查是否允許取消確認
                if (!_config.AllowUnconfirmAfterConfirm)
                {
                    result.IsValid = false;
                    result.ErrorMessage = "已確認的狀態無法取消。";
                    return result;
                }

                // 如果配置要求只有管理員可以取消確認，這裡會在上層權限檢查中處理
            }

            // 業務規則 3: 檢查是否有必要的附件
            if (newIsChecked)
            {
                // 確認狀態時，應該確保有必要的文件
                // 這個檢查可以根據具體業務需求實作
            }

            return result;
        }

        private DateTime? GetCreateDate<T>(T record) where T : class
        {
            var property = typeof(T).GetProperty("CreateDate");
            return property?.GetValue(record) as DateTime?;
        }

        private async Task<bool> CheckRelatedDataAsync(string recordId)
        {
            // 檢查是否有相關的檔案記錄
            var hasFiles = await _context.YCRS_Files.AnyAsync(f => f.Id == recordId);
            
            // 可以添加更多相關資料的檢查
            
            return hasFiles;
        }

        #endregion
    }

    /// <summary>
    /// 驗證結果類別
    /// </summary>
    public class ValidationResult
    {
        public bool IsValid { get; set; }
        public string ErrorMessage { get; set; } = string.Empty;
        public Exception? Exception { get; set; }
    }
}
