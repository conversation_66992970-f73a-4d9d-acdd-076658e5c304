-- 修復 YCRS_Files 表的 Sno 欄位為自動遞增
-- 這個腳本需要在 YCRS 資料庫中執行

USE [YMDB]
GO

-- 檢查 YCRS_Files 表是否存在
IF EXISTS (SELECT * FROM sys.tables WHERE name = 'YCRS_Files')
BEGIN
    PRINT 'YCRS_Files 表存在，開始檢查 Sno 欄位設定...'
    
    -- 檢查 Sno 欄位是否為 IDENTITY
    IF EXISTS (
        SELECT * FROM sys.columns c
        INNER JOIN sys.tables t ON c.object_id = t.object_id
        WHERE t.name = 'YCRS_Files' 
        AND c.name = 'Sno' 
        AND c.is_identity = 1
    )
    BEGIN
        PRINT 'Sno 欄位已經是 IDENTITY，無需修改。'
    END
    ELSE
    BEGIN
        PRINT '開始修復 Sno 欄位為 IDENTITY...'
        
        -- 備份現有資料
        IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'YCRS_Files_Backup')
        BEGIN
            SELECT * INTO YCRS_Files_Backup FROM YCRS_Files
            PRINT '已備份現有資料到 YCRS_Files_Backup 表'
        END
        
        -- 如果表中有資料，需要先處理
        DECLARE @RowCount INT
        SELECT @RowCount = COUNT(*) FROM YCRS_Files
        
        IF @RowCount > 0
        BEGIN
            PRINT '表中有 ' + CAST(@RowCount AS VARCHAR(10)) + ' 筆資料，需要重建表結構...'
            
            -- 創建新的表結構
            CREATE TABLE YCRS_Files_New (
                Sno int IDENTITY(1,1) PRIMARY KEY,
                Id nvarchar(max) NULL,
                Filepath nvarchar(200) NULL,
                FileName nvarchar(100) NULL,
                IsMarkedForDeletion bit NOT NULL DEFAULT 0,
                Createdate datetime2 NOT NULL
            )
            
            -- 將資料插入新表（不包含 Sno，讓它自動生成）
            SET IDENTITY_INSERT YCRS_Files_New OFF
            INSERT INTO YCRS_Files_New (Id, Filepath, FileName, IsMarkedForDeletion, Createdate)
            SELECT Id, Filepath, FileName, IsMarkedForDeletion, Createdate
            FROM YCRS_Files
            ORDER BY Sno  -- 保持原有順序
            
            -- 刪除舊表
            DROP TABLE YCRS_Files
            
            -- 重命名新表
            EXEC sp_rename 'YCRS_Files_New', 'YCRS_Files'
            
            PRINT '表結構重建完成，Sno 欄位現在是 IDENTITY。'
        END
        ELSE
        BEGIN
            PRINT '表中無資料，直接修改表結構...'
            
            -- 刪除現有的 Sno 欄位
            ALTER TABLE YCRS_Files DROP CONSTRAINT [PK__YCRS_Fil__CA1E8E3D2F10007B] -- 可能需要調整約束名稱
            ALTER TABLE YCRS_Files DROP COLUMN Sno
            
            -- 添加新的 IDENTITY Sno 欄位
            ALTER TABLE YCRS_Files ADD Sno int IDENTITY(1,1) PRIMARY KEY
            
            PRINT 'Sno 欄位已修改為 IDENTITY。'
        END
    END
END
ELSE
BEGIN
    PRINT 'YCRS_Files 表不存在，將由 Entity Framework 自動創建。'
END

-- 驗證修改結果
IF EXISTS (
    SELECT * FROM sys.columns c
    INNER JOIN sys.tables t ON c.object_id = t.object_id
    WHERE t.name = 'YCRS_Files' 
    AND c.name = 'Sno' 
    AND c.is_identity = 1
)
BEGIN
    PRINT '✓ 驗證成功：Sno 欄位現在是 IDENTITY。'
END
ELSE
BEGIN
    PRINT '✗ 驗證失敗：Sno 欄位仍然不是 IDENTITY。'
END

PRINT '腳本執行完成。'
