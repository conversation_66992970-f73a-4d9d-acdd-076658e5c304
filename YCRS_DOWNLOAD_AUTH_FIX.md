# YCRS 檔案下載身份驗證問題修復

## 🔍 問題診斷

### 原始錯誤
```
warn: Program[0]
      Unauthorized download attempt for file: /secure-uploads/YCRS_Care/20250813085726_0d51b6df_人總8月份函.pdf
```

### 根本原因
1. **UserState 生命週期問題**：UserState 是 Scoped 服務，在新視窗（`target="_blank"`）中開啟下載連結時，會創建新的 HTTP 請求上下文
2. **身份驗證狀態丟失**：新的請求上下文沒有經過 Blazor 的 SSO 初始化流程，導致 `userState.Account` 為空
3. **下載端點依賴問題**：原始下載端點完全依賴 UserState 進行身份驗證

## 🔧 修復方案

### 方案一：修改下載端點支援 Cookie 驗證（已實施）
修改 `/api/secure-download` 端點，讓它能夠直接從 SSO Cookie 中驗證使用者身份：

**修改內容**：
- 從 `PUBLIC_APP_USER_SSO_TOKEN` Cookie 中讀取 SSO Token
- 使用 `SsoService.GetUserBasicProfileFromApi()` 直接驗證 Token
- 不再依賴 Blazor 的 UserState 服務

### 方案二：修改前端下載方式（已實施）
將 `target="_blank"` 改為使用 JavaScript fetch API：

**優點**：
- 保持當前頁面的身份驗證狀態
- 更好的錯誤處理
- 支援自定義檔案名稱

## 📋 修改的檔案

### 1. Program.cs
**修改內容**：
- 修改 `/api/secure-download` 端點的身份驗證邏輯
- 添加 `GetUserInfoFromSsoTokenAsync` 輔助方法
- 添加 `UserInfo` 類別

**關鍵變更**：
```csharp
// 直接從 Cookie 驗證使用者身份
var ssoToken = context.Request.Cookies["PUBLIC_APP_USER_SSO_TOKEN"];
if (string.IsNullOrEmpty(ssoToken))
{
    context.Response.StatusCode = 401;
    await context.Response.WriteAsync("需要身份驗證。請重新登入。");
    return;
}

// 使用 SsoService 驗證並取得使用者資訊
var userInfo = await GetUserInfoFromSsoTokenAsync(ssoService, ssoToken, logger);
```

### 2. Services/SsoService.cs
**修改內容**：
- 將 `GetUserBasicProfileFromApi` 方法從 private 改為 public
- 允許下載端點直接使用 SSO 驗證功能

### 3. Components/Pages/YCRS/CareCaseList.razor
**修改內容**：
- 移除下載連結的 `target="_blank"` 屬性
- 添加 `onclick="downloadFile(this.href); return false;"`
- 添加 `downloadFile` JavaScript 函數

### 4. Components/Pages/YCRS/CareCaseNewEdit.razor
**修改內容**：
- 同樣修改下載連結和添加 JavaScript 函數

## 🧪 測試步驟

### 1. 基本下載測試
1. 登入 YCRS 系統
2. 進入溫馨關懷表列表頁面 (`/CareCaseList`)
3. 點擊任一檔案的「下截關懷表」連結
4. 確認檔案能正常下載

### 2. 權限測試
1. 以不同權限的使用者登入
2. 嘗試下載不同單位的檔案
3. 確認權限控制正常運作

### 3. 錯誤處理測試
1. 嘗試下載不存在的檔案
2. 使用無效的檔案路徑
3. 確認錯誤訊息正確顯示

## 🔍 技術細節

### JavaScript 下載函數
```javascript
function downloadFile(url) {
    fetch(url, {
        method: 'GET',
        credentials: 'same-origin' // 確保包含 cookies
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        // 從 response headers 取得檔案名稱
        const contentDisposition = response.headers.get('Content-Disposition');
        let filename = 'download';
        if (contentDisposition) {
            const filenameMatch = contentDisposition.match(/filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/);
            if (filenameMatch && filenameMatch[1]) {
                filename = decodeURIComponent(filenameMatch[1].replace(/['"]/g, ''));
            }
        }
        
        return response.blob().then(blob => ({ blob, filename }));
    })
    .then(({ blob, filename }) => {
        // 創建下載連結
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.style.display = 'none';
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
        
        console.log("檔案下載完成：", filename);
    })
    .catch(error => {
        console.error('下載失敗：', error);
        alert('檔案下載失敗：' + error.message);
    });
}
```

### SSO Token 驗證流程
1. 從 `PUBLIC_APP_USER_SSO_TOKEN` Cookie 讀取 Token
2. 調用 `SsoService.GetUserBasicProfileFromApi(token)` 驗證
3. 解析回應中的使用者資訊
4. 檢查管理員權限
5. 傳遞給 `SecureFileDownloadService` 進行檔案下載

## ✅ 預期結果

修復後，使用者應該能夠：
- ✅ 正常點擊下載連結並成功下載檔案
- ✅ 看到適當的錯誤訊息（如果檔案不存在或無權限）
- ✅ 在瀏覽器控制台看到詳細的下載日誌
- ✅ 下載的檔案具有正確的檔案名稱

## 🔧 故障排除

### 如果仍然出現 401 錯誤
1. 檢查瀏覽器是否有 `PUBLIC_APP_USER_SSO_TOKEN` Cookie
2. 確認 SSO Token 是否有效
3. 查看伺服器日誌中的詳細錯誤訊息

### 如果下載失敗
1. 開啟瀏覽器開發者工具的 Network 標籤
2. 檢查下載請求的狀態碼和回應
3. 查看 Console 標籤中的 JavaScript 錯誤訊息

### 如果檔案名稱不正確
1. 檢查 `Content-Disposition` 標頭是否正確設定
2. 確認檔案名稱的 URL 編碼處理

## 📝 後續改進建議

1. **快取優化**：考慮快取 SSO Token 驗證結果
2. **錯誤處理**：改善前端錯誤訊息顯示
3. **進度指示**：為大檔案下載添加進度指示器
4. **安全增強**：添加下載頻率限制

修復已完成，建議立即進行測試以確認問題已解決。
