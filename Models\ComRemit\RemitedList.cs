using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Intra2025.Models.ComRemit
{
    [Table("RemitedList")]
    public class RemitedList
    {
        [Key]
        [Column("sno")]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int Sno { get; set; }

        [Column("CollectNo")]
        public string? CollectNo { get; set; }

        [Column("CollecAcc")]
        public string? CollecAcc { get; set; }

        [Column("CollecName")]
        public string? CollecName { get; set; }

        [Column("CollectId")]
        public string? CollectId { get; set; }

        [Column("RemitPrice")]
        [Range(1, int.MaxValue, ErrorMessage = "金額必須為正數")]
        public int? RemitPrice { get; set; }

        [Column("RemitMemo")]
        public string? RemitMemo { get; set; }

        [Column("ConSno")]
        public int? ConSno { get; set; }

        [Column("ConPer")]
        public string? ConPer { get; set; }

        [Column("ConUnit")]
        public string? ConUnit { get; set; }

        [Column("ConDate")]
        public DateTime? ConDate { get; set; }

        [Column("kindno")]
        public int? Kindno { get; set; }

        [Column("CashDate")]
        public DateTime? CashDate { get; set; }

        [Column("BatchNum")]
        public string? BatchNum { get; set; }

        [Column("IfFee")]
        public string? IfFee { get; set; }

        [Column("ConMemo")]
        public string? ConMemo { get; set; }
    }
}
