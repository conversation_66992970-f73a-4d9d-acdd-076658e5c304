using Microsoft.AspNetCore.Components.Forms;
using System.Security.Cryptography;
using System.Text;
using System.Text.RegularExpressions;
using Intra2025.Data;
using Microsoft.EntityFrameworkCore;

namespace Intra2025.Services
{
    /// <summary>
    /// 安全檔案上傳服務，提供完整的檔案上傳安全驗證
    /// </summary>
    public class SecureFileUploadService
    {
        private readonly IWebHostEnvironment _environment;
        private readonly ILogger<SecureFileUploadService> _logger;
        private readonly FileUploadConfigService _config;
        private readonly YCRSDbContext _ycrsContext;

        public SecureFileUploadService(
            IWebHostEnvironment environment,
            ILogger<SecureFileUploadService> logger,
            FileUploadConfigService config,
            YCRSDbContext ycrsContext)
        {
            _environment = environment;
            _logger = logger;
            _config = config;
            _ycrsContext = ycrsContext;
        }

        /// <summary>
        /// 安全上傳檔案
        /// </summary>
        public async Task<FileUploadResult> UploadFileAsync(IBrowserFile file, string category, string userId)
        {
            var result = new FileUploadResult();

            try
            {
                // 1. 基本驗證
                var basicValidation = ValidateBasicFileProperties(file);
                if (!basicValidation.IsValid)
                {
                    result.IsSuccess = false;
                    result.ErrorMessage = basicValidation.ErrorMessage;
                    return result;
                }

                // 2. 檔案類型驗證
                var typeValidation = ValidateFileType(file, category);
                if (!typeValidation.IsValid)
                {
                    result.IsSuccess = false;
                    result.ErrorMessage = typeValidation.ErrorMessage;
                    return result;
                }

                // 3. 檔案內容驗證
                var contentValidation = await ValidateFileContentAsync(file);
                if (!contentValidation.IsValid)
                {
                    result.IsSuccess = false;
                    result.ErrorMessage = contentValidation.ErrorMessage;
                    return result;
                }

                // 4. 生成安全的檔案名稱和路徑
                var secureFileName = GenerateSecureFileName(file.Name);
                var secureFilePath = GetSecureFilePath(category, secureFileName);

                // 5. 確保目錄存在
                var directory = Path.GetDirectoryName(secureFilePath);
                if (!Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory!);
                }

                // 6. 儲存檔案
                using (var fileStream = new FileStream(secureFilePath, FileMode.Create))
                {
                    await file.OpenReadStream(_config.GetMaxFileSize(category)).CopyToAsync(fileStream);
                }

                // 7. 計算檔案雜湊值
                var fileHash = await ComputeFileHashAsync(secureFilePath);

                // 8. 記錄上傳日誌
                _logger.LogInformation("File uploaded successfully: {FileName} by user {UserId}", 
                    secureFileName, userId);

                result.IsSuccess = true;
                result.FileName = secureFileName;
                result.FilePath = GetRelativeFilePath(category, secureFileName);
                result.FileHash = fileHash;
                result.FileSize = file.Size;

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error uploading file: {FileName} by user {UserId}", file.Name, userId);
                result.IsSuccess = false;
                result.ErrorMessage = "檔案上傳過程中發生錯誤，請稍後再試。";
                return result;
            }
        }

        /// <summary>
        /// 驗證基本檔案屬性
        /// </summary>
        private ValidationResult ValidateBasicFileProperties(IBrowserFile file)
        {
            var result = new ValidationResult { IsValid = true };

            // 檢查檔案是否為空
            if (file == null)
            {
                result.IsValid = false;
                result.ErrorMessage = "未選擇檔案。";
                return result;
            }

            // 檢查檔案名稱
            if (string.IsNullOrWhiteSpace(file.Name))
            {
                result.IsValid = false;
                result.ErrorMessage = "檔案名稱不能為空。";
                return result;
            }

            // 檢查檔案名稱長度
            if (file.Name.Length > _config.MaxFileNameLength)
            {
                result.IsValid = false;
                result.ErrorMessage = $"檔案名稱過長，最多允許 {_config.MaxFileNameLength} 個字元。";
                return result;
            }

            // 檢查檔案名稱是否包含危險字元
            if (ContainsDangerousCharacters(file.Name))
            {
                result.IsValid = false;
                result.ErrorMessage = "檔案名稱包含不允許的字元。";
                return result;
            }

            // 檢查檔案大小
            if (file.Size <= 0)
            {
                result.IsValid = false;
                result.ErrorMessage = "檔案大小無效。";
                return result;
            }

            return result;
        }

        /// <summary>
        /// 驗證檔案類型
        /// </summary>
        private ValidationResult ValidateFileType(IBrowserFile file, string category)
        {
            var result = new ValidationResult { IsValid = true };

            var extension = Path.GetExtension(file.Name).ToLowerInvariant();
            var allowedExtensions = _config.GetAllowedExtensions(category);

            // 檢查副檔名
            if (!allowedExtensions.Contains(extension))
            {
                result.IsValid = false;
                result.ErrorMessage = $"不支援的檔案格式。允許的格式：{string.Join(", ", allowedExtensions)}";
                return result;
            }

            // 檢查檔案大小
            var maxSize = _config.GetMaxFileSize(category);
            if (file.Size > maxSize)
            {
                result.IsValid = false;
                result.ErrorMessage = $"檔案大小超過限制。最大允許：{maxSize / 1024 / 1024} MB";
                return result;
            }

            // 檢查 MIME 類型
            var allowedMimeTypes = _config.GetAllowedMimeTypes(category);
            if (allowedMimeTypes.Any() && !allowedMimeTypes.Contains(file.ContentType))
            {
                result.IsValid = false;
                result.ErrorMessage = "檔案類型不符合安全要求。";
                return result;
            }

            return result;
        }

        /// <summary>
        /// 驗證檔案內容
        /// </summary>
        private async Task<ValidationResult> ValidateFileContentAsync(IBrowserFile file)
        {
            var result = new ValidationResult { IsValid = true };

            try
            {
                using var stream = file.OpenReadStream(_config.MaxScanSize);
                var buffer = new byte[Math.Min(1024, (int)file.Size)];
                await stream.ReadAsync(buffer, 0, buffer.Length);

                // 檢查檔案簽名（Magic Numbers）
                if (!IsValidFileSignature(buffer, Path.GetExtension(file.Name).ToLowerInvariant()))
                {
                    result.IsValid = false;
                    result.ErrorMessage = "檔案內容與副檔名不符，可能是偽造的檔案。";
                    return result;
                }

                // 檢查是否包含可執行內容
                if (ContainsExecutableContent(buffer))
                {
                    result.IsValid = false;
                    result.ErrorMessage = "檔案包含可執行內容，不允許上傳。";
                    return result;
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error validating file content for file: {FileName}", file.Name);
                result.IsValid = false;
                result.ErrorMessage = "檔案內容驗證失敗。";
                return result;
            }
        }

        /// <summary>
        /// 生成安全的檔案名稱
        /// </summary>
        private string GenerateSecureFileName(string originalFileName)
        {
            var extension = Path.GetExtension(originalFileName);
            var nameWithoutExtension = Path.GetFileNameWithoutExtension(originalFileName);
            
            // 清理檔案名稱
            var cleanName = Regex.Replace(nameWithoutExtension, @"[^\w\-_\.]", "_");
            
            // 限制長度
            if (cleanName.Length > 50)
            {
                cleanName = cleanName.Substring(0, 50);
            }
            
            // 添加時間戳和 GUID 確保唯一性
            var timestamp = DateTime.Now.ToString("yyyyMMddHHmmss");
            var uniqueId = Guid.NewGuid().ToString("N")[..8];
            
            return $"{timestamp}_{uniqueId}_{cleanName}{extension}";
        }

        /// <summary>
        /// 取得安全的檔案路徑（非 Web 可存取）
        /// </summary>
        private string GetSecureFilePath(string category, string fileName)
        {
            var secureUploadPath = Path.Combine(_environment.ContentRootPath, "App_Data", "SecureUploads", category);
            return Path.Combine(secureUploadPath, fileName);
        }

        /// <summary>
        /// 取得相對檔案路徑（用於資料庫儲存）
        /// </summary>
        private string GetRelativeFilePath(string category, string fileName)
        {
            return $"/secure-uploads/{category}/{fileName}";
        }

        /// <summary>
        /// 檢查檔案名稱是否包含危險字元
        /// </summary>
        private bool ContainsDangerousCharacters(string fileName)
        {
            var dangerousPatterns = new[]
            {
                @"\.\.[\\/]", // 路徑遍歷
                @"[<>:""|?*]", // Windows 不允許的字元
                @"^(CON|PRN|AUX|NUL|COM[1-9]|LPT[1-9])(\.|$)", // Windows 保留名稱
                @"^\.", // 隱藏檔案
                @"\.$" // 以點結尾
            };

            return dangerousPatterns.Any(pattern => Regex.IsMatch(fileName, pattern, RegexOptions.IgnoreCase));
        }

        /// <summary>
        /// 驗證檔案簽名
        /// </summary>
        private bool IsValidFileSignature(byte[] fileBytes, string extension)
        {
            if (fileBytes.Length < 4) return false;

            var signatures = new Dictionary<string, byte[][]>
            {
                { ".pdf", new[] { new byte[] { 0x25, 0x50, 0x44, 0x46 } } }, // %PDF
                { ".zip", new[] { new byte[] { 0x50, 0x4B, 0x03, 0x04 }, new byte[] { 0x50, 0x4B, 0x05, 0x06 } } }, // PK
                { ".7z", new[] { new byte[] { 0x37, 0x7A, 0xBC, 0xAF, 0x27, 0x1C } } }, // 7z
                { ".odt", new[] { new byte[] { 0x50, 0x4B, 0x03, 0x04 } } }, // PK (OpenDocument)
                { ".ods", new[] { new byte[] { 0x50, 0x4B, 0x03, 0x04 } } }, // PK (OpenDocument)
                { ".odp", new[] { new byte[] { 0x50, 0x4B, 0x03, 0x04 } } }  // PK (OpenDocument)
            };

            if (!signatures.ContainsKey(extension))
                return false;

            return signatures[extension].Any(signature => 
                fileBytes.Take(signature.Length).SequenceEqual(signature));
        }

        /// <summary>
        /// 檢查是否包含可執行內容
        /// </summary>
        private bool ContainsExecutableContent(byte[] fileBytes)
        {
            // 檢查 PE 檔案簽名 (Windows 可執行檔)
            if (fileBytes.Length >= 2 && fileBytes[0] == 0x4D && fileBytes[1] == 0x5A) // MZ
                return true;

            // 檢查 ELF 檔案簽名 (Linux 可執行檔)
            if (fileBytes.Length >= 4 && fileBytes[0] == 0x7F && fileBytes[1] == 0x45 && 
                fileBytes[2] == 0x4C && fileBytes[3] == 0x46) // ELF
                return true;

            // 檢查腳本內容
            var content = Encoding.UTF8.GetString(fileBytes);
            var scriptPatterns = new[]
            {
                @"<script[^>]*>",
                @"javascript:",
                @"vbscript:",
                @"on\w+\s*=",
                @"eval\s*\(",
                @"exec\s*\("
            };

            return scriptPatterns.Any(pattern => 
                Regex.IsMatch(content, pattern, RegexOptions.IgnoreCase));
        }

        /// <summary>
        /// 計算檔案雜湊值
        /// </summary>
        private async Task<string> ComputeFileHashAsync(string filePath)
        {
            using var sha256 = SHA256.Create();
            using var stream = File.OpenRead(filePath);
            var hash = await sha256.ComputeHashAsync(stream);
            return Convert.ToHexString(hash);
        }

        /// <summary>
        /// 創建 YCRS_Files 記錄並儲存到資料庫
        /// </summary>
        public async Task<bool> CreateYCRSFileRecordAsync(string recordId, string originalFileName, string filePath)
        {
            try
            {
                // 取得下一個 Sno 值
                var maxSno = await _ycrsContext.YCRS_Files.MaxAsync(f => (int?)f.Sno) ?? 0;

                var fileRecord = new Models.YCRS_Files
                {
                    Sno = maxSno + 1,
                    Id = recordId,
                    FileName = originalFileName,
                    Filepath = filePath,
                    Createdate = DateTime.Now
                };

                _ycrsContext.YCRS_Files.Add(fileRecord);
                await _ycrsContext.SaveChangesAsync();

                _logger.LogInformation("YCRS 檔案記錄已創建: {FileName} for record {RecordId}", originalFileName, recordId);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "創建 YCRS 檔案記錄失敗: {FileName} for record {RecordId}", originalFileName, recordId);
                return false;
            }
        }
    }

    /// <summary>
    /// 檔案上傳結果
    /// </summary>
    public class FileUploadResult
    {
        public bool IsSuccess { get; set; }
        public string ErrorMessage { get; set; } = string.Empty;
        public string FileName { get; set; } = string.Empty;
        public string FilePath { get; set; } = string.Empty;
        public string FileHash { get; set; } = string.Empty;
        public long FileSize { get; set; }
    }
}
