@page "/ComRemit/AdminList"
@rendermode @(new InteractiveServerRenderMode(prerender: false))
@inject ComRemitedListService RemitedListService
@inject ComRemitQRCodeService BarcodeService
@inject IJSRuntime JSRuntime
@using Intra2025.Services
@using Intra2025.Models.ComRemit
@using Intra2025.Components.Base
@using Microsoft.AspNetCore.Components.Web

@inherits BasePageComponent

<PageTitle>管理者彙整編號查詢</PageTitle>

<!-- 樣式定義 -->
<style>
    .admin-page {
        width: 100%;
        margin: 0;
        padding: 20px;
        box-sizing: border-box;
    }

    .search-section {
        background-color: #f8f9fa;
        padding: 20px;
        border-radius: 8px;
        margin-bottom: 20px;
    }

    .result-section {
        background-color: #e9ecef;
        padding: 20px;
        border-radius: 8px;
        margin: 20px 0;
    }

    .button-group {
        display: flex;
        gap: 10px;
        flex-wrap: wrap;
    }

    .info-card {
        background-color: #d1ecf1;
        border: 1px solid #bee5eb;
        border-radius: 8px;
        padding: 15px;
        margin: 15px 0;
    }
</style>

<div class="admin-page">    
    <div class="card">
        <div class="card-header">
            <h4 style="color: #0033CC; font-weight: bold;">【管理者作業】</h4>
        </div>
        <div class="card-body">
            @if (!string.IsNullOrEmpty(errorMessage))
            {
                <div class="alert alert-danger">
                    @errorMessage
                </div>
            }

            @if (!string.IsNullOrEmpty(successMessage))
            {
                <div class="alert alert-success">
                    @successMessage
                </div>
            }

            @if (!_userState.IsAdmin)
            {
                <!-- 非管理員只顯示錯誤訊息，不顯示任何功能 -->
                <div class="alert alert-warning">
                    <h5>存取被拒絕</h5>
                    <p>此頁面僅限管理員使用。如果您認為這是錯誤，請聯繫系統管理員。</p>
                </div>
            }
            else
            {
                <!-- 搜尋區域 -->
                <div class="search-section">
                <div class="row">
                    <div class="col-md-6">
                        <label class="form-label">彙整編號:</label>
                        <input type="text" class="form-control" @bind="searchId" @onkeypress="OnKeyPress"
                            placeholder="請輸入彙整編號" />
                    </div>
                    <div class="col-md-6 d-flex align-items-end">
                        <button class="btn btn-primary" @onclick="SearchData">搜尋</button>
                    </div>
                </div>
            </div>

            <!-- 搜尋結果區域 -->
            @if (showResults && !string.IsNullOrEmpty(searchResultMessage))
            {
                <div class="result-section">
                    <div class="info-card">
                        <h5><strong>彙整編號:</strong> @searchId</h5>
                        <div>@searchResultMessage</div>
                    </div>

                    <div class="row mt-3">
                        <div class="col-md-6">
                            <label class="form-label">匯款日期:</label>
                            <input type="date" class="form-control" @bind="selectedCashDate" />
                            <button class="btn btn-success mt-2" @onclick="UpdateCashDate">設定匯款日期</button>
                        </div>
                    </div>

                    <div class="mt-4">
                        <div class="button-group">
                            <button class="btn btn-info" @onclick="ExportFile">@exportButtonText</button>
                            <button class="btn btn-warning" @onclick="GenerateReport">產生報表</button>
                            <button class="btn btn-secondary" @onclick="ExportFinancialFile">產生庫款系統匯出檔</button>
                        </div>
                    </div>
                </div>
            }

            <!-- 說明區域 -->
            @if (!showResults)
            {
                <div class="text-center py-5">
                    <h5 class="text-muted">管理者作業說明</h5>
                    <div class="mt-3">
                        <p class="text-muted">請輸入彙整編號進行搜尋，系統將顯示該編號的彙整資料</p>
                        <ul class="list-unstyled text-muted">
                            <li>• 設定匯款日期</li>
                            <li>• 匯出郵局或台銀匯出檔</li>
                            <li>• 產生PDF報表</li>
                            <li>• 產生庫款系統匯出檔</li>
                        </ul>
                    </div>
                </div>
            }
            } <!-- 結束 else 區塊 -->
        </div>
    </div>
</div>

<!-- JavaScript 函數 -->
<script>
    function downloadFile(filename, content) {
        const element = document.createElement('a');
        element.setAttribute('href', 'data:text/plain;base64,' + content);
        element.setAttribute('download', filename);
        element.style.display = 'none';
        document.body.appendChild(element);
        element.click();
        document.body.removeChild(element);
    }

    function downloadPdfFile(filename, content) {
        const element = document.createElement('a');
        element.setAttribute('href', 'data:application/pdf;base64,' + content);
        element.setAttribute('download', filename);
        element.style.display = 'none';
        document.body.appendChild(element);
        element.click();
        document.body.removeChild(element);
    }
</script>

@code {
    private string searchId = "";
    private string searchResultMessage = "";
    private string errorMessage = "";
    private string successMessage = "";

    private bool showResults = false;
    private bool ifGenPostFile = true;
    private string exportButtonText = "匯出郵局匯出檔";
    private DateTime? selectedCashDate;

    private List<Intra2025.Models.ComRemit.RemitedList> currentRemitedList = new
    List<Intra2025.Models.ComRemit.RemitedList>();

    protected override async Task OnInitializedAsync()
    {
        await base.OnInitializedAsync();

        // 檢查是否為管理者權限
        if (!_userState.IsAdmin)
        {
            errorMessage = "您沒有管理者權限，無法使用此功能";
            return;
        }
    }

    private int? GetSearchIdAsInt()
    {
        if (int.TryParse(searchId, out var id))
        {
            return id;
        }
        errorMessage = "彙整編號格式不正確，必須為數字。";
        successMessage = "";
        showResults = false;
        return null;
    }

    private async Task OnKeyPress(KeyboardEventArgs e)
    {
        if (e.Key == "Enter")
        {
            await SearchData();
        }
    }

    private async Task SearchData()
    {
        try
        {
            errorMessage = "";
            successMessage = "";
            showResults = false;

            var searchIdInt = GetSearchIdAsInt();
            if (searchIdInt == null)
            {
                StateHasChanged();
                return;
            }

            currentRemitedList = await RemitedListService.GetRemitedRawListByConSnoAsync(searchIdInt.Value);

            if (!currentRemitedList.Any())
            {
                errorMessage = "找不到對應的資料";
                showResults = false;
                StateHasChanged();
                return;
            }

            // 判斷是郵局還是台銀
            ifGenPostFile = currentRemitedList.All(r => r.CollectNo == "7000021");
            exportButtonText = ifGenPostFile ? "匯出郵局匯出檔" : "匯出台銀匯出檔";

            var total = currentRemitedList.Sum(r => (decimal)(r.RemitPrice ?? 0));
            var nameList = currentRemitedList.Take(3).Select(r => r.CollecName ?? "").ToList();
            var names = string.Join(",", nameList);
            if (currentRemitedList.Count > 3)
            {
                names += "等";
            }

            searchResultMessage = $"匯入{names}{currentRemitedList.Count}人，共{total:N0}元。";
            showResults = true;
        }
        catch (Exception ex)
        {
            errorMessage = $"搜尋時發生錯誤: {ex.Message}";
            showResults = false;
            Logger.LogError(ex, "搜尋彙整資料時發生錯誤");
        }
        StateHasChanged();
    }

    private async Task UpdateCashDate()
    {
        try
        {
            if (!selectedCashDate.HasValue)
            {
                errorMessage = "請選擇匯款日期";
                return;
            }

            var searchIdInt = GetSearchIdAsInt();
            if (searchIdInt == null) return;

            var success = await RemitedListService.UpdateCashDateAsync(searchIdInt.Value, selectedCashDate.Value);

            if (success)
            {
                successMessage = $"已設定彙整編號 {searchId} 的匯款日期為 {selectedCashDate.Value:yyyy-MM-dd}";
                errorMessage = "";
            }
            else
            {
                errorMessage = "設定匯款日期失敗";
            }
        }
        catch (Exception ex)
        {
            errorMessage = $"設定匯款日期時發生錯誤: {ex.Message}";
            Logger.LogError(ex, "設定匯款日期時發生錯誤");
        }
        StateHasChanged();
    }

    private async Task ExportFile()
    {
        try
        {
            if (!currentRemitedList.Any())
            {
                errorMessage = "沒有資料可以匯出";
                return;
            }

            string fileContent;
            string fileName;

            if (ifGenPostFile)
            {
                // 產生郵局匯出檔
                fileContent = GeneratePostFile();
                fileName = $"郵局匯出檔_{searchId}_{DateTime.Now:yyyyMMdd}.txt";
            }
            else
            {
                // 產生台銀匯出檔
                fileContent = GenerateBankFile();
                fileName = $"台銀匯出檔_{searchId}_{DateTime.Now:yyyyMMdd}.txt";
            }

            var base64Content = Convert.ToBase64String(System.Text.Encoding.UTF8.GetBytes(fileContent));
            await JSRuntime.InvokeVoidAsync("downloadFile", fileName, base64Content);

            successMessage = $"已產生 {fileName}";
            errorMessage = "";
        }
        catch (Exception ex)
        {
            errorMessage = $"匯出檔案時發生錯誤: {ex.Message}";
            Logger.LogError(ex, "匯出檔案時發生錯誤");
        }
        StateHasChanged();
    }

    private string GeneratePostFile()
    {
        var lines = new List<string>();

        foreach (var item in currentRemitedList)
        {
            // 郵局格式：帳號,戶名,金額,用途
            var line = $"{item.CollecAcc},{item.CollecName},{item.RemitPrice},{item.RemitMemo}";
            lines.Add(line);
        }

        return string.Join("\n", lines);
    }

    private string GenerateBankFile()
    {
        var lines = new List<string>();

        foreach (var item in currentRemitedList)
        {
            // 台銀格式：機構代號,帳號,戶名,金額,用途
            var line = $"{item.CollectNo},{item.CollecAcc},{item.CollecName},{item.RemitPrice},{item.RemitMemo}";
            lines.Add(line);
        }

        return string.Join("\n", lines);
    }

    private async Task GenerateReport()
    {
        try
        {
            if (!currentRemitedList.Any())
            {
                errorMessage = "沒有資料可以產生報表";
                return;
            }

            // 使用與 RemitedList 相同的列印方式
            var result = await RemitedListService.PrintPdfAsync(int.Parse(searchId));

            if (result.PdfBytes != null && result.PdfBytes.Length > 0)
            {
                // 將 PDF 轉換為 Base64
                var base64 = Convert.ToBase64String(result.PdfBytes);

                // 驗證 Base64 內容是否為有效的 PDF（檢查前幾個字元）
                var pdfHeader = result.PdfBytes.Take(4).ToArray();
                var isValidPdf = pdfHeader.Length >= 4 && pdfHeader[0] == 0x25 && pdfHeader[1] == 0x50 && pdfHeader[2] == 0x44 && pdfHeader[3] == 0x46;

                if (!isValidPdf)
                {
                    errorMessage = "PDF 檔案格式無效，請聯絡系統管理員";
                    return;
                }

                // 嘗試多種方式顯示 PDF
                try
                {
                    // 方法1：使用 downloadFile 直接下載
                    await JSRuntime.InvokeVoidAsync("downloadFile", result.Filename, base64);
                    successMessage = $"已產生 {result.Filename}";
                    errorMessage = "";
                }
                catch (Exception)
                {
                    // 方法2：在新視窗中開啟（備用方案）
                    var dataUrl = $"data:application/pdf;base64,{base64}";
                    await JSRuntime.InvokeVoidAsync("open", dataUrl, "_blank");
                    successMessage = "PDF 已在新視窗中開啟";
                    errorMessage = "";
                }
            }
            else
            {
                errorMessage = "無法產生PDF檔案 - 檔案為空或生成失敗";
            }
        }
        catch (Exception ex)
        {
            errorMessage = $"列印失敗: {ex.Message}";
            Logger.LogError(ex, "產生報表時發生錯誤");
        }
        StateHasChanged();
    }



    private async Task ExportFinancialFile()
    {
        try
        {
            if (!currentRemitedList.Any())
            {
                errorMessage = "沒有資料可以匯出";
                return;
            }

            var fileContent = GenerateFinancialSystemFile();
            var fileName = $"庫款系統匯出檔_{searchId}_{DateTime.Now:yyyyMMdd}.csv";
            var base64Content = Convert.ToBase64String(System.Text.Encoding.UTF8.GetBytes(fileContent));

            await JSRuntime.InvokeVoidAsync("downloadFile", fileName, base64Content);

            successMessage = $"已產生 {fileName}";
            errorMessage = "";
        }
        catch (Exception ex)
        {
            errorMessage = $"產生庫款系統匯出檔時發生錯誤: {ex.Message}";
            Logger.LogError(ex, "產生庫款系統匯出檔時發生錯誤");
        }
        StateHasChanged();
    }

    private string GenerateFinancialSystemFile()
    {
        var lines = new List<string>();

        // CSV 標頭
        lines.Add("彙整編號,收款人戶名,身分證字號,金融機構代號,帳號,匯款金額,用途說明,匯款日期");

        foreach (var item in currentRemitedList)
        {
            var cashDateStr = selectedCashDate?.ToString("yyyy-MM-dd") ?? "";
            var line =
            $"{searchId},{item.CollecName},{item.CollectId},{item.CollectNo},{item.CollecAcc},{item.RemitPrice},{item.RemitMemo},{cashDateStr}";
            lines.Add(line);
        }

        return string.Join("\n", lines);
    }
}
