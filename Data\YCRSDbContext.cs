﻿using Intra2025.Models;
using Intra2025.Models.YouthCareReportService;
using Microsoft.EntityFrameworkCore;

namespace Intra2025.Data
{
    public class YCRSDbContext : DbContext
    {
        public DbSet<YCRS_ChildRecord> YCRS_ChildRecord { get; set; }
        public DbSet<YCRS_CareRecord> YCRS_CareRecord { get; set; }
        public DbSet<YCRS_AccessLog> YCRS_AccessLog { get; set; }
        public DbSet<YCRS_Files> YCRS_Files { get; set; }

        public YCRSDbContext(DbContextOptions<YCRSDbContext> options)
            : base(options)
        {
        }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // 設定 YCRS_Files 的 Sno 欄位為自動遞增
            modelBuilder.Entity<YCRS_Files>()
                .Property(f => f.Sno)
                .UseIdentityColumn();

            // 設定 YCRS_AccessLog 的 Id 欄位為自動遞增
            modelBuilder.Entity<YCRS_AccessLog>()
                .Property(a => a.Id)
                .UseIdentityColumn();
        }
    }
}
