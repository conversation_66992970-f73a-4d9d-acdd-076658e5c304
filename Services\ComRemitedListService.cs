using Intra2025.Models.ComRemit;
using Microsoft.EntityFrameworkCore;
using QuestPDF.Fluent;
using QuestPDF.Helpers;
using QuestPDF.Infrastructure;
using QuestPDF.Previewer;
using QuestPDF.Drawing;
using QuestPDF.Elements;
using Intra2025.Data;
using Intra2025.Components.Base;

namespace Intra2025.Services
{
    public class ComRemitedListService
    {
        private readonly ComRemitDbContext _context;
        private readonly ComRemitQRCodeService _barcodeService;
        private readonly UserState _userState;

        public ComRemitedListService(ComRemitDbContext context, ComRemitQRCodeService barcodeService, UserState userState)
        {
            _context = context;
            _barcodeService = barcodeService;
            _userState = userState;
        }

        /// <summary>
        /// 判斷是否需要扣手續費
        /// </summary>
        /// <param name="ifFeeValue">手續費類別值</param>
        /// <returns>是否需要扣手續費</returns>
        private bool ShouldChargeFee(string? ifFeeValue)
        {
            if (string.IsNullOrEmpty(ifFeeValue)) return false;

            // 只有類別7需要扣手續費，其他類別(1-6)都不扣
            return ifFeeValue == "7" || ifFeeValue == "是"; // 向後兼容舊資料
        }

        public async Task<(List<RemitedListViewModel> Items, int TotalPages)> GetPagedListAsync(string keyword, int pageIndex, int pageSize)
        {
            // 只顯示已完成彙整的資料（CashDate 不為 null）
            var query = _context.RemitedList.Where(r => r.CashDate != null).AsQueryable();

            // 合併搜尋（系統編號/收款人戶名/用途說明）
            if (!string.IsNullOrWhiteSpace(keyword))
            {
                // 嘗試解析為系統編號
                if (int.TryParse(keyword, out var conSnoInt))
                {
                    query = query.Where(r => r.ConSno == conSnoInt ||
                                           (r.CollecName ?? "").Contains(keyword) ||
                                           (r.RemitMemo ?? "").Contains(keyword));
                }
                else
                {
                    // 純文字搜尋（收款人戶名/用途說明）
                    query = query.Where(r => (r.CollecName ?? "").Contains(keyword) || (r.RemitMemo ?? "").Contains(keyword));
                }
            }
            var group = query.GroupBy(r => r.ConSno)
                .Select(g => new RemitedListViewModel
                {
                    ConSno = g.Key,
                    Count = g.Count(),
                    TotalAmount = g.Sum(x => (decimal)(x.RemitPrice ?? 0)),
                    RemitMemo = g.Select(x => x.RemitMemo).FirstOrDefault(),
                    ConDate = g.Select(x => x.ConDate).FirstOrDefault(),
                    ConMemo = g.Select(x => x.ConMemo).FirstOrDefault(), // 使用正確的 ConMemo 欄位
                    CashDate = g.Select(x => x.CashDate).FirstOrDefault() // 添加 CashDate 用於排序
                });
            var totalCount = await group.CountAsync();
            var totalPages = (int)Math.Ceiling(totalCount / (double)pageSize);
            var items = await group.OrderByDescending(x => x.CashDate ?? x.ConDate) // 優先按 CashDate 排序，如果沒有則用 ConDate
                .ThenByDescending(x => x.ConSno) // 次要排序條件
                .Skip((pageIndex - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync();

            // 為每個項目載入前5筆明細
            foreach (var item in items)
            {
                if (item.ConSno.HasValue)
                {
                    item.Details = await GetDetailListAsync(item.ConSno.Value);
                }
            }

            return (items, totalPages);
        }

        public async Task<List<RemitedListDetailViewModel>> GetDetailListAsync(int conSno)
        {
            return await _context.RemitedList
                .Where(r => r.ConSno == conSno)
                .OrderBy(r => r.Sno)
                .Take(5)
                .Select(r => new RemitedListDetailViewModel
                {
                    CollecName = r.CollecName,
                    CollecAcc = r.CollecAcc,
                    RemitPrice = r.RemitPrice,
                    CollectNo = r.CollectNo
                })
                .ToListAsync();
        }

        public async Task DeleteByConSnoAsync(int conSno)
        {
            // 權限檢查：只有管理員可以批量刪除彙整資料
            if (!_userState.IsAdmin)
            {
                throw new UnauthorizedAccessException("批量刪除彙整資料需要管理員權限");
            }

            var items = await _context.RemitedList.Where(r => r.ConSno == conSno).ToListAsync();
            if (items.Any())
            {
                _context.RemitedList.RemoveRange(items);
                await _context.SaveChangesAsync();
            }
        }

        public async Task<(string content, string filename)> ExportTxtAsync(int conSno)
        {
            var items = await _context.RemitedList.Where(r => r.ConSno == conSno).OrderBy(r => r.Sno).ToListAsync();
            if (!items.Any()) return ("", "");

            // 產生CSV格式，與批次匯入功能相容
            var lines = new List<string>();

            // 加入CSV標題行
            lines.Add("局號,帳號,匯款戶名,統編(身分證),帳款金額,是否有手續費,備註(此列不需刪除，資料由第2列開始加入即可)");

            // 加入資料行
            foreach (var item in items)
            {
                // 轉換IfFee格式：將資料庫中的值轉換為"是"/"否"格式
                var ifFeeText = "否"; // 預設值
                if (!string.IsNullOrEmpty(item.IfFee))
                {
                    if (item.IfFee == "是" || item.IfFee == "1" || item.IfFee.ToLower() == "true")
                    {
                        ifFeeText = "是";
                    }
                }

                // 處理可能包含逗號的欄位，用雙引號包圍
                var collectName = EscapeCsvField(item.CollecName ?? "");
                var remitMemo = EscapeCsvField(item.RemitMemo ?? "");

                var line = $"{item.CollectNo ?? ""},{item.CollecAcc ?? ""},{collectName},{item.CollectId ?? ""},{item.RemitPrice ?? 0},{ifFeeText},{remitMemo}";
                lines.Add(line);
            }

            var content = string.Join("\r\n", lines);
            var filename = $"CR{conSno:000000}_Export.csv"; // 改為CSV副檔名

            return (content, filename);
        }

        /// <summary>
        /// 處理CSV欄位中的特殊字元，如果包含逗號、雙引號或換行符號則用雙引號包圍
        /// </summary>
        private string EscapeCsvField(string field)
        {
            if (string.IsNullOrEmpty(field))
                return field;

            // 如果欄位包含逗號、雙引號或換行符號，需要用雙引號包圍
            if (field.Contains(",") || field.Contains("\"") || field.Contains("\r") || field.Contains("\n"))
            {
                // 將欄位中的雙引號替換為兩個雙引號
                field = field.Replace("\"", "\"\"");
                return $"\"{field}\"";
            }

            return field;
        }

        /// <summary>
        /// 根據用戶帳號獲取已彙整清單 (權限控制)
        /// </summary>
        public async Task<List<RemitedListViewModel>> GetRemitedListByUserAccountAsync(string userAccount)
        {
            // 只顯示已完成彙整的資料（CashDate 不為 null）並根據用戶權限篩選
            var query = _context.RemitedList
                .Where(r => r.CashDate != null && r.ConPer == userAccount)
                .AsQueryable();

            var group = await query.GroupBy(r => r.ConSno)
                .Select(g => new RemitedListViewModel
                {
                    ConSno = g.Key,
                    Count = g.Count(),
                    TotalAmount = g.Sum(x => (decimal)(x.RemitPrice ?? 0)),
                    RemitMemo = g.Select(x => x.RemitMemo).FirstOrDefault(),
                    ConDate = g.Select(x => x.ConDate).FirstOrDefault(),
                    ConMemo = g.Select(x => x.ConMemo).FirstOrDefault(),
                    CashDate = g.Select(x => x.CashDate).FirstOrDefault()
                })
                .OrderByDescending(x => x.CashDate ?? x.ConDate)
                .ThenByDescending(x => x.ConSno)
                .ToListAsync();

            // 為每個項目載入前5筆明細
            foreach (var item in group)
            {
                if (item.ConSno.HasValue)
                {
                    item.Details = await GetDetailListAsync(item.ConSno.Value);
                }
            }

            return group;
        }

        /// <summary>
        /// 根據關鍵字和用戶帳號搜尋已彙整清單 (權限控制)
        /// </summary>
        public async Task<List<RemitedListViewModel>> SearchRemitedListByUserAccountAsync(string keyword, string userAccount)
        {
            // 只顯示已完成彙整的資料（CashDate 不為 null）並根據用戶權限篩選
            var query = _context.RemitedList
                .Where(r => r.CashDate != null && r.ConPer == userAccount)
                .AsQueryable();

            // 合併搜尋（系統編號/收款人戶名/用途說明）
            if (!string.IsNullOrWhiteSpace(keyword))
            {
                // 嘗試解析為系統編號
                if (int.TryParse(keyword, out var conSnoInt))
                {
                    query = query.Where(r => r.ConSno == conSnoInt ||
                                           (r.CollecName ?? "").Contains(keyword) ||
                                           (r.RemitMemo ?? "").Contains(keyword));
                }
                else
                {
                    // 純文字搜尋（收款人戶名/用途說明）
                    query = query.Where(r => (r.CollecName ?? "").Contains(keyword) || (r.RemitMemo ?? "").Contains(keyword));
                }
            }

            var group = await query.GroupBy(r => r.ConSno)
                .Select(g => new RemitedListViewModel
                {
                    ConSno = g.Key,
                    Count = g.Count(),
                    TotalAmount = g.Sum(x => (decimal)(x.RemitPrice ?? 0)),
                    RemitMemo = g.Select(x => x.RemitMemo).FirstOrDefault(),
                    ConDate = g.Select(x => x.ConDate).FirstOrDefault(),
                    ConMemo = g.Select(x => x.ConMemo).FirstOrDefault(),
                    CashDate = g.Select(x => x.CashDate).FirstOrDefault()
                })
                .OrderByDescending(x => x.CashDate ?? x.ConDate)
                .ThenByDescending(x => x.ConSno)
                .ToListAsync();

            // 為每個項目載入前5筆明細
            foreach (var item in group)
            {
                if (item.ConSno.HasValue)
                {
                    item.Details = await GetDetailListAsync(item.ConSno.Value);
                }
            }

            return group;
        }

        /// <summary>
        /// 刪除已彙整資料
        /// </summary>
        public async Task<bool> DeleteRemitedAsync(int conSno)
        {
            try
            {
                var items = await _context.RemitedList.Where(r => r.ConSno == conSno).ToListAsync();
                if (items.Any())
                {
                    _context.RemitedList.RemoveRange(items);
                    await _context.SaveChangesAsync();
                    return true;
                }
                return false;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 根據 ConSno 獲取已彙整清單詳細資料
        /// </summary>
        public async Task<List<RemitedListViewModel>> GetRemitedListByConSnoAsync(int conSno)
        {
            var query = _context.RemitedList
                .Where(r => r.ConSno == conSno)
                .AsQueryable();

            var group = await query.GroupBy(r => r.ConSno)
                .Select(g => new RemitedListViewModel
                {
                    ConSno = g.Key,
                    Count = g.Count(),
                    TotalAmount = g.Sum(x => (decimal)(x.RemitPrice ?? 0)),
                    RemitMemo = g.Select(x => x.RemitMemo).FirstOrDefault(),
                    ConDate = g.Select(x => x.ConDate).FirstOrDefault(),
                    ConMemo = g.Select(x => x.ConMemo).FirstOrDefault(),
                    CashDate = g.Select(x => x.CashDate).FirstOrDefault()
                })
                .ToListAsync();

            // 為每個項目載入完整明細
            foreach (var item in group)
            {
                if (item.ConSno.HasValue)
                {
                    item.Details = await GetDetailListAsync(item.ConSno.Value);
                }
            }

            return group;
        }

        /// <summary>
        /// 更新兌現日期 (需要管理員權限)
        /// </summary>
        public async Task<bool> UpdateCashDateAsync(int conSno, DateTime cashDate)
        {
            // 權限檢查：只有管理員可以更新兌現日期
            if (!_userState.IsAdmin)
            {
                throw new UnauthorizedAccessException("此操作需要管理員權限");
            }

            try
            {
                var items = await _context.RemitedList.Where(r => r.ConSno == conSno).ToListAsync();
                foreach (var item in items)
                {
                    item.CashDate = cashDate;
                }
                await _context.SaveChangesAsync();
                return true;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 根據 ConSno 獲取已彙整的原始資料列表 (供 AdminList 使用，需要管理員權限)
        /// </summary>
        public async Task<List<RemitedList>> GetRemitedRawListByConSnoAsync(int conSno)
        {
            // 權限檢查：只有管理員可以查看彙整資料
            if (!_userState.IsAdmin)
            {
                throw new UnauthorizedAccessException("此操作需要管理員權限");
            }

            return await _context.RemitedList
                .Where(r => r.ConSno == conSno)
                .OrderBy(r => r.Sno)
                .ToListAsync();
        }

        public async Task<(byte[] PdfBytes, string Filename)> PrintPdfAsync(int conSno)
        {
            try
            {
                // 設定 QuestPDF 授權，確保關閉除錯模式
                QuestPDF.Settings.License = LicenseType.Community;
                QuestPDF.Settings.EnableDebugging = false;

                var items = await _context.RemitedList.Where(r => r.ConSno == conSno).OrderBy(r => r.Sno).ToListAsync();
                if (!items.Any())
                {
                    Console.WriteLine($"PrintPdfAsync: 找不到 ConSno={conSno} 的資料");
                    return (Array.Empty<byte>(), "");
                }

                // 判斷是否為郵局格式 (CollectNo 全部為 "7000021")
                var ifGenPostFile = items.All(r => r.CollectNo == "7000021");
                var searchId = conSno.ToString();

                Console.WriteLine($"PrintPdfAsync: ConSno={conSno}, 資料筆數={items.Count}, 是否郵局格式={ifGenPostFile}");

                byte[] pdfBytes;
                string filename;

                if (ifGenPostFile)
                {
                    Console.WriteLine("PrintPdfAsync: 生成郵局報表");
                    pdfBytes = GeneratePostalReportAdvanced(items, conSno);
                    filename = $"CR{searchId.PadLeft(6, '0')}_POST.pdf";
                }
                else
                {
                    Console.WriteLine("PrintPdfAsync: 生成台銀報表");
                    pdfBytes = GenerateTWBankReportAdvanced(items, conSno);
                    filename = $"CR{searchId.PadLeft(6, '0')}_TWBANK.pdf";
                }

                if (pdfBytes == null)
                {
                    Console.WriteLine("PrintPdfAsync: 警告 - PDF 生成返回 null");
                    return (Array.Empty<byte>(), "");
                }

                // Console.WriteLine($"PrintPdfAsync: PDF 生成完成，大小={pdfBytes?.Length ?? 0} bytes");
                return (pdfBytes ?? Array.Empty<byte>(), filename ?? "");
            }
            catch
            {
                // Console.WriteLine("PrintPdfAsync: 發生錯誤");
                throw; // 重新拋出異常，讓調用方處理
            }
        }

        private byte[] GeneratePostalReportAdvanced(List<RemitedList> list, int conSno)
        {
            try
            {
                var totalAmount = list.Count;
                var totalCost = list.Sum(x => (decimal)(x.RemitPrice ?? 0));
                var searchId = conSno.ToString();
                var filename = $"CR{searchId.PadLeft(6, '0')}_POST";
                var memo = list.FirstOrDefault()?.RemitMemo ?? "";
                var cashDate = list.FirstOrDefault()?.CashDate;

                // 取得 SSO 使用者基本資料
                var officePhone = _userState.OfficePhone ?? "";
                var fax = _userState.Fax ?? "";

                return Document.Create(container =>
                {
                    container.Page(page =>
                    {
                        page.Size(PageSizes.A4);
                        page.Margin(1.5f, Unit.Centimetre);
                        page.DefaultTextStyle(x => x.FontFamily("Microsoft YaHei").FontSize(11));

                        page.Content().Element(container =>
                        {
                            container.Column(column =>
                            {
                                for (int copyIndex = 0; copyIndex < 3; copyIndex++)
                                {
                                    if (copyIndex > 0)
                                    {
                                        column.Item().PageBreak();
                                    }

                                    // 總表部分 - 「委託郵局代存員工薪資總表(408)」
                                    GenerateSummarySection(column, filename, totalCost, totalAmount, memo);

                                    // 分頁
                                    column.Item().PageBreak();

                                    // 明細表部分 - 「郵政(存簿儲金)薪資存款團體戶存款單」
                                    GenerateDetailSection(column, filename, list);
                                }
                            });
                        });

                        // 按照AdminList的方式在右上角添加條碼
                        page.Foreground().PaddingTop(5).PaddingRight(20).AlignTop().AlignRight().Column(barcodeCol =>
                        {
                            // 顯示實際的條碼圖片
                            var qrText = filename;
                            try
                            {
                                if (System.Runtime.InteropServices.RuntimeInformation.IsOSPlatform(System.Runtime.InteropServices.OSPlatform.Windows))
                                {
                                    var barcodeBytes = _barcodeService.GenerateCode128Barcode(qrText);
                                    if (barcodeBytes.Length > 0)
                                    {
                                        barcodeCol.Item().PaddingTop(5).Width(200).Image(barcodeBytes).FitWidth();
                                        return;
                                    }
                                }
                            }
                            catch
                            {
                                // 如果條碼生成失敗，使用ASCII備用方案
                            }

                            // 備用方案：使用ASCII條碼
                            var barcodeAscii = _barcodeService.GenerateBarcodeAscii(qrText);
                            barcodeCol.Item().PaddingTop(5).Width(200).Text(barcodeAscii).FontFamily("Courier New").FontSize(6);
                            barcodeCol.Item().Width(200).PaddingTop(2).Text(qrText).FontSize(9).AlignCenter();
                        });
                    });
                }).GeneratePdf();
            }
            catch
            {
                // 如果生成失敗，輸出錯誤訊息並返回簡化版本
                Console.WriteLine($"GeneratePostalReportAdvanced 發生錯誤");
                return GenerateSimplePostalReport(list, conSno);
            }
        }

        private void GenerateSummarySection(ColumnDescriptor column, string filename, decimal totalCost, int totalAmount, string memo)
        {
            column.Item().PaddingBottom(18);
            column.Item().Text("委託郵局代存員工薪資總表(408)").FontSize(18).Bold().AlignCenter();

            // 第一行表格 - 按照Fun.cs的genRow1Cell3標準
            column.Item().DefaultTextStyle(x => x.FontSize(9)).Table(table =>
            {
                table.ColumnsDefinition(columns =>
                {
                    columns.RelativeColumn(1);
                    columns.RelativeColumn(1);
                    columns.RelativeColumn(1);
                });

                table.Cell().Element(CellStyle).Text($"\n受託局名: 宜蘭中山路郵局\n\n受託局號: 0111000\n\n檔名: {filename}").FontSize(9);
                table.Cell().Element(CellStyle).Text($"\n劃撥儲金帳號:11206482\n\n\n押碼值:").FontSize(9);

                // 第三個cell包含詳細的資料別表格
                table.Cell().Element(container => container).Table(innerTable =>
                {
                    innerTable.ColumnsDefinition(columns =>
                    {
                        columns.ConstantColumn(30);
                        columns.RelativeColumn(1);
                        columns.ConstantColumn(30);
                    });

                    innerTable.Cell().Element(BorderCellStyle).Text("勾註").FontSize(9);
                    innerTable.Cell().Element(BorderCellStyle).Text("資料別及作業方式").FontSize(9);
                    innerTable.Cell().Element(BorderCellStyle).Text("批次").FontSize(9);

                    innerTable.Cell().Element(BorderCellStyle).Text(" ");
                    innerTable.Cell().Element(BorderCellStyle).Text("媒體薪資類(Y)").FontSize(9);
                    innerTable.Cell().Element(BorderCellStyle).Text(" ");

                    innerTable.Cell().Element(BorderCellStyle).Text(" ");
                    innerTable.Cell().Element(BorderCellStyle).Text("媒體非薪資類(N)").FontSize(9);
                    innerTable.Cell().Element(BorderCellStyle).Text(" ");

                    innerTable.Cell().Element(BorderCellStyle).Text(" ");
                    innerTable.Cell().Element(BorderCellStyle).Text("非媒體類(C)").FontSize(9);
                    innerTable.Cell().Element(BorderCellStyle).Text(" ");

                    innerTable.Cell().Element(BorderCellStyle).Text(" ");
                    innerTable.Cell().Element(BorderCellStyle).Text("薪資存款直接傳輸作業").FontSize(9);
                    innerTable.Cell().Element(BorderCellStyle).Text(" ");

                    innerTable.Cell().Element(BorderCellStyle).Text(" ");
                    innerTable.Cell().Element(BorderCellStyle).Text("本局磁片傳輸").FontSize(9);
                    innerTable.Cell().Element(BorderCellStyle).Text(" ");

                    innerTable.Cell().Element(BorderCellStyle).Text(" ");
                    innerTable.Cell().Element(BorderCellStyle).Text("本局傳輸(非媒體)").FontSize(9);
                    innerTable.Cell().Element(BorderCellStyle).Text(" ");
                });
            });

            column.Item().PaddingVertical(10);

            // 第二行表格 - 按照Fun.cs的genRow2標準
            column.Item().DefaultTextStyle(x => x.FontSize(9)).Table(table =>
            {
                table.ColumnsDefinition(columns =>
                {
                    columns.ConstantColumn(80);
                    columns.ConstantColumn(80);
                    columns.ConstantColumn(80);
                    columns.ConstantColumn(80);
                    columns.ConstantColumn(80);
                    columns.RelativeColumn(1);
                });

                // 表頭                
                table.Cell().Element(BorderCellStyle).AlignCenter().Text("指定轉存日期");
                table.Cell().Element(BorderCellStyle).AlignCenter().Text("委存總件數");
                table.Cell().Element(BorderCellStyle).AlignCenter().Text("委存總金額");
                table.Cell().Element(BorderCellStyle).AlignCenter().Text("款項細目代碼");
                table.Cell().Element(BorderCellStyle).AlignCenter().Text("款項來源代號\n(本欄由郵局填寫)");
                table.Cell().Element(BorderCellStyle).AlignCenter().Text("備註");

                // 資料行
                table.Cell().Element(BorderCellStyle).Text(" ");
                table.Cell().Element(BorderCellStyle).AlignCenter().Text($"{totalAmount:N0}");
                table.Cell().Element(BorderCellStyle).AlignCenter().Text($"{totalCost:N0}");
                table.Cell().Element(BorderCellStyle).Text(" ");
                table.Cell().Element(BorderCellStyle).Text(" ");
                table.Cell().Element(BorderCellStyle).Text(memo);
            });

            column.Item().PaddingVertical(10);

            // 委託機構信息區塊 - 按照Fun.cs第120行的格式
            column.Item().Text($"\n委託機構名稱: 宜蘭縣政府\n委託機構地址: 宜蘭市縣政北路1號\n連絡    電話: {_userState.OfficePhone}\n傳真    號碼: {_userState.Fax}\n經辦:                               單位主管:")
                .FontSize(9);

            column.Item().PaddingVertical(10);

            // 右側區塊：核章黑框和儲匯壽險專用章
            column.Item().AlignRight().Column(rightColumn =>
            {
                // 核章黑框 - 按照Fun.cs的genRow4Cell3標準
                rightColumn.Item().Width(120).Height(80).Border(1).BorderColor(Colors.Black);

                rightColumn.Item().PaddingTop(10);

                // 儲匯壽險專用章區塊
                rightColumn.Item().Text("儲匯壽險專用章\n主管:__________")
                    .FontSize(9);
            });

            column.Item().PaddingVertical(20);

            column.Item().Text("說明:\n1.本表由委託機構於每月撥存員工薪資時填造一式三份檢附相關團體戶存款單及支票，一併交受託局(以薪資存款直接傳輸作業者，僅需填一份，免造送存款單)。\n2.本表各項目請正確填寫清楚，並與附件支票及團體戶存款單核對無誤。\n3.委託機構應將撥存總額開具劃線支票一張，在本表備註欄註明付款行庫名稱、帳號及支票號碼。\n4.受託局受理時在一份總表指定處加蓋主管及經辦員章與郵戳作為收款及收件之依據，不另備文或另開收據。\n5.本表右上角薪資轉存「資料別及作業方式」、「批次」等欄務必勾填。\n6.薪資存款入帳後，受託局應將留局之薪資總表隨「現金及票卷日報」送會計單位審核後，退回存查。")
                .FontSize(9);

            column.Item().PaddingVertical(20);

            // 直接傳輸作業表格 - 按照Fun.cs的genRow7標準
            column.Item().Table(table =>
            {
                table.ColumnsDefinition(columns =>
                {
                    columns.RelativeColumn(1);  // 直接傳輸作業
                    columns.RelativeColumn(1);  // 1.傳送時間
                    columns.RelativeColumn(1);  // 2.是否須回送存款入帳詳情
                    columns.RelativeColumn(2);  // 回送選項
                    columns.RelativeColumn(1);  // 實際轉存/撥退
                    columns.RelativeColumn(1);  // 筆數
                    columns.RelativeColumn(1);  // 金額
                });

                // 第一行
                table.Cell().RowSpan(3).Element(BorderCellStyle).Text("直接傳輸作業").FontSize(10).AlignCenter();
                table.Cell().RowSpan(3).Element(BorderCellStyle).Text("1.傳送時間：__月__日__午__時__分").FontSize(9);
                table.Cell().RowSpan(3).Element(BorderCellStyle).Text("2.是否須回送存款入帳詳情").FontSize(9);
                table.Cell().RowSpan(3).Element(BorderCellStyle).Text("□Y回送紙本\n□E回送電子檔\n□B回送紙本及電子檔\n□N不回送").FontSize(9).AlignLeft();
                table.Cell().Element(BorderCellStyle).Text("實際轉存").FontSize(9).AlignCenter();
                table.Cell().Element(BorderCellStyle).Text("_________筆").FontSize(9).AlignCenter();
                table.Cell().Element(BorderCellStyle).Text("_________元").FontSize(9).AlignCenter();

                // 第二行
                table.Cell().Element(BorderCellStyle).Text("撥退").FontSize(9).AlignCenter();
                table.Cell().Element(BorderCellStyle).Text("_________筆").FontSize(9).AlignCenter();
                table.Cell().Element(BorderCellStyle).Text("_________元").FontSize(9).AlignCenter();

                // 第三行
                table.Cell().ColumnSpan(3).Element(BorderCellStyle).Text("經辦:__________  主管:__________").FontSize(9);
            });
        }

        private void GenerateDetailSection(ColumnDescriptor column, string filename, List<RemitedList> list)
        {
            // 按照 Fun.cs 的 genDetailPageHeader 結構建立表頭，完全符合標準格式
            column.Item().PaddingBottom(12);
            column.Item().Table(table =>
            {
                // 使用4欄位表格，相對寬度為 [1, 1, 4, 3] - 完全按照Fun.cs標準
                table.ColumnsDefinition(columns =>
                {
                    columns.RelativeColumn(1);
                    columns.RelativeColumn(1);
                    columns.RelativeColumn(4);
                    columns.RelativeColumn(3);
                });

                // 第一行 - 按照Fun.cs genDetailPageHeader標準
                table.Cell().Element(NoBorderCellStyle).Text(" "); // 空白
                table.Cell()
                // *** 順序修正：ColumnSpan() 必須在 Element() 之前呼叫 ***
                .ColumnSpan(2)
                .Element(NoBorderCellStyle)
                .AlignCenter()
                .Text("郵政(存簿儲金)薪資存款團體戶存款單").FontSize(16).Bold();


                table.Cell().Element(NoBorderCellStyle).Text("030 連線\n033 非連線").FontSize(10);

                // 第二行 - 按照Fun.cs genDetailPageHeader標準
                table.Cell().Element(NoBorderCellStyle).Text("受託局名\n及局號戳").FontSize(10);
                table.Cell().Element(NoBorderCellStyle).Text(" "); // 空白
                table.Cell().Element(NoBorderCellStyle).Text("劃撥儲金帳號:\n\n存款日期:  年  月  日").FontSize(10);

                // 第四欄：按照genRow2Cell4標準建立子表格
                table.Cell().Element(container => GenerateRow2Cell4SubTable(container));
            });

            //column.Item().PaddingVertical(2);

            const int detailRecordsPerPage = 20; // 按照Fun.cs的標準，每頁20筆
            var detailTotalPages = Math.Max(1, (int)Math.Ceiling((double)list.Count / detailRecordsPerPage));

            for (int detailPageIndex = 0; detailPageIndex < detailTotalPages; detailPageIndex++)
            {
                if (detailPageIndex > 0)
                {
                    column.Item().PageBreak();
                    column.Item().PaddingBottom(2);
                    // 在右上角添加條碼（後續頁面）
                    column.Item().Row(row =>
                    {
                        row.RelativeItem(3).Text(""); // 左側空白
                        row.RelativeItem(1).AlignRight().Text($"{filename}-{detailPageIndex + 1}").FontFamily("Courier New").FontSize(8); // 右側條碼文字
                    });

                    // 後續頁面按照標準格式顯示表頭
                    //column.Item().PaddingBottom(8);
                    column.Item().Table(table =>
                    {
                        table.ColumnsDefinition(columns =>
                        {
                            columns.RelativeColumn(1);
                            columns.RelativeColumn(1);
                            columns.RelativeColumn(4);
                            columns.RelativeColumn(3);
                        });

                        // 第一行 - 按照Fun.cs genDetailPageHeader標準
                        table.Cell().Element(NoBorderCellStyle).Text(" "); // 空白
                        table.Cell().Element(NoBorderCellStyle).Text(" "); // 空白
                        table.Cell().Element(NoBorderCellStyle).Text("郵政(存簿儲金)薪資存款團體戶存款單").FontSize(14).Bold();
                        table.Cell().Element(NoBorderCellStyle).Text("030 連線\n033 非連線").FontSize(10);

                        // 第二行 - 按照Fun.cs genDetailPageHeader標準
                        table.Cell().Element(NoBorderCellStyle).Text("受託局名\n及局號戳").FontSize(10);
                        table.Cell().Element(NoBorderCellStyle).Text(" "); // 空白
                        table.Cell().Element(NoBorderCellStyle).Text("劃撥儲金帳號:\n\n存款日期:  年  月  日").FontSize(10);
                        table.Cell().Element(container => GenerateRow2Cell4SubTable(container));

                        // 空行 - 按照Fun.cs標準
                        //table.Cell().ColumnSpan(2).Element(NoBorderCellStyle).Text("\n");
                    });
                }

                var detailStartIndex = detailPageIndex * detailRecordsPerPage;
                var detailEndIndex = Math.Min(detailStartIndex + detailRecordsPerPage, list.Count);
                var detailPageData = list.Skip(detailStartIndex).Take(detailEndIndex - detailStartIndex).ToList();

                // 按照Fun.cs的35欄位結構建立表格
                GenerateDetailTable(column, detailPageData, (int)list.Sum(x => x.RemitPrice ?? 0), list.Count, detailPageIndex == detailTotalPages - 1);
                        }
        }

        private void GenerateDetailTable(ColumnDescriptor column, List<RemitedList> pageData, int totalAmount, int totalCount, bool isLastPage)
        {
            // 建立35欄位的明細表格，完全按照Fun.cs的genRow3結構
            column.Item().Table(table =>
            {
                // 定義35個欄位的相對寬度，完全按照Fun.cs的float[] { 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 4, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1 }
                table.ColumnsDefinition(columns =>
                {
                    // 前16個欄位：員工立帳局號(6) + 檢號(1) + 存簿帳號(7) + 檢號(1) + cr[13](1) = 16欄
                    for (int i = 0; i < 16; i++)
                        columns.RelativeColumn(1);

                    // 第17欄：戶名，寬度為4
                    columns.RelativeColumn(4);

                    // 後18個欄位：身分證統一編號(10) + 存款金額(8) = 18欄
                    for (int i = 0; i < 18; i++)
                        columns.RelativeColumn(1);
                });

                // 表頭第一行 - 完全按照Fun.cs的genRow3結構
                // 員工立帳局號 (Colspan=7, Rowspan=2) - 包含前7位數字 + 檢號
                table.Cell().ColumnSpan(7).RowSpan(2).Element(DetailCellStyle).Text("員工立帳局號").FontSize(8).AlignCenter();

                // 檢號 (Rowspan=2) - 存簿帳號的檢號
                table.Cell().RowSpan(2).Element(DetailCellStyle).Text("檢號").FontSize(8).AlignCenter();
                // 存簿帳號 (Colspan=7, Rowspan=2) - 包含7位數字
                table.Cell().ColumnSpan(7).RowSpan(2).Element(DetailCellStyle).Text("存簿帳號").FontSize(8).AlignCenter();

                // 檢號 (Rowspan=2) - 存簿帳號的檢號
                table.Cell().RowSpan(2).Element(DetailCellStyle).Text("檢號").FontSize(8).AlignCenter();

                // 戶名 (Rowspan=2)
                table.Cell().RowSpan(2).Element(DetailCellStyle).Text("戶名").FontSize(8).AlignCenter();

                // 身分證統一編號 (Colspan=10, Rowspan=2)
                table.Cell().ColumnSpan(10).RowSpan(2).Element(DetailCellStyle).Text("身分證統一編號").FontSize(8).AlignCenter();

                // 存款金額 (Colspan=8) - 只跨第一行
                table.Cell().ColumnSpan(8).Element(DetailCellStyle).Text("存款金額").FontSize(8).AlignCenter();

                // 表頭第二行 - 只有存款金額的細分
                table.Cell().Element(DetailCellStyle).Text("千萬").FontSize(8).AlignCenter();
                table.Cell().Element(DetailCellStyle).Text("百萬").FontSize(8).AlignCenter();
                table.Cell().Element(DetailCellStyle).Text("十萬").FontSize(8).AlignCenter();
                table.Cell().Element(DetailCellStyle).Text("萬").FontSize(8).AlignCenter();
                table.Cell().Element(DetailCellStyle).Text("千").FontSize(8).AlignCenter();
                table.Cell().Element(DetailCellStyle).Text("百").FontSize(8).AlignCenter();
                table.Cell().Element(DetailCellStyle).Text("十").FontSize(8).AlignCenter();
                table.Cell().Element(DetailCellStyle).Text("元").FontSize(8).AlignCenter();

                // 資料行 - 完全按照Fun.cs的genRow3結構
                foreach (var item in pageData)
                {
                    var accountNo = (item.CollecAcc ?? "").Replace("-", "").Trim();
                    if (accountNo.Length != 14)
                    {
                        accountNo = accountNo.PadRight(14, '0'); // 補足14位
                    }

                    var accountChars = accountNo.ToCharArray();

                    // 員工立帳局號 (前6位) = 6欄
                    for (int i = 0; i < 6; i++)
                    {
                        table.Cell().Element(DetailCellStyle).Text(i < accountChars.Length ? accountChars[i].ToString() : "").FontSize(8).AlignCenter();
                    }
                    // 檢號 (員工立帳局號的檢號) = 1欄
                    table.Cell().Element(DetailCellStyle).Text("-").FontSize(8).AlignCenter();

                    // 存簿帳號 (第7-13位) = 7欄
                    for (int i = 6; i < 13; i++)
                    {
                        table.Cell().Element(DetailCellStyle).Text(i < accountChars.Length ? accountChars[i].ToString() : "").FontSize(8).AlignCenter();
                    }

                    // 檢號 (存簿帳號的檢號) = 1欄
                    table.Cell().Element(DetailCellStyle).Text("-").FontSize(8).AlignCenter();

                    // cr[13] (第14位) = 1欄
                    table.Cell().Element(DetailCellStyle).Text(accountChars.Length > 13 ? accountChars[13].ToString() : "").FontSize(8).AlignCenter();

                    // 戶名 = 1欄
                    table.Cell().Element(DetailCellStyle).Text(item.CollecName ?? "").FontSize(8).AlignCenter();

                    // 身分證統一編號 (10位) = 10欄
                    var idNo = (item.CollectId ?? "").Trim().ToUpper();
                    if (idNo.Length == 8)
                        idNo = idNo.PadLeft(10, ' ');
                    var idChars = idNo.ToCharArray();
                    for (int i = 0; i < 10; i++)
                    {
                        table.Cell().Element(DetailCellStyle).Text(i < idChars.Length ? idChars[i].ToString() : "").FontSize(8).AlignCenter();
                    }

                    // 存款金額 (8位，對應千萬到元) = 8欄
                    var amount = (item.RemitPrice ?? 0).ToString().PadLeft(8, '0');
                    var amountChars = amount.ToCharArray();
                    for (int i = 0; i < 8; i++)
                    {
                        table.Cell().Element(DetailCellStyle).Text(amountChars[i].ToString()).FontSize(8).AlignCenter();
                    }
                }

                // 補足空白行到20行 - 按照Fun.cs的結構
                var emptyRows = Math.Max(0, 20 - pageData.Count);
                for (int row = 0; row < emptyRows; row++)
                {
                    for (int col = 0; col < 35; col++) // 35欄位
                    {
                        table.Cell().Element(DetailCellStyle).Text("").FontSize(8);
                    }
                }

                // 如果是最後一頁，加入總計行
                if (isLastPage)
                {
                    // 空白行
                    table.Cell().ColumnSpan(35u).Element(DetailCellStyle).Text("").FontSize(8);

                    // 委託總計第一行
                    table.Cell().RowSpan(2u).ColumnSpan(6u).Element(DetailCellStyle).Text("委託總計").FontSize(8).AlignLeft();
                    table.Cell().RowSpan(2u).ColumnSpan(6u).Element(DetailCellStyle).Text($"共存款{totalCount}次").FontSize(8).AlignLeft();
                    table.Cell().RowSpan(2u).ColumnSpan(6u).Element(DetailCellStyle).Text("新臺幣").FontSize(8).AlignLeft();

                    // 中間空白區域
                    int middleCols = 35 - 18 - 12; // 35總欄數 - 18個前面欄位 - 12個金額欄位
                    table.Cell().ColumnSpan((uint)middleCols).Element(DetailCellStyle).Text("").FontSize(8);

                    // 金額單位標題（最右方）
                    table.Cell().Element(DetailCellStyle).Text("億").FontSize(8).AlignCenter();
                    table.Cell().ColumnSpan(2u).Element(DetailCellStyle).Text("千萬").FontSize(8).AlignCenter();
                    table.Cell().ColumnSpan(2u).Element(DetailCellStyle).Text("佰萬").FontSize(8).AlignCenter();
                    table.Cell().ColumnSpan(2u).Element(DetailCellStyle).Text("十萬").FontSize(8).AlignCenter();
                    table.Cell().Element(DetailCellStyle).Text("萬").FontSize(8).AlignCenter();
                    table.Cell().Element(DetailCellStyle).Text("千").FontSize(8).AlignCenter();
                    table.Cell().Element(DetailCellStyle).Text("百").FontSize(8).AlignCenter();
                    table.Cell().Element(DetailCellStyle).Text("十").FontSize(8).AlignCenter();
                    table.Cell().Element(DetailCellStyle).Text("元").FontSize(8).AlignCenter();

                    // 委託總計第二行
                    // 前18欄被rowspan和colspan佔用，不需要再加cell

                    // 中間空白區域
                    table.Cell().ColumnSpan((uint)middleCols).Element(DetailCellStyle).Text("").FontSize(8);

                    // 總金額數字（最右方）
                    var totalAmountStr = totalAmount.ToString().PadLeft(9, '0');
                    var totalChars = totalAmountStr.ToCharArray();
                    table.Cell().Element(DetailCellStyle).Text(totalChars[0].ToString()).FontSize(8).AlignCenter(); // 億
                    table.Cell().ColumnSpan(2u).Element(DetailCellStyle).Text(totalChars[1].ToString()).FontSize(8).AlignCenter(); // 千萬
                    table.Cell().ColumnSpan(2u).Element(DetailCellStyle).Text(totalChars[2].ToString()).FontSize(8).AlignCenter(); // 佰萬
                    table.Cell().ColumnSpan(2u).Element(DetailCellStyle).Text(totalChars[3].ToString()).FontSize(8).AlignCenter(); // 十萬
                    table.Cell().Element(DetailCellStyle).Text(totalChars[4].ToString()).FontSize(8).AlignCenter(); // 萬
                    table.Cell().Element(DetailCellStyle).Text(totalChars[5].ToString()).FontSize(8).AlignCenter(); // 千
                    table.Cell().Element(DetailCellStyle).Text(totalChars[6].ToString()).FontSize(8).AlignCenter(); // 百
                    table.Cell().Element(DetailCellStyle).Text(totalChars[7].ToString()).FontSize(8).AlignCenter(); // 十
                    table.Cell().Element(DetailCellStyle).Text(totalChars[8].ToString()).FontSize(8).AlignCenter(); // 元
                }
            });

            // 如果是最後一頁，添加明細頁的說明文字和其他信息
            if (isLastPage)
            {
                column.Item().PaddingVertical(1);

                // 委託機構信息
                column.Item().Text("委託機構名稱:宜蘭縣政府    經辦:________________________________   主管:____________  \n委託機構地址:宜蘭市縣政北路1號       連絡電話:03-9251000-3379")
                    .FontSize(10);

                column.Item().PaddingVertical(1);

                // 「(非連線) 立帳局郵戳」表格區塊 - 修正版本
                column.Item().Table(table =>
                {
                    table.ColumnsDefinition(columns =>
                    {
                        columns.RelativeColumn(1);  // 「(非連線) 立帳局郵戳」文字區域
                        columns.RelativeColumn(6);  // 右側表格區域
                    });

                    // 左側：「(非連線) 立帳局郵戳」文字
                    table.Cell().Element(NoBorderCellStyle).Text("\n(非連線)\n立帳局郵戳").FontSize(11).AlignRight();

                    // 右側：立帳局郵戳表格
                    table.Cell().Element(container => container).Table(innerTable =>
                    {
                        innerTable.ColumnsDefinition(columns =>
                        {
                            columns.RelativeColumn(2);  // 項目欄位
                            columns.RelativeColumn(1);  // 次數欄位  
                            columns.RelativeColumn(4);  // 金額欄位（需要更寬來容納細格）
                        });

                        // 表頭第一行 - 項目
                        innerTable.Cell().Element(BorderCellStyle).Text("項目").FontSize(9).AlignCenter();

                        // 表頭第一行 - 次數
                        innerTable.Cell().Element(BorderCellStyle).Text("次數").FontSize(9).AlignCenter();

                        // 表頭第一行 - 金額（含細格）
                        innerTable.Cell().Element(BorderCellStyle).Table(amountTable =>
                        {
                            amountTable.ColumnsDefinition(amountColumns =>
                            {
                                // 根據附圖，金額欄位需要細分為多個小格
                                for (int i = 0; i < 12; i++) // 億千萬佰萬十萬萬千百十元角分 = 12格
                                {
                                    amountColumns.RelativeColumn(1);
                                }
                            });

                            // 金額標題行
                            amountTable.Cell().ColumnSpan(12).Element(BorderCellStyle).Text("金額").FontSize(8).AlignCenter();

                            // 金額細格標題
                            string[] amountLabels = { "億", "千", "萬", "佰", "萬", "十", "萬", "萬", "千", "百", "十", "元", "角", "分" };
                            // 注意：根據附圖顯示的格式，可能需要調整標籤
                            string[] actualLabels = { "億", "千", "萬", "佰", "萬", "十", "萬", "萬", "千", "百", "十", "元" };

                            for (int i = 0; i < 12; i++)
                            {
                                if (i < actualLabels.Length)
                                {
                                    amountTable.Cell().Element(BorderCellStyle).Text(actualLabels[i]).FontSize(6).AlignCenter();
                                }
                                else
                                {
                                    amountTable.Cell().Element(BorderCellStyle).Text(" ").FontSize(6).AlignCenter();
                                }
                            }
                        });

                        // column.Item().PaddingVertical(1);

                        // 實存存簿儲金行
                        innerTable.Cell().Element(BorderCellStyle).PaddingLeft(2).Text("實存存簿儲金").FontSize(9).AlignLeft();
                        innerTable.Cell().Element(BorderCellStyle).Text(" ").FontSize(9);

                        // 實存存簿儲金的金額格
                        innerTable.Cell().Element(BorderCellStyle).Table(amountTable =>
                        {
                            amountTable.ColumnsDefinition(amountColumns =>
                            {
                                for (int i = 0; i < 12; i++)
                                {
                                    amountColumns.RelativeColumn(1);
                                }
                            });

                            // 12個空白格子供填寫
                            for (int i = 0; i < 12; i++)
                            {
                                amountTable.Cell().Element(BorderCellStyle).Text(" ").FontSize(8).AlignCenter();
                            }
                        });

                        // 送存劃撥儲金行
                        innerTable.Cell().Element(BorderCellStyle).PaddingLeft(2).Text("送存劃撥儲金").FontSize(9).AlignLeft();
                        innerTable.Cell().Element(BorderCellStyle).Text(" ").FontSize(9);

                        // 送存劃撥儲金的金額格
                        innerTable.Cell().Element(BorderCellStyle).Table(amountTable =>
                        {
                            amountTable.ColumnsDefinition(amountColumns =>
                            {
                                for (int i = 0; i < 12; i++)
                                {
                                    amountColumns.RelativeColumn(1);
                                }
                            });

                            // 12個空白格子供填寫
                            for (int i = 0; i < 12; i++)
                            {
                                amountTable.Cell().Element(BorderCellStyle).Text(" ").FontSize(8).AlignCenter();
                            }
                        });
                    });
                });
            }
        }

        private static IContainer DetailCellStyle(IContainer container)
        {
            return container
                .Border(0.5f)
                .BorderColor(Colors.Grey.Lighten2)
                .Padding(1)
                .MinHeight(15);
        }

        private static IContainer NoBorderCellStyle(IContainer container)
        {
            return container
                .Padding(2)
                .MinHeight(15);
        }

        private static void GenerateRow2Cell4SubTable(IContainer container)
        {
            container.Table(table =>
            {
                // 按照Fun.cs genRow2Cell4標準：5欄位，相對寬度為 [4, 1, 1, 4, 1]
                table.ColumnsDefinition(columns =>
                {
                    columns.RelativeColumn(4);
                    columns.RelativeColumn(1);
                    columns.RelativeColumn(1);
                    columns.RelativeColumn(4);
                    columns.RelativeColumn(1);
                });

                // 第一行
                table.Cell().Element(BorderCellStyle).Text("媒體").FontSize(9);
                table.Cell().Element(BorderCellStyle).Text("").FontSize(9);
                table.Cell().RowSpan(4).Element(BorderCellStyle).Text("非媒體").FontSize(9);
                table.Cell().Element(BorderCellStyle).Text("資料中心代登").FontSize(9);
                table.Cell().Element(BorderCellStyle).Text("").FontSize(9);

                // 第二行
                table.Cell().Element(BorderCellStyle).Text("本局磁片傳輸").FontSize(9);
                table.Cell().Element(BorderCellStyle).Text("").FontSize(9);
                // 非媒體欄位跨行，不需要再添加
                table.Cell().Element(BorderCellStyle).Text("1512入機").FontSize(9);
                table.Cell().Element(BorderCellStyle).Text("").FontSize(9);

                // 第三行
                table.Cell().Element(BorderCellStyle).Text("").FontSize(9);
                table.Cell().Element(BorderCellStyle).Text("").FontSize(9);
                // 非媒體欄位跨行，不需要再添加
                table.Cell().Element(BorderCellStyle).Text("後線PC預登").FontSize(9);
                table.Cell().Element(BorderCellStyle).Text("").FontSize(9);
            });
        }

        private byte[] GenerateSimplePostalReport(List<RemitedList> list, int conSno)
        {
            var totalAmount = list.Count;
            var totalCost = list.Sum(x => (decimal)(x.RemitPrice ?? 0));
            var searchId = conSno.ToString();
            var filename = $"CR{searchId.PadLeft(6, '0')}_POST";
            var memo = list.FirstOrDefault()?.RemitMemo ?? "";

            return Document.Create(container =>
            {
                container.Page(page =>
                {
                    page.Size(PageSizes.A4);
                    page.Margin(2, Unit.Centimetre);
                    page.DefaultTextStyle(x => x.FontFamily("Microsoft YaHei").FontSize(12));

                    page.Content().Column(column =>
                    {
                        column.Item().Text("委託郵局代存員工薪資總表(408)").FontSize(18).Bold().AlignCenter();
                        column.Item().PaddingVertical(20);

                        column.Item().Text($"檔名: {filename}");
                        column.Item().Text($"撥存總額: {totalCost:N0}");
                        column.Item().Text($"總筆數: {totalAmount}");
                        column.Item().Text($"備註: {memo}");

                        column.Item().PaddingVertical(20);

                        // 簡化的明細列表
                        column.Item().Table(table =>
                        {
                            table.ColumnsDefinition(columns =>
                            {
                                columns.ConstantColumn(50);   // 序號
                                columns.RelativeColumn(2);   // 戶名
                                columns.ConstantColumn(100); // 帳號
                                columns.ConstantColumn(80);  // 金額
                            });

                            table.Cell().Element(HeaderCellStyle).Text("序號");
                            table.Cell().Element(HeaderCellStyle).Text("戶名");
                            table.Cell().Element(HeaderCellStyle).Text("帳號");
                            table.Cell().Element(HeaderCellStyle).Text("金額");

                            for (int i = 0; i < list.Count; i++)
                            {
                                var item = list[i];
                                table.Cell().Element(BorderCellStyle).Text((i + 1).ToString());
                                table.Cell().Element(BorderCellStyle).Text(item.CollecName ?? "");
                                table.Cell().Element(BorderCellStyle).Text(item.CollecAcc ?? "");
                                table.Cell().Element(BorderCellStyle).AlignRight().Text($"{(item.RemitPrice ?? 0):N0}");
                            }
                        });
                    });

                    page.Footer().AlignCenter().Text(text =>
                    {
                        text.Span($"{filename} - ");
                        text.CurrentPageNumber();
                        text.Span(" / ");
                        text.TotalPages();
                    });
                });
            }).GeneratePdf();
        }

        private static IContainer BorderCellStyle(IContainer container)
        {
            return container
                .Border(0.3f)
                .BorderColor(Colors.Black)
                .PaddingVertical(1)
                .PaddingHorizontal(1)
                .AlignCenter()
                .AlignMiddle()
                .DefaultTextStyle(x => x.FontSize(9)); // 添加預設字體大小
        }

        private static IContainer HeaderCellStyle(IContainer container)
        {
            return container
                .Border(0.3f)
                .BorderColor(Colors.Black)
                .Background(Colors.White)
                .PaddingVertical(3)
                .PaddingHorizontal(2)
                .AlignCenter()
                .AlignMiddle();
        }

        private static IContainer CellStyle(IContainer container)
        {
            return container
                .PaddingVertical(3)
                .PaddingHorizontal(5)
                .AlignLeft()
                .AlignMiddle();
        }

        private byte[] GenerateTWBankReportAdvanced(List<RemitedList> data, int conSno)
        {
            var totalAmount = data.Count;
            var totalCost = data.Sum(x => (decimal)(x.RemitPrice ?? 0));
            var totalFee = data.Count(x => ShouldChargeFee(x.IfFee)) * 30;
            var conOrga = "相關單位";
            var searchId = conSno.ToString();
            var filename = $"CR{searchId.PadLeft(6, '0')}_TWBANK";
            var batchNum = GenerateBatchNumber();
            var conMemo = data.FirstOrDefault()?.RemitMemo ?? "113全國學生音樂比賽獎金";

            const int recordsPerPage = 18;
            var totalPages = Math.Max(1, (int)Math.Ceiling((double)data.Count / recordsPerPage));

            return Document.Create(container =>
            {
                container.Page(page =>
                {
                    page.Size(PageSizes.A4);
                    page.Margin(50, Unit.Point);
                    page.MarginBottom(80, Unit.Point);
                    page.DefaultTextStyle(x => x.FontFamily("Microsoft YaHei").FontSize(10));

                    page.Content().Element(container =>
                    {
                        container.Column(column =>
                        {
                            for (int copyIndex = 0; copyIndex < 3; copyIndex++)
                            {
                                for (int pageIndex = 0; pageIndex < totalPages; pageIndex++)
                                {
                                    if (copyIndex > 0 || pageIndex > 0)
                                    {
                                        column.Item().PageBreak();
                                    }

                                    var currentPageNumber = pageIndex + 1;
                                    var startIndex = pageIndex * recordsPerPage;
                                    var endIndex = Math.Min(startIndex + recordsPerPage, data.Count);
                                    var pageData = data.Skip(startIndex).Take(endIndex - startIndex).ToList();

                                    column.Item().Column(col =>
                                    {
                                        col.Item().Row(row =>
                                        {
                                            row.RelativeItem(2).Text("整批匯款資料清單");
                                            row.RelativeItem(1).Text("");
                                            row.RelativeItem(2).Text($"頁次：{currentPageNumber}/{totalPages}");
                                        });

                                        col.Item().Row(row =>
                                        {
                                            row.RelativeItem(2).Text($"匯款人:宜府{conOrga}");
                                            row.RelativeItem(1).Text("");
                                            row.RelativeItem(2).Text($"製表日：{DateTime.Now.Year - 1911}年{DateTime.Now.Month}月{DateTime.Now.Day}日");
                                        });

                                        col.Item().Row(row =>
                                        {
                                            row.RelativeItem(2).Text("匯款行:0040222[台灣銀行宜蘭分行]");
                                            row.RelativeItem(1).Text("");
                                            row.RelativeItem(2).Text($"用途：{conMemo}");
                                        });

                                        col.Item().Row(row =>
                                        {
                                            row.RelativeItem(2).Text("匯款日: 年 月 日");
                                            row.RelativeItem(1).Text("");
                                            row.RelativeItem(2).Text($"批號：{batchNum}");
                                        });

                                        col.Item().Row(row =>
                                        {
                                            row.RelativeItem(2).Text("");
                                            row.RelativeItem(1).Text("");
                                            row.RelativeItem(2).Text($"檔名：{filename}");
                                        });

                                        col.Item().PaddingVertical(10);

                                        col.Item().Row(row =>
                                        {
                                            row.ConstantItem(35).Text("序號").AlignCenter();
                                            row.ConstantItem(55).Text("解款行").AlignCenter();
                                            row.ConstantItem(100).Text("帳號").AlignCenter();
                                            row.ConstantItem(55).Text("匯款金額").AlignCenter();
                                            row.ConstantItem(35).Text("手續費").AlignCenter();
                                            row.ConstantItem(60).Text("合計\n(帳款金額)").AlignCenter();
                                            row.RelativeItem(3).Text("收款人姓名\n備註").AlignCenter();
                                        });

                                        col.Item().Text("-------------------------------------------------------------------------------------");

                                        for (int i = 0; i < pageData.Count; i++)
                                        {
                                            var item = pageData[i];
                                            var currentPrice = (decimal)(item.RemitPrice ?? 0);
                                            var fee = ShouldChargeFee(item.IfFee) ? 30 : 0;
                                            var netAmount = currentPrice - fee;
                                            var memo = item.RemitMemo ?? "";
                                            if (memo.Length > 35)
                                                memo = memo.Substring(0, 35) + "..";

                                            col.Item().Row(row =>
                                            {
                                                row.ConstantItem(35).Text((startIndex + i + 1).ToString()).AlignCenter();
                                                row.ConstantItem(55).Text(item.CollectNo ?? "").AlignCenter();
                                                row.ConstantItem(100).Text((item.CollecAcc ?? "").Replace("-", ""));
                                                row.ConstantItem(55).Text($"{netAmount:N0}").AlignRight();
                                                row.ConstantItem(35).Text(fee.ToString()).AlignRight();
                                                row.ConstantItem(60).Text($"{currentPrice:N0}").AlignRight();
                                                row.RelativeItem(3).Text($"({item.CollecName}){memo}");
                                            });
                                        }

                                        if (pageIndex == totalPages - 1)
                                        {
                                            col.Item().PaddingVertical(5);

                                            col.Item().Row(row =>
                                            {
                                                row.RelativeItem(3).Text($"資料總筆數:{totalAmount}       資料總金額:").AlignRight();
                                                row.ConstantItem(55).Text($"{totalCost - totalFee:N0}").AlignRight();
                                                row.ConstantItem(35).Text($"{totalFee}").AlignRight();
                                                row.ConstantItem(60).Text($"{totalCost:N0}").AlignRight();
                                                row.RelativeItem(1).Text("");
                                            });

                                            col.Item().PaddingVertical(5);

                                            col.Item().Row(row =>
                                            {
                                                row.RelativeItem(1).Text("經辦:");
                                                row.RelativeItem(1).Text("單位主管:");
                                                row.RelativeItem(1).Text("製表人:");
                                                row.RelativeItem(1).Text("電話:");
                                            });

                                            col.Item().PaddingVertical(3);

                                            col.Item().Row(row =>
                                            {
                                                row.RelativeItem(2).Text("身分證字號:");
                                                row.RelativeItem(2).Text("電子檔傳送日期:");
                                            });
                                        }
                                    });
                                }
                            }
                        });
                    });

                    page.Foreground().PaddingBottom(15).PaddingRight(20).AlignBottom().AlignRight().Column(footerCol =>
                    {
                        var qrText = filename;
                        try
                        {
                            if (System.Runtime.InteropServices.RuntimeInformation.IsOSPlatform(System.Runtime.InteropServices.OSPlatform.Windows))
                            {
                                var barcodeBytes = _barcodeService.GenerateCode128Barcode(qrText);
                                
                                if (barcodeBytes != null && barcodeBytes.Length > 0)
                                {
                                    footerCol.Item().Width(200).Image(barcodeBytes).FitWidth();
                                    return;
                                }
                            }
                        }
                        catch
                        {
                            // 如果條碼生成失敗，使用ASCII備用方案
                        }

                        // 備用方案：使用ASCII條碼
                        var barcodeAscii = _barcodeService.GenerateBarcodeAscii(qrText);
                        footerCol.Item().Width(200).Text(barcodeAscii).FontFamily("Courier New").FontSize(6);
                        footerCol.Item().Width(200).PaddingTop(2).Text(qrText).FontSize(9).AlignCenter();
                    });
                });
            }).GeneratePdf();
        }

        private string GenerateBatchNumber()
        {
            var today = DateTime.Now;
            var dateStr = $"{today.Year - 1911:D2}{today.Month:D2}{today.Day:D2}";
            return $"{dateStr}6101";
        }

        public async Task<int?> CopyByConSnoAsync(int conSno)
        {
            var items = await _context.RemitedList.Where(r => r.ConSno == conSno).ToListAsync();
            if (!items.Any()) return null;
            var newConSno = await _context.RemitedList.MaxAsync(r => r.ConSno) ?? 0;
            newConSno++;
            var newItems = items.Select(r => new RemitedList
            {
                CollectNo = r.CollectNo,
                CollecAcc = r.CollecAcc,
                CollecName = r.CollecName,
                CollectId = r.CollectId,
                RemitPrice = r.RemitPrice,
                RemitMemo = r.RemitMemo,
                ConSno = newConSno,
                ConPer = r.ConPer,
                ConUnit = r.ConUnit,
                ConDate = DateTime.Now,
                Kindno = r.Kindno,
                CashDate = null,
                BatchNum = r.BatchNum,
                IfFee = r.IfFee
            }).ToList();
            await _context.RemitedList.AddRangeAsync(newItems);
            await _context.SaveChangesAsync();
            return newConSno;
        }
    }
}
