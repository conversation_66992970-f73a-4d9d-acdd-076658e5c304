/*!*  filename: sf-rating.min.js 
*     version : 26.1.41 
*     Copyright Syncfusion Inc. 2001 - 2024. All rights reserved. 
*     Use of this code is subject to the terms of our license.
*     A copy of the current license can be obtained at any time by e-mailing 
*     <EMAIL>. Any infringement will be prosecuted under 
*     applicable laws. 
*/
(window.webpackJsonp=window.webpackJsonp||[]).push([[49],{"./bundles/sf-rating.js":function(t,e,i){"use strict";i.r(e);i("./modules/sf-rating.js")},"./modules/sf-rating.js":function(t,e){window.sfBlazor=window.sfBlazor||{},window.sfBlazor.Rating=function(){"use strict";var t=function(){function t(t){window.sfBlazor=window.sfBlazor,this.isResetAdded=!1,this.isTouchSelected=!1,this.updateContext(t),window.sfBlazor.setCompInstance(this),this.listElement=this.element.querySelector(".e-rating-item-list"),this.getElements(),this.bindEvent(),this.updateReset()}return t.prototype.updateContext=function(t){sf.base.extend(this,this,t)},t.prototype.getElements=function(){this.containerElements=this.element.querySelectorAll(".e-rating-item-container"),this.resetElement=this.allowReset?this.element.querySelector(".e-reset"):null,this.ratingLabel=this.showLabel?this.element.querySelector(".e-rating-label"):null},t.prototype.updateReset=function(t){void 0===t&&(t=!1),(t||this.isResetAdded!==this.allowReset)&&(this.resetElement=this.allowReset?this.element.querySelector(".e-reset"):null,!t&&this.allowReset?(sf.base.EventHandler.add(this.resetElement,"click",this.resetClickHandler,this),sf.base.EventHandler.add(this.resetElement,"keydown",this.resetKeyActionHandler.bind(this),this)):this.isResetAdded&&(sf.base.EventHandler.remove(this.resetElement,"click",this.resetClickHandler.bind(this)),sf.base.EventHandler.remove(this.resetElement,"keydown",this.resetKeyActionHandler.bind(this))),this.isResetAdded=this.allowReset)},t.prototype.bindItemEvent=function(t,e){var i=this;sf.base.EventHandler.add(t,"mousemove",(function(t){return i.mouseMoveHandler(e+1,t)}),this),sf.base.EventHandler.add(t,"mouseleave",this.mouseLeaveHandler.bind(this),this),sf.base.EventHandler.add(t,"click",this.clickHandler.bind(this),this)},t.prototype.bindEvent=function(){sf.base.EventHandler.add(this.listElement,"keydown",this.keyActionHandler.bind(this),this),sf.base.EventHandler.add(this.listElement,"touchmove",this.touchMoveHandler.bind(this),this),sf.base.EventHandler.add(this.listElement,"touchend",this.touchEndHandler.bind(this),this),sf.base.EventHandler.add(this.listElement,"touchstart",this.touchStartHandler.bind(this),this);for(var t=0;t<this.containerElements.length;t++)this.bindItemEvent(this.containerElements[t],t)},t.prototype.unBindItemEvent=function(t,e){var i=this;sf.base.EventHandler.remove(t,"mousemove",(function(t){return i.mouseMoveHandler(e+1,t)})),sf.base.EventHandler.remove(t,"mouseleave",this.mouseLeaveHandler.bind(this)),sf.base.EventHandler.remove(t,"click",this.clickHandler.bind(this))},t.prototype.unBindEvent=function(){sf.base.EventHandler.remove(this.listElement,"keydown",this.keyActionHandler),sf.base.EventHandler.remove(this.listElement,"touchmove",this.touchMoveHandler.bind(this)),sf.base.EventHandler.remove(this.listElement,"touchend",this.touchEndHandler.bind(this)),sf.base.EventHandler.remove(this.listElement,"touchstart",this.touchStartHandler.bind(this));for(var t=0;t<this.containerElements.length;t++)this.unBindItemEvent(this.containerElements[t],t)},t.prototype.getRatingValue=function(t,e){return this.enableSingleSelection&&0==this.precision?t==e+1?1:0:t>=e+1?1:t<e?0:t-e},t.prototype.UpdateItemValue=function(){for(var t=0;t<this.itemsCount;t++){this.containerElements[t].classList.remove("e-rating-selected","e-rating-intermediate","e-selected-value");var e=this.getRatingValue(this.currentValue,t);1==e?this.containerElements[t].classList.add("e-rating-selected"):e>0?this.containerElements[t].classList.add("e-rating-intermediate"):0===this.precision&&t<this.value&&!this.enableSingleSelection&&this.containerElements[t].classList.add("e-selected-value"),this.containerElements[t].style.setProperty("--rating-value",100*e+"%"),this.containerElements[t].classList[0===this.value&&0===t||this.value===t+1||e>0&&e<1?"add":"remove"]("e-rating-focus")}this.allowReset&&this.resetElement.classList[this.value>this.min?"remove":"add"]("e-disabled")},t.prototype.updateCurrentValue=function(t){this.currentValue=t,this.UpdateItemValue(),this.updateLabel()},t.prototype.updateLabel=function(){this.showLabel&&!this.labelTemplate&&(this.ratingLabel.textContent=this.currentValue+" / "+this.itemsCount)},t.prototype.mouseMoveHandler=function(t,e){if(!this.preventAction){this.isTouchSelected&&(this.element.classList.remove("e-rating-touch"),this.isTouchSelected=!1);var i=t;0!=this.precision&&(i=e.offsetX/this.containerElements[t-1].clientWidth,i=this.enableRtl?1-i:i,2==this.precision?i=i<=.25?.25:i<=.5?.5:i<.75?.75:1:1==this.precision?i=i<=.5?.5:1:3==this.precision&&(i=0==(i=Math.ceil(10*i)/10)?.1:i),i=i+t-1),i=this.validateValue(i);var s=e.target.classList.contains("e-rating-item-container")?e.target:e.target.closest(".e-rating-item-container");(this.currentValue!=i||this.value==i&&this.showTooltip&&!this.element.querySelector(".e-rating-tooltip").classList.contains("e-show-tooltip"))&&(this.updateCurrentValue(i),this.showTooltip&&s&&!this.tooltipTemplate&&this.openTooltip(s),(this.callMouseMoveHandler||this.tooltipTemplate||this.labelTemplate)&&this.dotNetRef.invokeMethodAsync("MouseMoveHandler",this.currentValue))}},t.prototype.openTooltip=function(t){var e=this.element.querySelector(".e-rating-tooltip");e.querySelector(".e-tip-content").innerHTML=this.currentValue.toString(),t.appendChild(e),e.classList.add("e-show-tooltip")},t.prototype.closeTooltip=function(){this.element.querySelector(".e-rating-tooltip").classList.remove("e-show-tooltip")},t.prototype.validateValue=function(t){return t=0==this.precision?Math.round(t):1==this.precision?Math.round(2*t)/2:2==this.precision?Math.round(4*t)/4:Math.round(10*t)/10},t.prototype.touchMoveHandler=function(t){if(!this.preventAction){this.isTouchSelected||(this.element.classList.add("e-rating-touch"),this.isTouchSelected=!0),this.element.classList.add("e-touch-select");var e=this.listElement.getBoundingClientRect(),i=(t.touches[0].clientX-e.x)/e.width*this.itemsCount;i=(i=this.enableRtl?this.itemsCount-i:i)<this.min?this.min:i>this.itemsCount?this.itemsCount:i;var s=0===(i=this.validateValue(i))?null:this.containerElements[Math.ceil(i)-1];(this.currentValue!=i||this.value==i&&this.showTooltip&&!this.element.querySelector(".e-rating-tooltip").classList.contains("e-show-tooltip"))&&(this.value=i,this.updateCurrentValue(i),this.showTooltip&&!this.tooltipTemplate&&(s?this.openTooltip(s):this.closeTooltip()),this.dotNetRef.invokeMethodAsync("TouchEventHandler",this.currentValue,!0))}},t.prototype.clickHandler=function(){this.preventAction||(this.value=this.min>0&&this.currentValue<this.min?this.min:this.currentValue,this.updateCurrentValue(this.value),this.dotNetRef.invokeMethodAsync("SetValue",this.value))},t.prototype.mouseLeaveHandler=function(){this.preventAction||(this.showTooltip&&this.closeTooltip(),this.updateCurrentValue(this.value),this.dotNetRef.invokeMethodAsync("MouseOutHandler",this.currentValue))},t.prototype.touchStartHandler=function(){for(var t=document.querySelectorAll(".e-rating-item-container"),e=0;e<t.length;e++){var i=t[e].querySelector(".e-rating-tooltip");i&&i.classList.contains("e-show-tooltip")&&i.classList.remove("e-show-tooltip")}},t.prototype.touchEndHandler=function(){this.element.classList.remove("e-touch-select"),this.showTooltip&&(this.closeTooltip(),this.dotNetRef.invokeMethodAsync("TouchEventHandler",this.currentValue,!1))},t.prototype.resetClickHandler=function(){this.preventAction||this.value>this.min&&(this.value=this.min,this.updateCurrentValue(this.value),this.dotNetRef.invokeMethodAsync("SetValue",this.min))},t.prototype.keyActionHandler=function(t){switch(t.key){case"ArrowUp":this.handleNavigation(!0);break;case"ArrowDown":this.handleNavigation(!1);break;case"ArrowLeft":this.handleNavigation(this.enableRtl);break;case"ArrowRight":this.handleNavigation(!this.enableRtl)}},t.prototype.resetKeyActionHandler=function(t){switch(t.key){case" ":this.resetClickHandler()}},t.prototype.handleNavigation=function(t){if((!t&&this.value>this.min||t&&this.value<this.itemsCount)&&!this.preventAction){var e=0==this.precision?1:1==this.precision?.5:2==this.precision?.25:Math.round(1)/10;e=t?this.value+e:this.value-e,this.value=this.validateValue(e),this.updateCurrentValue(this.value),this.dotNetRef.invokeMethodAsync("SetValue",this.value)}},t.prototype.destroy=function(){this.unBindEvent(),this.updateReset(!0)},t}();return{initialize:function(e){e.dataId&&new t(e)},updateRatingProps:function(t){if(t.dataId){var e=window.sfBlazor.getCompInstance(t.dataId);e.updateContext(t),e.getElements(),e.updateReset()}},destroy:function(t){t&&window.sfBlazor.getCompInstance(t).destroy()}}}()}}]);(async()=>{await import(`${document.baseURI}_content/Syncfusion.Blazor/scripts/syncfusion-blazor-base.min.js?v=26.1.undefined`).then(()=>{sfBlazor.loadDependencies('sfrating');})})();