// 計畫進度管理相關的 JavaScript 函數

window.downloadFile = (fileName, url) => {
    const link = document.createElement('a');
    link.href = url;
    link.download = fileName;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
};

// 確認對話框
window.confirmDialog = (message) => {
    return confirm(message);
};

// 警告對話框
window.alertDialog = (message) => {
    alert(message);
};
