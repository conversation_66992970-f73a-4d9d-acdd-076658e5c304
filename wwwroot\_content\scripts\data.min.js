(window.webpackJsonp=window.webpackJsonp||[]).push([[1],{"./bundles/data.js":function(e,t,r){"use strict";r.r(t);r("./modules/data.js")},"./modules/data.js":function(e,t){function r(e){return(r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}window.sf=window.sf||{},window.sf.data=function(e){"use strict";var t,n=function(){function e(e){return this.subQuery=null,this.isChild=!1,this.distincts=[],this.queries=[],this.key="",this.fKey="","string"==typeof e?this.fromTable=e:e&&e instanceof Array&&(this.lookups=e),this.expands=[],this.sortedColumns=[],this.groupedColumns=[],this.subQuery=null,this.isChild=!1,this.params=[],this.lazyLoad=[],this}return e.prototype.setKey=function(e){return this.key=e,this},e.prototype.using=function(e){return this.dataManager=e,this},e.prototype.execute=function(e,t,r,n){return(e=e||this.dataManager)?e.executeQuery(this,t,r,n):a.throwError('Query - execute() : dataManager needs to be is set using "using" function or should be passed as argument')},e.prototype.executeLocal=function(e){return(e=e||this.dataManager)?e.executeLocal(this):a.throwError('Query - executeLocal() : dataManager needs to be is set using "using" function or should be passed as argument')},e.prototype.clone=function(){var t=new e;return t.queries=this.queries.slice(0),t.key=this.key,t.isChild=this.isChild,t.dataManager=this.dataManager,t.fromTable=this.fromTable,t.params=this.params.slice(0),t.expands=this.expands.slice(0),t.sortedColumns=this.sortedColumns.slice(0),t.groupedColumns=this.groupedColumns.slice(0),t.subQuerySelector=this.subQuerySelector,t.subQuery=this.subQuery,t.fKey=this.fKey,t.isCountRequired=this.isCountRequired,t.distincts=this.distincts.slice(0),t.lazyLoad=this.lazyLoad.slice(0),t},e.prototype.from=function(e){return this.fromTable=e,this},e.prototype.addParams=function(e,t){return"function"==typeof t?this.params.push({key:e,fn:t}):this.params.push({key:e,value:t}),this},e.prototype.distinct=function(e){return this.distincts="string"==typeof e?[].slice.call([e],0):e.slice(0),this},e.prototype.expand=function(e){return this.expands="string"==typeof e?[].slice.call([e],0):e.slice(0),this},e.prototype.where=function(e,t,r,n,s,a){t=t?t.toLowerCase():null;var i=null;return"string"==typeof e?i=new o(e,t,r,n,s,a):e instanceof o&&(i=e),this.queries.push({fn:"onWhere",e:i}),this},e.prototype.search=function(e,t,r,n,o){"string"==typeof t&&(t=[t]),r&&"none"!==r||(r="contains");var s=a.fnOperators[r];return this.queries.push({fn:"onSearch",e:{fieldNames:t,operator:r,searchKey:e,ignoreCase:n,ignoreAccent:o,comparer:s}}),this},e.prototype.sortBy=function(e,t,r){return this.sortByForeignKey(e,t,r)},e.prototype.sortByForeignKey=function(t,r,n,o){var s,i,u=sf.base.isNullOrUndefined(o)?"ascending":o;if("string"==typeof t&&a.endsWith(t.toLowerCase()," desc")&&(t=t.replace(/ desc$/i,""),r="descending"),r&&"string"!=typeof r||(u=r?r.toLowerCase():"ascending",r=a.fnSort(r)),n){s=e.filterQueries(this.queries,"onSortBy");for(var c=0;c<s.length;c++)if("string"==typeof(i=s[c].e.fieldName)){if(i===t)return this}else if(i instanceof Array)for(var l=0;l<i.length;l++)if(i[l]===t||t.toLowerCase()===i[l]+" desc")return this}return this.queries.push({fn:"onSortBy",e:{fieldName:t,comparer:r,direction:u}}),this},e.prototype.sortByDesc=function(e){return this.sortBy(e,"descending")},e.prototype.group=function(e,t,r){return this.sortBy(e,null,!0),this.queries.push({fn:"onGroup",e:{fieldName:e,comparer:t||null,format:r||null}}),this},e.prototype.page=function(e,t){return this.queries.push({fn:"onPage",e:{pageIndex:e,pageSize:t}}),this},e.prototype.range=function(e,t){return this.queries.push({fn:"onRange",e:{start:e,end:t}}),this},e.prototype.take=function(e){return this.queries.push({fn:"onTake",e:{nos:e}}),this},e.prototype.skip=function(e){return this.queries.push({fn:"onSkip",e:{nos:e}}),this},e.prototype.select=function(e){return"string"==typeof e&&(e=[].slice.call([e],0)),this.queries.push({fn:"onSelect",e:{fieldNames:e}}),this},e.prototype.hierarchy=function(e,t){return this.subQuerySelector=t,this.subQuery=e,this},e.prototype.foreignKey=function(e){return this.fKey=e,this},e.prototype.requiresCount=function(){return this.isCountRequired=!0,this},e.prototype.aggregate=function(e,t){return this.queries.push({fn:"onAggregates",e:{field:t,type:e}}),this},e.filterQueries=function(e,t){return e.filter((function(e){return e.fn===t}))},e.filterQueryLists=function(e,t){for(var r=e.filter((function(e){return-1!==t.indexOf(e.fn)})),n={},o=0;o<r.length;o++)n[r[o].fn]||(n[r[o].fn]=r[o].e);return n},e}(),o=function(){function e(t,r,n,o,s,i){return void 0===o&&(o=!1),this.ignoreAccent=!1,this.isComplex=!1,"string"==typeof t?(this.field=t,this.operator=r.toLowerCase(),this.value=n,this.matchCase=i,this.ignoreCase=o,this.ignoreAccent=s,this.isComplex=!1,this.comparer=a.fnOperators.processOperator(this.operator)):(t instanceof e&&n instanceof e||n instanceof Array)&&(this.isComplex=!0,this.condition=r.toLowerCase(),this.predicates=[t],this.matchCase=t.matchCase,this.ignoreCase=t.ignoreCase,this.ignoreAccent=t.ignoreAccent,n instanceof Array?[].push.apply(this.predicates,n):this.predicates.push(n)),this}return e.and=function(){for(var t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];return e.combinePredicates([].slice.call(t,0),"and")},e.prototype.and=function(t,r,n,o,s){return e.combine(this,t,r,n,"and",o,s)},e.or=function(){for(var t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];return e.combinePredicates([].slice.call(t,0),"or")},e.prototype.or=function(t,r,n,o,s){return e.combine(this,t,r,n,"or",o,s)},e.ornot=function(){for(var t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];return e.combinePredicates([].slice.call(t,0),"or not")},e.prototype.ornot=function(t,r,n,o,s){return e.combine(this,t,r,n,"ornot",o,s)},e.andnot=function(){for(var t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];return e.combinePredicates([].slice.call(t,0),"and not")},e.prototype.andnot=function(t,r,n,o,s){return e.combine(this,t,r,n,"andnot",o,s)},e.fromJson=function(e){if(e instanceof Array){for(var t=[],r=0,n=e.length;r<n;r++)t.push(this.fromJSONData(e[r]));return t}var o=e;return this.fromJSONData(o)},e.prototype.validate=function(e){var t,r,n=this.predicates?this.predicates:[];if(!this.isComplex&&this.comparer)return this.condition&&-1!==this.condition.indexOf("not")?(this.condition=""===this.condition.split("not")[0]?void 0:this.condition.split("not")[0],!this.comparer.call(this,a.getObject(this.field,e),this.value,this.ignoreCase,this.ignoreAccent)):this.comparer.call(this,a.getObject(this.field,e),this.value,this.ignoreCase,this.ignoreAccent);r=this.condition&&-1!==this.condition.indexOf("not")?-1!==this.condition.indexOf("and"):"and"===this.condition;for(var o=0;o<n.length;o++)if(o>0&&this.condition&&-1!==this.condition.indexOf("not")&&(n[o].condition=n[o].condition?n[o].condition+"not":"not"),t=n[o].validate(e),r){if(!t)return!1}else if(t)return!0;return r},e.prototype.toJson=function(){var e,t;if(this.isComplex){e=[],t=this.predicates;for(var r=0;r<t.length;r++)e.push(t[r].toJson())}return{isComplex:this.isComplex,field:this.field,operator:this.operator,value:this.value,ignoreCase:this.ignoreCase,ignoreAccent:this.ignoreAccent,condition:this.condition,predicates:e,matchCase:this.matchCase}},e.combinePredicates=function(t,r){if(1===t.length){if(!(t[0]instanceof Array))return t[0];t=t[0]}return new e(t[0],r,t.slice(1))},e.combine=function(t,r,n,o,s,i,u){return r instanceof e?e[s](t,r):"string"==typeof r?e[s](t,new e(r,n,o,i,u)):a.throwError("Predicate - "+s+" : invalid arguments")},e.fromJSONData=function(t){for(var r=t.predicates||[],n=r.length,o=[],s=0;s<n;s++)o.push(this.fromJSONData(r[s]));return t.isComplex?new e(o[0],t.condition,o.slice(1)):new e(t.field,t.operator,t.value,t.ignoreCase,t.ignoreAccent)},e}(),s={GroupGuid:"{271bbba0-1ee7}"},a=function(){function e(){}return e.getValue=function(e,t){return"function"==typeof e?e.call(t||{}):e},e.endsWith=function(e,t){return e.slice&&e.slice(-t.length)===t},e.notEndsWith=function(e,t){return e.slice&&e.slice(-t.length)!==t},e.startsWith=function(e,t){return e.slice(0,t.length)===t},e.notStartsWith=function(e,t){return e.slice(0,t.length)!==t},e.wildCard=function(e,t){var r,n;if(-1!==t.indexOf("[")&&(t=t.split("[").join("[[]")),-1!==t.indexOf("(")&&(t=t.split("(").join("[(]")),-1!==t.indexOf(")")&&(t=t.split(")").join("[)]")),-1!==t.indexOf("\\")&&(t=t.split("\\").join("[\\\\]")),-1!==t.indexOf("*")){"*"!==t.charAt(0)&&(t="^"+t),"*"!==t.charAt(t.length-1)&&(t+="$"),r=t.split("*");for(var o=0;o<r.length;o++)-1===r[o].indexOf(".")?r[o]=r[o]+".*":r[o]=r[o]+"*";t=r.join("")}return-1===t.indexOf("%3f")&&-1===t.indexOf("?")||(n=-1!==t.indexOf("%3f")?t.split("%3f"):t.split("?"),t=n.join(".")),new RegExp(t,"g").test(e)},e.like=function(t,r){return-1!==r.indexOf("%")&&("%"===r.charAt(0)&&r.lastIndexOf("%")<2?(r=r.substring(1,r.length),e.startsWith(e.toLowerCase(t),e.toLowerCase(r))):"%"===r.charAt(r.length-1)&&r.indexOf("%")>r.length-3?(r=r.substring(0,r.length-1),e.endsWith(e.toLowerCase(t),e.toLowerCase(r))):r.lastIndexOf("%")!==r.indexOf("%")&&r.lastIndexOf("%")>r.indexOf("%")+1?(r=r.substring(r.indexOf("%")+1,r.lastIndexOf("%")),-1!==t.indexOf(r)):-1!==t.indexOf(r))},e.fnSort=function(t){return"ascending"===(t=t?e.toLowerCase(t):"ascending")?this.fnAscending:this.fnDescending},e.fnAscending=function(e,t){return sf.base.isNullOrUndefined(e)&&sf.base.isNullOrUndefined(t)||null==t?-1:"string"==typeof e?e.localeCompare(t):null==e?1:e-t},e.fnDescending=function(e,t){return sf.base.isNullOrUndefined(e)&&sf.base.isNullOrUndefined(t)?-1:null==t?1:"string"==typeof e?-1*e.localeCompare(t):null==e?-1:t-e},e.extractFields=function(e,t){for(var r={},n=0;n<t.length;n++)r=this.setValue(t[n],this.getObject(t[n],e),r);return r},e.select=function(e,t){for(var r=[],n=0;n<e.length;n++)r.push(this.extractFields(e[n],t));return r},e.group=function(t,r,n,o,a,i,u){o=o||1;var c=t;if(c.GroupGuid===s.GroupGuid){for(var l=function(e){if(sf.base.isNullOrUndefined(a))c[e].items=d.group(c[e].items,r,n,c.level+1,null,i,u),c[e].count=c[e].items.length;else{var t,o=a.filter((function(t){return t.key===c[e].key}));t=a.indexOf(o[0]),c[e].items=d.group(c[e].items,r,n,c.level+1,a[t].items,i,u),c[e].count=a[t].count}},d=this,p=0;p<c.length;p++)l(p);return c.childLevels+=1,c}var f={},h=[];h.GroupGuid=s.GroupGuid,h.level=o,h.childLevels=0,h.records=c;for(var g=function(e){var t=y.getVal(c,e,r);if(sf.base.isNullOrUndefined(i)||(t=i(t,r)),!f[t]&&(f[t]={key:t,count:0,items:[],aggregates:{},field:r},h.push(f[t]),!sf.base.isNullOrUndefined(a))){var o=a.filter((function(e){return e.key===f[t].key}));f[t].count=o[0].count}f[t].count=sf.base.isNullOrUndefined(a)?f[t].count+=1:f[t].count,(!u||u&&n.length)&&f[t].items.push(c[e])},y=this,v=0;v<c.length;v++)g(v);if(n&&n.length){var m=function(t){for(var r={},o=void 0,s=n,i=0;i<n.length;i++)if(o=e.aggregates[n[i].type],sf.base.isNullOrUndefined(a))o&&(r[s[i].field+" - "+s[i].type]=o(h[t].items,s[i].field));else{var u=a.filter((function(e){return e.key===h[t].key}));o&&(r[s[i].field+" - "+s[i].type]=o(u[0].items,s[i].field))}h[t].aggregates=r};for(v=0;v<h.length;v++)m(v)}if(u&&h.length&&n.length)for(v=0;v<h.length;v++)h[v].items=[];return c.length&&h||c},e.buildHierarchy=function(e,t,r,n,o){var s,a={};for(n.result&&(n=n.result),n.GroupGuid&&this.throwError("DataManager: Do not have support Grouping in hierarchy"),s=0;s<n.length;s++){(a[i=this.getObject(e,n[s])]||(a[i]=[])).push(n[s])}for(s=0;s<r.length;s++){var i=this.getObject(o||e,r[s]);r[s][t]=a[i]}},e.getFieldList=function(e,t,n){if(void 0===n&&(n=""),null==t)return this.getFieldList(e,[],n);for(var o=e,s=0,a=Object.keys(e);s<a.length;s++){var i=a[s];"object"!==r(o[i])||o[i]instanceof Array?t.push(n+i):this.getFieldList(o[i],t,n+i+".")}return t},e.getObject=function(e,t){if(!e)return t;if(t){if(-1===e.indexOf(".")){var r=e.charAt(0).toLowerCase()+e.slice(1),n=e.charAt(0).toUpperCase()+e.slice(1);return sf.base.isNullOrUndefined(t[e])?sf.base.isNullOrUndefined(t[r])?sf.base.isNullOrUndefined(t[n])?null:t[n]:t[r]:t[e]}for(var o=t,s=e.split("."),a=0;a<s.length&&null!=o;a++){if(void 0===(o=o[s[a]])){var i=s[a].charAt(0).toUpperCase()+s[a].slice(1);o=t[i]||t[i.charAt(0).toLowerCase()+i.slice(1)]||null}t=o}return o}},e.setValue=function(e,t,r){var n,o,s=e.toString().split("."),a=r||{},i=a,u=s.length;for(n=0;n<u;n++)o=s[n],n+1===u?i[o]=void 0===t?void 0:t:sf.base.isNullOrUndefined(i[o])&&(i[o]={}),i=i[o];return a},e.sort=function(e,t,r){if(e.length<=1)return e;var n=parseInt((e.length/2).toString(),10),o=e.slice(0,n),s=e.slice(n);return o=this.sort(o,t,r),s=this.sort(s,t,r),this.merge(o,s,t,r)},e.ignoreDiacritics=function(t){return"string"!=typeof t?t:t.split("").map((function(t){return t in e.diacritics?e.diacritics[t]:t})).join("")},e.merge=function(e,t,r,n){for(var o,s=[];e.length>0||t.length>0;)o=e.length>0&&t.length>0?n?n(this.getVal(e,0,r),this.getVal(t,0,r),e[0],t[0])<=0?e:t:e[0][r]<e[0][r]?e:t:e.length>0?e:t,s.push(o.shift());return s},e.getVal=function(e,t,r){return r?this.getObject(r,e[t]):e[t]},e.toLowerCase=function(e){return e?"string"==typeof e?e.toLowerCase():e.toString():0===e||!1===e?e.toString():""},e.callAdaptorFunction=function(e,t,r,n){if(t in e){var o=e[t](r,n);sf.base.isNullOrUndefined(o)||(r=o)}return r},e.getAddParams=function(t,r,n){var o={};return e.callAdaptorFunction(t,"addParams",{dm:r,query:n,params:n.params,reqParams:o}),o},e.isPlainObject=function(e){return!!e&&e.constructor===Object},e.isCors=function(){var e=null;try{e=new window.XMLHttpRequest}catch(e){}return!!e&&"withCredentials"in e},e.getGuid=function(e){var t;return(e||"")+"00000000-0000-4000-0000-000000000000".replace(/0/g,(function(e,r){if("crypto"in window&&"getRandomValues"in crypto){var n=new Uint8Array(1);window.crypto.getRandomValues(n),t=n[0]%16|0}else t=16*Math.random()|0;return"0123456789abcdef"[19===r?3&t|8:t]}))},e.isNull=function(e){return null==e},e.getItemFromComparer=function(t,r,n){var o,s,a,i=0,u="string"==typeof e.getVal(t,0,r);if(t.length)for(;sf.base.isNullOrUndefined(o)&&i<t.length;)o=e.getVal(t,i,r),a=t[i++];for(;i<t.length;i++)s=e.getVal(t,i,r),sf.base.isNullOrUndefined(s)||(u&&(o=+o,s=+s),n(o,s)>0&&(o=s,a=t[i]));return a},e.distinct=function(t,n,o){o=!sf.base.isNullOrUndefined(o)&&o;var s,a=[],i={};return t.forEach((function(u,c){(s="object"===r(t[c])?e.getVal(t,c,n):t[c])in i||(a.push(o?t[c]:s),i[s]=1)})),a},e.processData=function(e,t){var r=this.prepareQuery(e),n=new O(t);e.requiresCounts&&r.requiresCount();var o=n.executeLocal(r),s={result:e.requiresCounts?o.result:o,count:o.count,aggregates:JSON.stringify(o.aggregates)};return e.requiresCounts?s:o},e.prepareQuery=function(t){var r=this,s=new n;(t.select&&s.select(t.select),t.where)&&e.parse.parseJson(t.where).filter((function(e){if(sf.base.isNullOrUndefined(e.condition))s.where(e.field,e.operator,e.value,e.ignoreCase,e.ignoreAccent);else{var t=[];e.field?t.push(new o(e.field,e.operator,e.value,e.ignoreCase,e.ignoreAccent)):t=t.concat(r.getPredicate(e.predicates)),"or"===e.condition?s.where(o.or(t)):"and"===e.condition&&s.where(o.and(t))}}));t.search&&e.parse.parseJson(t.search).filter((function(e){return s.search(e.key,e.fields,e.operator,e.ignoreCase,e.ignoreAccent)}));return t.aggregates&&t.aggregates.filter((function(e){return s.aggregate(e.type,e.field)})),t.sorted&&t.sorted.filter((function(e){return s.sortBy(e.name,e.direction)})),t.skip&&s.skip(t.skip),t.take&&s.take(t.take),t.group&&t.group.filter((function(e){return s.group(e)})),s},e.getPredicate=function(e){for(var t=[],r=0;r<e.length;r++){var n=e[r];if(n.field)t.push(new o(n.field,n.operator,n.value,n.ignoreCase,n.ignoreAccent));else{for(var s=[],a=this.getPredicate(n.predicates),i=0,u=Object.keys(a);i<u.length;i++){var c=u[i];s.push(a[c])}t.push("or"===n.condition?o.or(s):o.and(s))}}return t},e.serverTimezoneOffset=null,e.timeZoneHandling=!0,e.throwError=function(e){try{throw new Error(e)}catch(e){throw e.message+"\n"+e.stack}},e.aggregates={sum:function(t,r){for(var n,o=0,s="number"!=typeof e.getVal(t,0,r),a=0;a<t.length;a++)n=e.getVal(t,a,r),isNaN(n)||null===n||(s&&(n=+n),o+=n);return o},average:function(t,r){return e.aggregates.sum(t,r)/t.length},min:function(t,r){var n;return"function"==typeof r&&(n=r,r=null),e.getObject(r,e.getItemFromComparer(t,r,n||e.fnAscending))},max:function(t,r){var n;return"function"==typeof r&&(n=r,r=null),e.getObject(r,e.getItemFromComparer(t,r,n||e.fnDescending))},truecount:function(e,t){return new O(e).executeLocal((new n).where(t,"equal",!0,!0)).length},falsecount:function(e,t){return new O(e).executeLocal((new n).where(t,"equal",!1,!0)).length},count:function(e,t){return e.length}},e.operatorSymbols={"<":"lessthan",">":"greaterthan","<=":"lessthanorequal",">=":"greaterthanorequal","==":"equal","!=":"notequal","*=":"contains","$=":"endswith","^=":"startswith"},e.odBiOperator={"<":" lt ",">":" gt ","<=":" le ",">=":" ge ","==":" eq ","!=":" ne ",lessthan:" lt ",lessthanorequal:" le ",greaterthan:" gt ",greaterthanorequal:" ge ",equal:" eq ",notequal:" ne "},e.odUniOperator={"$=":"endswith","^=":"startswith","*=":"substringof",endswith:"endswith",startswith:"startswith",contains:"substringof",doesnotendwith:"not endswith",doesnotstartwith:"not startswith",doesnotcontain:"not substringof",wildcard:"wildcard",like:"like"},e.odv4UniOperator={"$=":"endswith","^=":"startswith","*=":"contains",endswith:"endswith",startswith:"startswith",contains:"contains",doesnotendwith:"not endswith",doesnotstartwith:"not startswith",doesnotcontain:"not contains",wildcard:"wildcard",like:"like"},e.diacritics={"Ⓐ":"A","Ａ":"A","À":"A","Á":"A","Â":"A","Ầ":"A","Ấ":"A","Ẫ":"A","Ẩ":"A","Ã":"A","Ā":"A","Ă":"A","Ằ":"A","Ắ":"A","Ẵ":"A","Ẳ":"A","Ȧ":"A","Ǡ":"A","Ä":"A","Ǟ":"A","Ả":"A","Å":"A","Ǻ":"A","Ǎ":"A","Ȁ":"A","Ȃ":"A","Ạ":"A","Ậ":"A","Ặ":"A","Ḁ":"A","Ą":"A","Ⱥ":"A","Ɐ":"A","Ꜳ":"AA","Æ":"AE","Ǽ":"AE","Ǣ":"AE","Ꜵ":"AO","Ꜷ":"AU","Ꜹ":"AV","Ꜻ":"AV","Ꜽ":"AY","Ⓑ":"B","Ｂ":"B","Ḃ":"B","Ḅ":"B","Ḇ":"B","Ƀ":"B","Ƃ":"B","Ɓ":"B","Ⓒ":"C","Ｃ":"C","Ć":"C","Ĉ":"C","Ċ":"C","Č":"C","Ç":"C","Ḉ":"C","Ƈ":"C","Ȼ":"C","Ꜿ":"C","Ⓓ":"D","Ｄ":"D","Ḋ":"D","Ď":"D","Ḍ":"D","Ḑ":"D","Ḓ":"D","Ḏ":"D","Đ":"D","Ƌ":"D","Ɗ":"D","Ɖ":"D","Ꝺ":"D","Ǳ":"DZ","Ǆ":"DZ","ǲ":"Dz","ǅ":"Dz","Ⓔ":"E","Ｅ":"E","È":"E","É":"E","Ê":"E","Ề":"E","Ế":"E","Ễ":"E","Ể":"E","Ẽ":"E","Ē":"E","Ḕ":"E","Ḗ":"E","Ĕ":"E","Ė":"E","Ë":"E","Ẻ":"E","Ě":"E","Ȅ":"E","Ȇ":"E","Ẹ":"E","Ệ":"E","Ȩ":"E","Ḝ":"E","Ę":"E","Ḙ":"E","Ḛ":"E","Ɛ":"E","Ǝ":"E","Ⓕ":"F","Ｆ":"F","Ḟ":"F","Ƒ":"F","Ꝼ":"F","Ⓖ":"G","Ｇ":"G","Ǵ":"G","Ĝ":"G","Ḡ":"G","Ğ":"G","Ġ":"G","Ǧ":"G","Ģ":"G","Ǥ":"G","Ɠ":"G","Ꞡ":"G","Ᵹ":"G","Ꝿ":"G","Ⓗ":"H","Ｈ":"H","Ĥ":"H","Ḣ":"H","Ḧ":"H","Ȟ":"H","Ḥ":"H","Ḩ":"H","Ḫ":"H","Ħ":"H","Ⱨ":"H","Ⱶ":"H","Ɥ":"H","Ⓘ":"I","Ｉ":"I","Ì":"I","Í":"I","Î":"I","Ĩ":"I","Ī":"I","Ĭ":"I","İ":"I","Ï":"I","Ḯ":"I","Ỉ":"I","Ǐ":"I","Ȉ":"I","Ȋ":"I","Ị":"I","Į":"I","Ḭ":"I","Ɨ":"I","Ⓙ":"J","Ｊ":"J","Ĵ":"J","Ɉ":"J","Ⓚ":"K","Ｋ":"K","Ḱ":"K","Ǩ":"K","Ḳ":"K","Ķ":"K","Ḵ":"K","Ƙ":"K","Ⱪ":"K","Ꝁ":"K","Ꝃ":"K","Ꝅ":"K","Ꞣ":"K","Ⓛ":"L","Ｌ":"L","Ŀ":"L","Ĺ":"L","Ľ":"L","Ḷ":"L","Ḹ":"L","Ļ":"L","Ḽ":"L","Ḻ":"L","Ł":"L","Ƚ":"L","Ɫ":"L","Ⱡ":"L","Ꝉ":"L","Ꝇ":"L","Ꞁ":"L","Ǉ":"LJ","ǈ":"Lj","Ⓜ":"M","Ｍ":"M","Ḿ":"M","Ṁ":"M","Ṃ":"M","Ɱ":"M","Ɯ":"M","Ⓝ":"N","Ｎ":"N","Ǹ":"N","Ń":"N","Ñ":"N","Ṅ":"N","Ň":"N","Ṇ":"N","Ņ":"N","Ṋ":"N","Ṉ":"N","Ƞ":"N","Ɲ":"N","Ꞑ":"N","Ꞥ":"N","Ǌ":"NJ","ǋ":"Nj","Ⓞ":"O","Ｏ":"O","Ò":"O","Ó":"O","Ô":"O","Ồ":"O","Ố":"O","Ỗ":"O","Ổ":"O","Õ":"O","Ṍ":"O","Ȭ":"O","Ṏ":"O","Ō":"O","Ṑ":"O","Ṓ":"O","Ŏ":"O","Ȯ":"O","Ȱ":"O","Ö":"O","Ȫ":"O","Ỏ":"O","Ő":"O","Ǒ":"O","Ȍ":"O","Ȏ":"O","Ơ":"O","Ờ":"O","Ớ":"O","Ỡ":"O","Ở":"O","Ợ":"O","Ọ":"O","Ộ":"O","Ǫ":"O","Ǭ":"O","Ø":"O","Ǿ":"O","Ɔ":"O","Ɵ":"O","Ꝋ":"O","Ꝍ":"O","Ƣ":"OI","Ꝏ":"OO","Ȣ":"OU","Ⓟ":"P","Ｐ":"P","Ṕ":"P","Ṗ":"P","Ƥ":"P","Ᵽ":"P","Ꝑ":"P","Ꝓ":"P","Ꝕ":"P","Ⓠ":"Q","Ｑ":"Q","Ꝗ":"Q","Ꝙ":"Q","Ɋ":"Q","Ⓡ":"R","Ｒ":"R","Ŕ":"R","Ṙ":"R","Ř":"R","Ȑ":"R","Ȓ":"R","Ṛ":"R","Ṝ":"R","Ŗ":"R","Ṟ":"R","Ɍ":"R","Ɽ":"R","Ꝛ":"R","Ꞧ":"R","Ꞃ":"R","Ⓢ":"S","Ｓ":"S","ẞ":"S","Ś":"S","Ṥ":"S","Ŝ":"S","Ṡ":"S","Š":"S","Ṧ":"S","Ṣ":"S","Ṩ":"S","Ș":"S","Ş":"S","Ȿ":"S","Ꞩ":"S","Ꞅ":"S","Ⓣ":"T","Ｔ":"T","Ṫ":"T","Ť":"T","Ṭ":"T","Ț":"T","Ţ":"T","Ṱ":"T","Ṯ":"T","Ŧ":"T","Ƭ":"T","Ʈ":"T","Ⱦ":"T","Ꞇ":"T","Ꜩ":"TZ","Ⓤ":"U","Ｕ":"U","Ù":"U","Ú":"U","Û":"U","Ũ":"U","Ṹ":"U","Ū":"U","Ṻ":"U","Ŭ":"U","Ü":"U","Ǜ":"U","Ǘ":"U","Ǖ":"U","Ǚ":"U","Ủ":"U","Ů":"U","Ű":"U","Ǔ":"U","Ȕ":"U","Ȗ":"U","Ư":"U","Ừ":"U","Ứ":"U","Ữ":"U","Ử":"U","Ự":"U","Ụ":"U","Ṳ":"U","Ų":"U","Ṷ":"U","Ṵ":"U","Ʉ":"U","Ⓥ":"V","Ｖ":"V","Ṽ":"V","Ṿ":"V","Ʋ":"V","Ꝟ":"V","Ʌ":"V","Ꝡ":"VY","Ⓦ":"W","Ｗ":"W","Ẁ":"W","Ẃ":"W","Ŵ":"W","Ẇ":"W","Ẅ":"W","Ẉ":"W","Ⱳ":"W","Ⓧ":"X","Ｘ":"X","Ẋ":"X","Ẍ":"X","Ⓨ":"Y","Ｙ":"Y","Ỳ":"Y","Ý":"Y","Ŷ":"Y","Ỹ":"Y","Ȳ":"Y","Ẏ":"Y","Ÿ":"Y","Ỷ":"Y","Ỵ":"Y","Ƴ":"Y","Ɏ":"Y","Ỿ":"Y","Ⓩ":"Z","Ｚ":"Z","Ź":"Z","Ẑ":"Z","Ż":"Z","Ž":"Z","Ẓ":"Z","Ẕ":"Z","Ƶ":"Z","Ȥ":"Z","Ɀ":"Z","Ⱬ":"Z","Ꝣ":"Z","ⓐ":"a","ａ":"a","ẚ":"a","à":"a","á":"a","â":"a","ầ":"a","ấ":"a","ẫ":"a","ẩ":"a","ã":"a","ā":"a","ă":"a","ằ":"a","ắ":"a","ẵ":"a","ẳ":"a","ȧ":"a","ǡ":"a","ä":"a","ǟ":"a","ả":"a","å":"a","ǻ":"a","ǎ":"a","ȁ":"a","ȃ":"a","ạ":"a","ậ":"a","ặ":"a","ḁ":"a","ą":"a","ⱥ":"a","ɐ":"a","ꜳ":"aa","æ":"ae","ǽ":"ae","ǣ":"ae","ꜵ":"ao","ꜷ":"au","ꜹ":"av","ꜻ":"av","ꜽ":"ay","ⓑ":"b","ｂ":"b","ḃ":"b","ḅ":"b","ḇ":"b","ƀ":"b","ƃ":"b","ɓ":"b","ⓒ":"c","ｃ":"c","ć":"c","ĉ":"c","ċ":"c","č":"c","ç":"c","ḉ":"c","ƈ":"c","ȼ":"c","ꜿ":"c","ↄ":"c","ⓓ":"d","ｄ":"d","ḋ":"d","ď":"d","ḍ":"d","ḑ":"d","ḓ":"d","ḏ":"d","đ":"d","ƌ":"d","ɖ":"d","ɗ":"d","ꝺ":"d","ǳ":"dz","ǆ":"dz","ⓔ":"e","ｅ":"e","è":"e","é":"e","ê":"e","ề":"e","ế":"e","ễ":"e","ể":"e","ẽ":"e","ē":"e","ḕ":"e","ḗ":"e","ĕ":"e","ė":"e","ë":"e","ẻ":"e","ě":"e","ȅ":"e","ȇ":"e","ẹ":"e","ệ":"e","ȩ":"e","ḝ":"e","ę":"e","ḙ":"e","ḛ":"e","ɇ":"e","ɛ":"e","ǝ":"e","ⓕ":"f","ｆ":"f","ḟ":"f","ƒ":"f","ꝼ":"f","ⓖ":"g","ｇ":"g","ǵ":"g","ĝ":"g","ḡ":"g","ğ":"g","ġ":"g","ǧ":"g","ģ":"g","ǥ":"g","ɠ":"g","ꞡ":"g","ᵹ":"g","ꝿ":"g","ⓗ":"h","ｈ":"h","ĥ":"h","ḣ":"h","ḧ":"h","ȟ":"h","ḥ":"h","ḩ":"h","ḫ":"h","ẖ":"h","ħ":"h","ⱨ":"h","ⱶ":"h","ɥ":"h","ƕ":"hv","ⓘ":"i","ｉ":"i","ì":"i","í":"i","î":"i","ĩ":"i","ī":"i","ĭ":"i","ï":"i","ḯ":"i","ỉ":"i","ǐ":"i","ȉ":"i","ȋ":"i","ị":"i","į":"i","ḭ":"i","ɨ":"i","ı":"i","ⓙ":"j","ｊ":"j","ĵ":"j","ǰ":"j","ɉ":"j","ⓚ":"k","ｋ":"k","ḱ":"k","ǩ":"k","ḳ":"k","ķ":"k","ḵ":"k","ƙ":"k","ⱪ":"k","ꝁ":"k","ꝃ":"k","ꝅ":"k","ꞣ":"k","ⓛ":"l","ｌ":"l","ŀ":"l","ĺ":"l","ľ":"l","ḷ":"l","ḹ":"l","ļ":"l","ḽ":"l","ḻ":"l","ſ":"l","ł":"l","ƚ":"l","ɫ":"l","ⱡ":"l","ꝉ":"l","ꞁ":"l","ꝇ":"l","ǉ":"lj","ⓜ":"m","ｍ":"m","ḿ":"m","ṁ":"m","ṃ":"m","ɱ":"m","ɯ":"m","ⓝ":"n","ｎ":"n","ǹ":"n","ń":"n","ñ":"n","ṅ":"n","ň":"n","ṇ":"n","ņ":"n","ṋ":"n","ṉ":"n","ƞ":"n","ɲ":"n","ŉ":"n","ꞑ":"n","ꞥ":"n","ǌ":"nj","ⓞ":"o","ｏ":"o","ò":"o","ó":"o","ô":"o","ồ":"o","ố":"o","ỗ":"o","ổ":"o","õ":"o","ṍ":"o","ȭ":"o","ṏ":"o","ō":"o","ṑ":"o","ṓ":"o","ŏ":"o","ȯ":"o","ȱ":"o","ö":"o","ȫ":"o","ỏ":"o","ő":"o","ǒ":"o","ȍ":"o","ȏ":"o","ơ":"o","ờ":"o","ớ":"o","ỡ":"o","ở":"o","ợ":"o","ọ":"o","ộ":"o","ǫ":"o","ǭ":"o","ø":"o","ǿ":"o","ɔ":"o","ꝋ":"o","ꝍ":"o","ɵ":"o","ƣ":"oi","ȣ":"ou","ꝏ":"oo","ⓟ":"p","ｐ":"p","ṕ":"p","ṗ":"p","ƥ":"p","ᵽ":"p","ꝑ":"p","ꝓ":"p","ꝕ":"p","ⓠ":"q","ｑ":"q","ɋ":"q","ꝗ":"q","ꝙ":"q","ⓡ":"r","ｒ":"r","ŕ":"r","ṙ":"r","ř":"r","ȑ":"r","ȓ":"r","ṛ":"r","ṝ":"r","ŗ":"r","ṟ":"r","ɍ":"r","ɽ":"r","ꝛ":"r","ꞧ":"r","ꞃ":"r","ⓢ":"s","ｓ":"s","ß":"s","ś":"s","ṥ":"s","ŝ":"s","ṡ":"s","š":"s","ṧ":"s","ṣ":"s","ṩ":"s","ș":"s","ş":"s","ȿ":"s","ꞩ":"s","ꞅ":"s","ẛ":"s","ⓣ":"t","ｔ":"t","ṫ":"t","ẗ":"t","ť":"t","ṭ":"t","ț":"t","ţ":"t","ṱ":"t","ṯ":"t","ŧ":"t","ƭ":"t","ʈ":"t","ⱦ":"t","ꞇ":"t","ꜩ":"tz","ⓤ":"u","ｕ":"u","ù":"u","ú":"u","û":"u","ũ":"u","ṹ":"u","ū":"u","ṻ":"u","ŭ":"u","ü":"u","ǜ":"u","ǘ":"u","ǖ":"u","ǚ":"u","ủ":"u","ů":"u","ű":"u","ǔ":"u","ȕ":"u","ȗ":"u","ư":"u","ừ":"u","ứ":"u","ữ":"u","ử":"u","ự":"u","ụ":"u","ṳ":"u","ų":"u","ṷ":"u","ṵ":"u","ʉ":"u","ⓥ":"v","ｖ":"v","ṽ":"v","ṿ":"v","ʋ":"v","ꝟ":"v","ʌ":"v","ꝡ":"vy","ⓦ":"w","ｗ":"w","ẁ":"w","ẃ":"w","ŵ":"w","ẇ":"w","ẅ":"w","ẘ":"w","ẉ":"w","ⱳ":"w","ⓧ":"x","ｘ":"x","ẋ":"x","ẍ":"x","ⓨ":"y","ｙ":"y","ỳ":"y","ý":"y","ŷ":"y","ỹ":"y","ȳ":"y","ẏ":"y","ÿ":"y","ỷ":"y","ẙ":"y","ỵ":"y","ƴ":"y","ɏ":"y","ỿ":"y","ⓩ":"z","ｚ":"z","ź":"z","ẑ":"z","ż":"z","ž":"z","ẓ":"z","ẕ":"z","ƶ":"z","ȥ":"z","ɀ":"z","ⱬ":"z","ꝣ":"z","Ά":"Α","Έ":"Ε","Ή":"Η","Ί":"Ι","Ϊ":"Ι","Ό":"Ο","Ύ":"Υ","Ϋ":"Υ","Ώ":"Ω","ά":"α","έ":"ε","ή":"η","ί":"ι","ϊ":"ι","ΐ":"ι","ό":"ο","ύ":"υ","ϋ":"υ","ΰ":"υ","ω":"ω","ς":"σ"},e.fnOperators={equal:function(t,r,n,o){return o&&(t=e.ignoreDiacritics(t),r=e.ignoreDiacritics(r)),n?e.toLowerCase(t)===e.toLowerCase(r):t===r},notequal:function(t,r,n,o){return o&&(t=e.ignoreDiacritics(t),r=e.ignoreDiacritics(r)),!e.fnOperators.equal(t,r,n)},lessthan:function(t,r,n){return n?e.toLowerCase(t)<e.toLowerCase(r):(sf.base.isNullOrUndefined(t)&&(t=void 0),t<r)},greaterthan:function(t,r,n){return n?e.toLowerCase(t)>e.toLowerCase(r):t>r},lessthanorequal:function(t,r,n){return n?e.toLowerCase(t)<=e.toLowerCase(r):(sf.base.isNullOrUndefined(t)&&(t=void 0),t<=r)},greaterthanorequal:function(t,r,n){return n?e.toLowerCase(t)>=e.toLowerCase(r):t>=r},contains:function(t,r,n,o){return o&&(t=e.ignoreDiacritics(t),r=e.ignoreDiacritics(r)),n?!sf.base.isNullOrUndefined(t)&&!sf.base.isNullOrUndefined(r)&&-1!==e.toLowerCase(t).indexOf(e.toLowerCase(r)):!sf.base.isNullOrUndefined(t)&&!sf.base.isNullOrUndefined(r)&&-1!==t.toString().indexOf(r)},doesnotcontain:function(t,r,n,o){return o&&(t=e.ignoreDiacritics(t),r=e.ignoreDiacritics(r)),n?!sf.base.isNullOrUndefined(t)&&!sf.base.isNullOrUndefined(r)&&-1===e.toLowerCase(t).indexOf(e.toLowerCase(r)):!sf.base.isNullOrUndefined(t)&&!sf.base.isNullOrUndefined(r)&&-1===t.toString().indexOf(r)},isnotnull:function(e){return null!=e},isnull:function(e){return null==e},startswith:function(t,r,n,o){return o&&(t=e.ignoreDiacritics(t),r=e.ignoreDiacritics(r)),n?t&&r&&e.startsWith(e.toLowerCase(t),e.toLowerCase(r)):t&&r&&e.startsWith(t,r)},doesnotstartwith:function(t,r,n,o){return o&&(t=e.ignoreDiacritics(t),r=e.ignoreDiacritics(r)),n?t&&r&&e.notStartsWith(e.toLowerCase(t),e.toLowerCase(r)):t&&r&&e.notStartsWith(t,r)},like:function(t,r,n,o){return o&&(t=e.ignoreDiacritics(t),r=e.ignoreDiacritics(r)),n?t&&r&&e.like(e.toLowerCase(t),e.toLowerCase(r)):t&&r&&e.like(t,r)},isempty:function(e){return void 0===e||""===e},isnotempty:function(e){return void 0!==e&&""!==e},wildcard:function(t,n,o,s){return s&&(t=e.ignoreDiacritics(t),n=e.ignoreDiacritics(n)),o?(t||"boolean"==typeof t)&&n&&"object"!==r(t)&&e.wildCard(e.toLowerCase(t),e.toLowerCase(n)):(t||"boolean"==typeof t)&&n&&e.wildCard(t,n)},endswith:function(t,r,n,o){return o&&(t=e.ignoreDiacritics(t),r=e.ignoreDiacritics(r)),n?t&&r&&e.endsWith(e.toLowerCase(t),e.toLowerCase(r)):t&&r&&e.endsWith(t,r)},doesnotendwith:function(t,r,n,o){return o&&(t=e.ignoreDiacritics(t),r=e.ignoreDiacritics(r)),n?t&&r&&e.notEndsWith(e.toLowerCase(t),e.toLowerCase(r)):t&&r&&e.notEndsWith(t,r)},processSymbols:function(t){var r=e.operatorSymbols[t];return r?e.fnOperators[r]:e.throwError("Query - Process Operator : Invalid operator")},processOperator:function(t){var r=e.fnOperators[t];return r||e.fnOperators.processSymbols(t)}},e.parse={parseJson:function(t){return"string"!=typeof t||!/^[\s]*\[|^[\s]*\{(.)+:/g.test(t)&&-1!==t.indexOf('"')?t instanceof Array?e.parse.iterateAndReviveArray(t):"object"===r(t)&&null!==t&&e.parse.iterateAndReviveJson(t):t=JSON.parse(t,e.parse.jsonReviver),t},iterateAndReviveArray:function(t){for(var n=0;n<t.length;n++)"object"===r(t[n])&&null!==t[n]?e.parse.iterateAndReviveJson(t[n]):"string"!=typeof t[n]||/^[\s]*\[|^[\s]*\{(.)+:|\"/g.test(t[n])&&-1!==t[n].toString().indexOf('"')?t[n]=e.parse.parseJson(t[n]):t[n]=e.parse.jsonReviver("",t[n])},iterateAndReviveJson:function(t){for(var n,o=0,s=Object.keys(t);o<s.length;o++){var a=s[o];e.startsWith(a,"__")||("object"===r(n=t[a])?n instanceof Array?e.parse.iterateAndReviveArray(n):n&&e.parse.iterateAndReviveJson(n):t[a]=e.parse.jsonReviver(t[a],n))}},jsonReviver:function(t,r){if("string"==typeof r){var n=/^\/Date\(([+-]?[0-9]+)([+-][0-9]{4})?\)\/$/.exec(r),o=e.timeZoneHandling?e.serverTimezoneOffset:null;if(n)return e.dateParse.toTimeZone(new Date(parseInt(n[1],10)),o,!0);if(/^(\d{4}\-\d\d\-\d\d([tT][\d:\.]*){1})([zZ]|([+\-])(\d\d):?(\d\d))?$/.test(r)){var s=r.indexOf("Z")>-1||r.indexOf("z")>-1,a=r.split(/[^0-9.]/);if(s){if(a[5].indexOf(".")>-1){var i=a[5].split(".");a[5]=i[0],a[6]=new Date(r).getUTCMilliseconds().toString()}else a[6]="00";r=e.dateParse.toTimeZone(new Date(parseInt(a[0],10),parseInt(a[1],10)-1,parseInt(a[2],10),parseInt(a[3],10),parseInt(a[4],10),parseInt(a[5]?a[5]:"00",10),parseInt(a[6],10)),e.serverTimezoneOffset,!1)}else{var u=new Date(parseInt(a[0],10),parseInt(a[1],10)-1,parseInt(a[2],10),parseInt(a[3],10),parseInt(a[4],10),parseInt(a[5]?a[5]:"00",10)),c=parseInt(a[6],10),l=parseInt(a[7],10);if(isNaN(c)&&isNaN(l))return u;r.indexOf("+")>-1?u.setHours(u.getHours()-c,u.getMinutes()-l):u.setHours(u.getHours()+c,u.getMinutes()+l),r=e.dateParse.toTimeZone(u,e.serverTimezoneOffset,!1)}null==e.serverTimezoneOffset&&(r=e.dateParse.addSelfOffset(r))}}return r},isJson:function(t){return"string"==typeof t[0]?t:e.parse.parseJson(t)},isGuid:function(e){return null!=/[A-Fa-f0-9]{8}(?:-[A-Fa-f0-9]{4}){3}-[A-Fa-f0-9]{12}/i.exec(e)},replacer:function(t,r){return e.isPlainObject(t)?e.parse.jsonReplacer(t,r):t instanceof Array?e.parse.arrayReplacer(t):t instanceof Date?e.parse.jsonReplacer({val:t},r).val:t},jsonReplacer:function(t,r){for(var n,o=0,s=Object.keys(t);o<s.length;o++){var a=s[o];if((n=t[a])instanceof Date){var i=n;null==e.serverTimezoneOffset?t[a]=e.dateParse.toTimeZone(i,null).toJSON():(i=new Date(+i+36e5*e.serverTimezoneOffset),t[a]=e.dateParse.toTimeZone(e.dateParse.addSelfOffset(i),null).toJSON())}}return t},arrayReplacer:function(t){for(var r=0;r<t.length;r++)e.isPlainObject(t[r])?t[r]=e.parse.jsonReplacer(t[r]):t[r]instanceof Date&&(t[r]=e.parse.jsonReplacer({date:t[r]}).date);return t},jsonDateReplacer:function(t,r){if("value"===t&&r){if("string"==typeof r){var n=/^\/Date\(([+-]?[0-9]+)([+-][0-9]{4})?\)\/$/.exec(r);if(n)r=e.dateParse.toTimeZone(new Date(parseInt(n[1],10)),null,!0);else if(/^(\d{4}\-\d\d\-\d\d([tT][\d:\.]*){1})([zZ]|([+\-])(\d\d):?(\d\d))?$/.test(r)){var o=r.split(/[^0-9]/);r=e.dateParse.toTimeZone(new Date(parseInt(o[0],10),parseInt(o[1],10)-1,parseInt(o[2],10),parseInt(o[3],10),parseInt(o[4],10),parseInt(o[5],10)),null,!0)}}if(r instanceof Date)return r=e.dateParse.addSelfOffset(r),null===e.serverTimezoneOffset?e.dateParse.toTimeZone(e.dateParse.addSelfOffset(r),null).toJSON():(r=e.dateParse.toTimeZone(r,r.getTimezoneOffset()/60-e.serverTimezoneOffset,!1)).toJSON()}return r}},e.dateParse={addSelfOffset:function(e){return new Date(+e-6e4*e.getTimezoneOffset())},toUTC:function(e){return new Date(+e+6e4*e.getTimezoneOffset())},toTimeZone:function(t,r,n){if(null===r)return t;var o=n?e.dateParse.toUTC(t):t;return new Date(+o-36e5*r)},toLocalTime:function(e){var t=e,r=-t.getTimezoneOffset(),n=r>=0?"+":"-",o=function(e){var t=Math.floor(Math.abs(e));return(t<10?"0":"")+t};return t.getFullYear()+"-"+o(t.getMonth()+1)+"-"+o(t.getDate())+"T"+o(t.getHours())+":"+o(t.getMinutes())+":"+o(t.getSeconds())+n+o(r/60)+":"+o(r%60)}},e}(),i=(t=function(e,r){return(t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)t.hasOwnProperty(r)&&(e[r]=t[r])})(e,r)},function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}),u={GroupGuid:"{271bbba0-1ee7}"},c=function(){function e(t){this.options={from:"table",requestType:"json",sortBy:"sorted",select:"select",skip:"skip",group:"group",take:"take",search:"search",count:"requiresCounts",where:"where",aggregates:"aggregates",expand:"expand"},this.type=e,this.dataSource=t,this.pvt={}}return e.prototype.processResponse=function(e,t,r,n){return e},e}(),l=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return i(t,e),t.prototype.processQuery=function(e,t){for(var r,n,o,s=e.dataSource.json.slice(0),a=s.length,i=!0,u={},c=0,l=[],d=[],p=0;p<t.lazyLoad.length;p++)c++,u[t.lazyLoad[p].key]=t.lazyLoad[p].value;var f={},h=!1;if(t.lazyLoad.length)for(p=0;p<t.queries.length;p++)if("onGroup"===(n=t.queries[p]).fn&&!sf.base.isNullOrUndefined(n.e.format)){h=!0;break}for(p=0;p<t.queries.length;p++)"onPage"!==(n=t.queries[p]).fn&&"onGroup"!==n.fn&&("onSortBy"!==n.fn||h)||!t.lazyLoad.length?(r=this[n.fn].call(this,s,n.e,t),"onAggregates"===n.fn?f[n.e.field+" - "+n.e.type]=r:s=void 0!==r?r:s,"onPage"!==n.fn&&"onSkip"!==n.fn&&"onTake"!==n.fn&&"onRange"!==n.fn||(i=!1),i&&(a=s.length)):("onGroup"===n.fn&&l.push(n.e),"onPage"===n.fn&&(o=n.e),"onSortBy"===n.fn&&d.unshift(n.e));if(c){var g={query:t,lazyLoad:u,result:s,group:l,page:o,sort:d},y=this.lazyLoadGroup(g);s=y.result,a=y.count}return t.isCountRequired&&(s={result:s,count:a,aggregates:f}),s},t.prototype.lazyLoadGroup=function(e){var t=0,r=this.getAggregate(e.query),n=e.result;if(sf.base.isNullOrUndefined(e.lazyLoad.onDemandGroupInfo)){var o=e.group[0].fieldName;t=(n=a.group(n,o,r,null,null,e.group[0].comparer,!0)).length;l=n;if(e.sort.length){var s=e.sort.length>1?e.sort.filter((function(e){return e.fieldName===o}))[0]:e.sort[0];n=this.onSortBy(n,s,e.query,!0)}e.page&&(n=this.onPage(n,e.page,e.query)),this.formGroupResult(n,l)}else{for(var i=e.lazyLoad.onDemandGroupInfo,u=i.where.length-1;u>=0;u--)n=this.onWhere(n,i.where[u]);if(e.group.length!==i.level){var c=e.group[i.level].fieldName;n=a.group(n,c,r,null,null,e.group[i.level].comparer,!0),e.sort.length&&(n=this.onSortBy(n,e.sort[parseInt(i.level.toString(),10)],e.query,!0))}else for(u=e.sort.length-1;u>=i.level;u--)n=this.onSortBy(n,e.sort[parseInt(u.toString(),10)],e.query,!1);t=n.length;var l=n;n=(n=n.slice(i.skip)).slice(0,i.take),e.group.length!==i.level&&this.formGroupResult(n,l)}return{result:n,count:t}},t.prototype.formGroupResult=function(e,t){if(e.length&&t.length){e.GroupGuid=t.GroupGuid,e.childLevels=t.childLevels,e.level=t.level,e.records=t.records}return e},t.prototype.getAggregate=function(e){var t=n.filterQueries(e.queries,"onAggregates"),r=[];if(t.length)for(var o=void 0,s=0;s<t.length;s++)o=t[s].e,r.push({type:o.type,field:a.getValue(o.field,e)});return r},t.prototype.batchRequest=function(e,t,r){var n,o=t.deletedRecords.length;for(n=0;n<t.addedRecords.length;n++)this.insert(e,t.addedRecords[n]);for(n=0;n<t.changedRecords.length;n++)this.update(e,r.key,t.changedRecords[n]);for(n=0;n<o;n++)this.remove(e,r.key,t.deletedRecords[n]);return t},t.prototype.onWhere=function(e,t){return e&&e.length?e.filter((function(e){if(t)return t.validate(e)})):e},t.prototype.onAggregates=function(e,t){var r=a.aggregates[t.type];return e&&r&&0!==e.length?r(e,t.field):null},t.prototype.onSearch=function(e,t){return e&&e.length?(0===t.fieldNames.length&&a.getFieldList(e[0],t.fieldNames),e.filter((function(e){for(var r=0;r<t.fieldNames.length;r++)if(t.comparer.call(e,a.getObject(t.fieldNames[r],e),t.searchKey,t.ignoreCase,t.ignoreAccent))return!0;return!1}))):e},t.prototype.onSortBy=function(e,t,r,n){if(!e||!e.length)return e;var o,s=a.getValue(t.fieldName,r);if(!s)return e.sort(t.comparer);if(s instanceof Array){for(var i=(s=s.slice(0)).length-1;i>=0;i--)s[i]&&(o=t.comparer,a.endsWith(s[i]," desc")&&(o=a.fnSort("descending"),s[i]=s[i].replace(" desc","")),e=a.sort(e,s[i],o));return e}return a.sort(e,n?"key":s,t.comparer)},t.prototype.onGroup=function(e,t,r){if(!e||!e.length)return e;var n=this.getAggregate(r);return a.group(e,a.getValue(t.fieldName,r),n,null,null,t.comparer)},t.prototype.onPage=function(e,t,r){var n=a.getValue(t.pageSize,r),o=(a.getValue(t.pageIndex,r)-1)*n,s=o+n;return e&&e.length?e.slice(o,s):e},t.prototype.onRange=function(e,t){return e&&e.length?e.slice(a.getValue(t.start),a.getValue(t.end)):e},t.prototype.onTake=function(e,t){return e&&e.length?e.slice(0,a.getValue(t.nos)):e},t.prototype.onSkip=function(e,t){return e&&e.length?e.slice(a.getValue(t.nos)):e},t.prototype.onSelect=function(e,t){return e&&e.length?a.select(e,a.getValue(t.fieldNames)):e},t.prototype.insert=function(e,t,r,n,o){return sf.base.isNullOrUndefined(o)?e.dataSource.json.push(t):e.dataSource.json.splice(o,0,t)},t.prototype.remove=function(e,t,n,o){var s,i=e.dataSource.json;for("object"!==r(n)||n instanceof Date||(n=a.getObject(t,n)),s=0;s<i.length&&a.getObject(t,i[s])!==n;s++);return s!==i.length?i.splice(s,1):null},t.prototype.update=function(e,t,r,n){var o,s,a=e.dataSource.json;for(sf.base.isNullOrUndefined(t)||(s=sf.base.getValue(t,r)),o=0;o<a.length&&(sf.base.isNullOrUndefined(t)||sf.base.getValue(t,a[o])!==s);o++);return o<a.length?sf.base.merge(a[o],r):null},t}(c),d=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return i(t,e),t.prototype.processQuery=function(e,t,o){var s,i,u=this.getQueryRequest(t),c=n.filterQueryLists(t.queries,["onSelect","onPage","onSkip","onTake","onRange"]),l=t.params,d=e.dataSource.url,p=null,f=this.options,h={sorts:[],groups:[],filters:[],searches:[],aggregates:[]};"onPage"in c?(s=c.onPage,i=((i=a.getValue(s.pageIndex,t))-1)*(p=a.getValue(s.pageSize,t))):"onRange"in c&&(i=(s=c.onRange).start,p=s.end-s.start);for(var g=0;g<u.sorts.length;g++)s=a.getValue(u.sorts[g].e.fieldName,t),h.sorts.push(a.callAdaptorFunction(this,"onEachSort",{name:s,direction:u.sorts[g].e.direction},t));o&&(s=this.getFiltersFrom(o,t))&&h.filters.push(a.callAdaptorFunction(this,"onEachWhere",s.toJson(),t));for(g=0;g<u.filters.length;g++){var y=a.callAdaptorFunction(this,"onEachWhere",u.filters[g].e.toJson(),t);this.getModuleName&&"ODataV4Adaptor"===this.getModuleName()&&!sf.base.isNullOrUndefined(u.filters[g].e.key)&&u.filters.length>1&&(y="("+y+")"),h.filters.push(y);for(var v=0,m="object"===r(h.filters[g])?Object.keys(h.filters[g]):[];v<m.length;v++){var b=m[v];a.isNull(h[b])&&delete h[b]}}for(g=0;g<u.searches.length;g++)s=u.searches[g].e,h.searches.push(a.callAdaptorFunction(this,"onEachSearch",{fields:s.fieldNames,operator:s.operator,key:s.searchKey,ignoreCase:s.ignoreCase},t));for(g=0;g<u.groups.length;g++)h.groups.push(a.getValue(u.groups[g].e.fieldName,t));for(g=0;g<u.aggregates.length;g++)s=u.aggregates[g].e,h.aggregates.push({type:s.type,field:a.getValue(s.field,t)});var O={};if(this.getRequestQuery(f,t,c,h,O),a.callAdaptorFunction(this,"addParams",{dm:e,query:t,params:l,reqParams:O}),t.lazyLoad.length)for(g=0;g<t.lazyLoad.length;g++)O[t.lazyLoad[g].key]=t.lazyLoad[g].value;for(var S=0,A=Object.keys(O);S<A.length;S++){b=A[S];(a.isNull(O[b])||""===O[b]||0===O[b].length)&&delete O[b]}f.skip in O&&f.take in O||null===p||(O[f.skip]=a.callAdaptorFunction(this,"onSkip",i,t),O[f.take]=a.callAdaptorFunction(this,"onTake",p,t));var w=this.pvt;return this.pvt={},"json"===this.options.requestType?{data:JSON.stringify(O,a.parse.jsonDateReplacer),url:d,pvtData:w,type:"POST",contentType:"application/json; charset=utf-8"}:(s=this.convertToQueryString(O,t,e),{type:"GET",url:(s=(-1!==e.dataSource.url.indexOf("?")?"&":"/")+s).length?d.replace(/\/*$/,s):d,pvtData:w})},t.prototype.getRequestQuery=function(e,t,r,n,o){var s=o;s[e.from]=t.fromTable,e.apply&&t.distincts.length&&(s[e.apply]="onDistinct"in this?a.callAdaptorFunction(this,"onDistinct",t.distincts):""),!t.distincts.length&&e.expand&&(s[e.expand]="onExpand"in this&&"onSelect"in r?a.callAdaptorFunction(this,"onExpand",{selects:a.getValue(r.onSelect.fieldNames,t),expands:t.expands},t):t.expands),s[e.select]="onSelect"in r&&!t.distincts.length?a.callAdaptorFunction(this,"onSelect",a.getValue(r.onSelect.fieldNames,t),t):"",s[e.count]=t.isCountRequired?a.callAdaptorFunction(this,"onCount",t.isCountRequired,t):"",s[e.search]=n.searches.length?a.callAdaptorFunction(this,"onSearch",n.searches,t):"",s[e.skip]="onSkip"in r?a.callAdaptorFunction(this,"onSkip",a.getValue(r.onSkip.nos,t),t):"",s[e.take]="onTake"in r?a.callAdaptorFunction(this,"onTake",a.getValue(r.onTake.nos,t),t):"",s[e.where]=n.filters.length||n.searches.length?a.callAdaptorFunction(this,"onWhere",n.filters,t):"",s[e.sortBy]=n.sorts.length?a.callAdaptorFunction(this,"onSortBy",n.sorts,t):"",s[e.group]=n.groups.length?a.callAdaptorFunction(this,"onGroup",n.groups,t):"",s[e.aggregates]=n.aggregates.length?a.callAdaptorFunction(this,"onAggregates",n.aggregates,t):"",s.param=[]},t.prototype.convertToQueryString=function(e,t,r){return""},t.prototype.processResponse=function(e,t,r,n,o,s){if(n&&n.headers.get("Content-Type")&&-1!==n.headers.get("Content-Type").indexOf("application/json")){var i=a.timeZoneHandling;t&&!t.timeZoneHandling&&(a.timeZoneHandling=!1),e=a.parse.parseJson(e),a.timeZoneHandling=i}var u=o,c=u.pvtData||{},l=e?e.groupDs:[];if(n&&n.headers.get("Content-Type")&&-1!==n.headers.get("Content-Type").indexOf("xml"))return r.isCountRequired?{result:[],count:0}:[];var d=JSON.parse(u.data);if(d&&"batch"===d.action&&e&&e.addedRecords)return s.addedRecords=e.addedRecords,s;e&&e.d&&(e=e.d);var p={};e&&"count"in e&&(p.count=e.count),p.result=e&&e.result?e.result:e;var f=!1;if(Array.isArray(e.result)&&e.result.length){if(sf.base.isNullOrUndefined(e.result[0].key)||(p.result=this.formRemoteGroupedData(p.result,1,c.groups.length-1)),r&&r.lazyLoad.length&&c.groups.length)for(var h=0;h<r.lazyLoad.length;h++)if("onDemandGroupInfo"===r.lazyLoad[h].key){var g=r.lazyLoad[h].value.level;c.groups.length===g&&(f=!0)}}return f||this.getAggregateResult(c,e,p,l,r),a.isNull(p.count)?p.result:{result:p.result,count:p.count,aggregates:p.aggregates}},t.prototype.formRemoteGroupedData=function(e,t,r){for(var n=0;n<e.length;n++)e[n].items.length&&Object.keys(e[n].items[0]).indexOf("key")>-1&&this.formRemoteGroupedData(e[n].items,t+1,r-1);return e.GroupGuid=u.GroupGuid,e.level=t,e.childLevels=r,e.records=e[0].items.length?this.getGroupedRecords(e,!sf.base.isNullOrUndefined(e[0].items.records)):[],e},t.prototype.getGroupedRecords=function(e,t){for(var r=[],n=0;n<e.length;n++)if(t)r=r.concat(e[n].items.records);else for(var o=0;o<e[n].items.length;o++)r.push(e[n].items[o]);return r},t.prototype.onGroup=function(e){return this.pvt.groups=e,e},t.prototype.onAggregates=function(e){this.pvt.aggregates=e},t.prototype.batchRequest=function(e,t,r,n,o){return{type:"POST",url:e.dataSource.batchUrl||e.dataSource.crudUrl||e.dataSource.removeUrl||e.dataSource.url,contentType:"application/json; charset=utf-8",dataType:"json",data:JSON.stringify(sf.base.extend({},{changed:t.changedRecords,added:t.addedRecords,deleted:t.deletedRecords,action:"batch",table:r[void 0],key:r[void 0]},a.getAddParams(this,e,n)))}},t.prototype.beforeSend=function(e,t){},t.prototype.insert=function(e,t,r,n){return{url:e.dataSource.insertUrl||e.dataSource.crudUrl||e.dataSource.url,data:JSON.stringify(sf.base.extend({},{value:t,table:r,action:"insert"},a.getAddParams(this,e,n)))}},t.prototype.remove=function(e,t,r,n,o){return{type:"POST",url:e.dataSource.removeUrl||e.dataSource.crudUrl||e.dataSource.url,data:JSON.stringify(sf.base.extend({},{key:r,keyColumn:t,table:n,action:"remove"},a.getAddParams(this,e,o)))}},t.prototype.update=function(e,t,r,n,o){return{type:"POST",url:e.dataSource.updateUrl||e.dataSource.crudUrl||e.dataSource.url,data:JSON.stringify(sf.base.extend({},{value:r,action:"update",keyColumn:t,key:a.getObject(t,r),table:n},a.getAddParams(this,e,o)))}},t.prototype.getFiltersFrom=function(e,t){var n,s=t.fKey,i=s,u=t.key,c=[];"object"!==r(e[0])&&(i=null);for(var l=0;l<e.length;l++)n="object"===r(e[0])?a.getObject(u||i,e[l]):e[l],c.push(new o(s,"equal",n));return o.or(c)},t.prototype.getAggregateResult=function(e,t,r,o,s){var i=t;if(t&&t.result&&(i=t.result),e&&e.aggregates&&e.aggregates.length){var u=e.aggregates,c=void 0,l=i,d={};t.aggregate&&(l=t.aggregate);for(var p=0;p<u.length;p++)(c=a.aggregates[u[p].type])&&(d[u[p].field+" - "+u[p].type]=c(l,u[p].field));r.aggregates=d}var f=Array.isArray(t.result)&&t.result.length&&!sf.base.isNullOrUndefined(t.result[0].key);if(e&&e.groups&&e.groups.length&&!f){var h=e.groups;for(p=0;p<h.length;p++){sf.base.isNullOrUndefined(o)||(o=a.group(o,h[p]));var g=n.filterQueries(s.queries,"onGroup")[p].e;i=a.group(i,h[p],e.aggregates,null,o,g.comparer)}r.result=i}return r},t.prototype.getQueryRequest=function(e){var t={sorts:[],groups:[],filters:[],searches:[],aggregates:[]};return t.sorts=n.filterQueries(e.queries,"onSortBy"),t.groups=n.filterQueries(e.queries,"onGroup"),t.filters=n.filterQueries(e.queries,"onWhere"),t.searches=n.filterQueries(e.queries,"onSearch"),t.aggregates=n.filterQueries(e.queries,"onAggregates"),t},t.prototype.addParams=function(e){var t=e.reqParams;e.params.length&&(t.params={});for(var r=0,n=e.params;r<n.length;r++){var o=n[r];if(t[o.key])throw new Error("Query() - addParams: Custom Param is conflicting other request arguments");t[o.key]=o.value,o.fn&&(t[o.key]=o.fn.call(e.query,o.key,e.query,e.dm)),t.params[o.key]=t[o.key]}},t}(c),p=function(e){function t(t){var r=e.call(this)||this;return r.options=sf.base.extend({},r.options,{requestType:"get",accept:"application/json;odata=light;q=1,application/json;odata=verbose;q=0.5",multipartAccept:"multipart/mixed",sortBy:"$orderby",select:"$select",skip:"$skip",take:"$top",count:"$inlinecount",where:"$filter",expand:"$expand",batch:"$batch",changeSet:"--changeset_",batchPre:"batch_",contentId:"Content-Id: ",batchContent:"Content-Type: multipart/mixed; boundary=",changeSetContent:"Content-Type: application/http\nContent-Transfer-Encoding: binary ",batchChangeSetContentType:"Content-Type: application/json; charset=utf-8 ",updateType:"PUT"}),sf.base.extend(r.options,t||{}),r}return i(t,e),t.prototype.getModuleName=function(){return"ODataAdaptor"},t.prototype.onPredicate=function(e,n,o){var s,i,u="",c=e.value,l=r(c),d=e.field?t.getField(e.field):null;if(c instanceof Date&&(c="datetime'"+a.parse.replacer(c)+"'"),"string"===l&&(c=c.replace(/'/g,"''"),e.ignoreCase&&(c=c.toLowerCase()),"like"!==e.operator&&(c=encodeURIComponent(c)),"wildcard"!==e.operator&&"like"!==e.operator&&(c="'"+c+"'"),o&&(d="cast("+d+", 'Edm.String')"),a.parse.isGuid(c)&&(i="guid"),e.ignoreCase&&(i||(d="tolower("+d+")"),c=c.toLowerCase())),"isempty"===e.operator||"isnull"===e.operator||"isnotempty"===e.operator||"isnotnull"===e.operator?(s=-1!==e.operator.indexOf("isnot")?a.odBiOperator.notequal:a.odBiOperator.equal,c="isnull"===e.operator||"isnotnull"===e.operator?null:"''"):s=a.odBiOperator[e.operator],s)return u+=d,u+=s,i&&(u+=i),u+c;if("like"===(s=sf.base.isNullOrUndefined(this.getModuleName)||"ODataV4Adaptor"!==this.getModuleName()?a.odUniOperator[e.operator]:a.odv4UniOperator[e.operator]))-1!==(c=c).indexOf("%")&&("%"===c.charAt(0)&&c.lastIndexOf("%")<2?(c=c.substring(1,c.length),s=sf.base.isNullOrUndefined(this.getModuleName)||"ODataV4Adaptor"!==this.getModuleName()?a.odUniOperator.startswith:a.odv4UniOperator.startswith):"%"===c.charAt(c.length-1)&&c.indexOf("%")>c.length-3?(c=c.substring(0,c.length-1),s=sf.base.isNullOrUndefined(this.getModuleName)||"ODataV4Adaptor"!==this.getModuleName()?a.odUniOperator.endswith:a.odv4UniOperator.endswith):c.lastIndexOf("%")!==c.indexOf("%")&&c.lastIndexOf("%")>c.indexOf("%")+1?(c=c.substring(c.indexOf("%")+1,c.lastIndexOf("%")),s=sf.base.isNullOrUndefined(this.getModuleName)||"ODataV4Adaptor"!==this.getModuleName()?a.odUniOperator.contains:a.odv4UniOperator.contains):s=sf.base.isNullOrUndefined(this.getModuleName)||"ODataV4Adaptor"!==this.getModuleName()?a.odUniOperator.contains:a.odv4UniOperator.contains),c="'"+(c=encodeURIComponent(c))+"'";else if("wildcard"===s)if(-1!==(c=c).indexOf("*")){var p=c.split("*"),f=void 0,h=0;if(0!==c.indexOf("*")&&-1===p[0].indexOf("%3f")&&-1===p[0].indexOf("?")&&(f="'"+(f=p[0])+"'",u+=(s=sf.base.isNullOrUndefined(this.getModuleName)||"ODataV4Adaptor"!==this.getModuleName()?a.odUniOperator.startswith:a.odv4UniOperator.startswith)+"(",u+=d+",",i&&(u+=i),u+=f+")",h++),c.lastIndexOf("*")!==c.length-1&&-1===p[p.length-1].indexOf("%3f")&&-1===p[p.length-1].indexOf("?")&&(f="'"+(f=p[p.length-1])+"'",h>0&&(u+=" and "),u+=(s=sf.base.isNullOrUndefined(this.getModuleName)||"ODataV4Adaptor"!==this.getModuleName()?a.odUniOperator.endswith:a.odv4UniOperator.endswith)+"(",u+=d+",",i&&(u+=i),u+=f+")",h++),p.length>2)for(var g=1;g<p.length-1;g++)if(-1===p[g].indexOf("%3f")&&-1===p[g].indexOf("?")){if(f="'"+(f=p[g])+"'",h>0&&(u+=" and "),"substringof"===(s=sf.base.isNullOrUndefined(this.getModuleName)||"ODataV4Adaptor"!==this.getModuleName()?a.odUniOperator.contains:a.odv4UniOperator.contains)||"not substringof"===s){var y=f;f=d,d=y}u+=s+"(",u+=d+",",i&&(u+=i),u+=f+")",h++}0===h?(s=sf.base.isNullOrUndefined(this.getModuleName)||"ODataV4Adaptor"!==this.getModuleName()?a.odUniOperator.contains:a.odv4UniOperator.contains,-1===c.indexOf("?")&&-1===c.indexOf("%3f")||(c=-1!==c.indexOf("?")?c.split("?").join(""):c.split("%3f").join("")),c="'"+c+"'"):s="wildcard"}else s=sf.base.isNullOrUndefined(this.getModuleName)||"ODataV4Adaptor"!==this.getModuleName()?a.odUniOperator.contains:a.odv4UniOperator.contains,-1===c.indexOf("?")&&-1===c.indexOf("%3f")||(c=-1!==c.indexOf("?")?c.split("?").join(""):c.split("%3f").join("")),c="'"+c+"'";if("substringof"===s||"not substringof"===s){y=c;c=d,d=y}return"wildcard"!==s&&(u+=s+"(",u+=d+",",i&&(u+=i),u+=c+")"),u},t.prototype.addParams=function(t){e.prototype.addParams.call(this,t),delete t.reqParams.params},t.prototype.onComplexPredicate=function(e,t,r){for(var n=[],o=0;o<e.predicates.length;o++)n.push("("+this.onEachWhere(e.predicates[o],t,r)+")");return n.join(" "+e.condition+" ")},t.prototype.onEachWhere=function(e,t,r){return e.isComplex?this.onComplexPredicate(e,t,r):this.onPredicate(e,t,r)},t.prototype.onWhere=function(e){return this.pvt.search&&e.push(this.onEachWhere(this.pvt.search,null,!0)),e.join(" and ")},t.prototype.onEachSearch=function(e){e.fields&&0===e.fields.length&&a.throwError("Query() - Search : oData search requires list of field names to search");for(var t=this.pvt.search||[],r=0;r<e.fields.length;r++)t.push(new o(e.fields[r],e.operator,e.key,e.ignoreCase));this.pvt.search=t},t.prototype.onSearch=function(e){return this.pvt.search=o.or(this.pvt.search),""},t.prototype.onEachSort=function(e){var r=[];if(e.name instanceof Array)for(var n=0;n<e.name.length;n++)r.push(t.getField(e.name[n])+("descending"===e.direction?" desc":""));else r.push(t.getField(e.name)+("descending"===e.direction?" desc":""));return r.join(",")},t.prototype.onSortBy=function(e){return e.reverse().join(",")},t.prototype.onGroup=function(e){return this.pvt.groups=e,[]},t.prototype.onSelect=function(e){for(var r=0;r<e.length;r++)e[r]=t.getField(e[r]);return e.join(",")},t.prototype.onAggregates=function(e){return this.pvt.aggregates=e,""},t.prototype.onCount=function(e){return!0===e?"allpages":""},t.prototype.beforeSend=function(e,t,r){a.endsWith(r.url,this.options.batch)&&"post"===r.type.toLowerCase()?(t.headers.set("Accept",this.options.multipartAccept),t.headers.set("DataServiceVersion","2.0")):t.headers.set("Accept",this.options.accept),t.headers.set("DataServiceVersion","2.0"),t.headers.set("MaxDataServiceVersion","2.0")},t.prototype.processResponse=function(e,t,r,n,o,s){if(o&&"GET"===o.type&&!this.rootUrl&&e["odata.metadata"]){var i=e["odata.metadata"].split("/$metadata#");this.rootUrl=i[0],this.resourceTableName=i[1]}if(!sf.base.isNullOrUndefined(e.d)){var u=r&&r.isCountRequired?e.d.results:e.d;if(!sf.base.isNullOrUndefined(u))for(var c=0;c<u.length;c++)sf.base.isNullOrUndefined(u[c].__metadata)||delete u[c].__metadata}var l=o&&o.pvtData,d=this.processBatchResponse(e,r,n,o,s);if(d)return d;var p=n&&o.fetchRequest.headers.get("DataServiceVersion"),f=null,h=p&&parseInt(p,10)||2;if(r&&r.isCountRequired){(e.__count||e["odata.count"])&&(f=e.__count||e["odata.count"]),e.d&&(e=e.d),(e.__count||e["odata.count"])&&(f=e.__count||e["odata.count"])}3===h&&e.value&&(e=e.value),e.d&&(e=e.d),h<3&&e.results&&(e=e.results);var g={};return g.count=f,g.result=e,this.getAggregateResult(l,e,g,null,r),a.isNull(f)?g.result:{result:g.result,count:g.count,aggregates:g.aggregates}},t.prototype.convertToQueryString=function(e,t,r){var n=[],o=e.table||"";delete e.table,r.dataSource.requiresFormat&&(e.$format="json");for(var s=0,a=Object.keys(e);s<a.length;s++){var i=a[s];n.push(i+"="+e[i])}return n=n.join("&"),r.dataSource.url&&-1!==r.dataSource.url.indexOf("?")&&!o?n:n.length?o+"?"+n:o||""},t.prototype.localTimeReplacer=function(e,t){for(var r=0,n=sf.base.isNullOrUndefined(t)?[]:Object.keys(t);r<n.length;r++){var o=n[r];t[o]instanceof Date&&(t[o]=a.dateParse.toLocalTime(t[o]))}return t},t.prototype.insert=function(e,t,r){return{url:(e.dataSource.insertUrl||e.dataSource.url).replace(/\/*$/,r?"/"+r:""),data:JSON.stringify(t,this.options.localTime?this.localTimeReplacer:null)}},t.prototype.remove=function(e,t,r,n){var o;return o="string"!=typeof r||a.parse.isGuid(r)?"("+r+")":"('"+r+"')",{type:"DELETE",url:(e.dataSource.removeUrl||e.dataSource.url).replace(/\/*$/,n?"/"+n:"")+o}},t.prototype.update=function(e,t,r,n,o,s){var i;return"PATCH"!==this.options.updateType||sf.base.isNullOrUndefined(s)||(r=this.compareAndRemove(r,s,t)),i="string"!=typeof r[t]||a.parse.isGuid(r[t])?"("+r[t]+")":"('"+r[t]+"')",{type:this.options.updateType,url:(e.dataSource.updateUrl||e.dataSource.url).replace(/\/*$/,n?"/"+n:"")+i,data:JSON.stringify(r,this.options.localTime?this.localTimeReplacer:null),accept:this.options.accept}},t.prototype.batchRequest=function(e,t,r,n,o){var s=r.guid=a.getGuid(this.options.batchPre),i=e.dataSource.batchUrl||this.rootUrl?(e.dataSource.batchUrl||this.rootUrl)+"/"+this.options.batch:(e.dataSource.batchUrl||e.dataSource.url).replace(/\/*$/,"/"+this.options.batch);r.url=this.resourceTableName?this.resourceTableName:r.url;var u={url:r.url,key:r.key,cid:1,cSet:a.getGuid(this.options.changeSet)},c="--"+s+"\n";return c+="Content-Type: multipart/mixed; boundary="+u.cSet.replace("--","")+"\n",this.pvt.changeSet=0,c+=this.generateInsertRequest(t.addedRecords,u,e),c+=this.generateUpdateRequest(t.changedRecords,u,e,o?o.changedRecords:[]),c+=this.generateDeleteRequest(t.deletedRecords,u,e),c+=u.cSet+"--\n",{type:"POST",url:i,dataType:"json",contentType:"multipart/mixed; charset=UTF-8;boundary="+s,data:c+="--"+s+"--"}},t.prototype.generateDeleteRequest=function(e,t,r){if(!e)return"";var n={method:"DELETE ",url:function(e,t,r){var n=a.getObject(r,e[t]);return"number"==typeof n||a.parse.isGuid(n)?"("+n+")":n instanceof Date?"("+e[t][r].toJSON()+")":"('"+n+"')"},data:function(e,t){return""}};return this.generateBodyContent(e,t,n,r)+"\n"},t.prototype.generateInsertRequest=function(e,t,r){if(!e)return"";var n={method:"POST ",url:function(e,t,r){return""},data:function(e,t){return JSON.stringify(e[t])+"\n\n"}};return this.generateBodyContent(e,t,n,r)},t.prototype.generateUpdateRequest=function(e,t,r,n){var o=this;if(!e)return"";e.forEach((function(e){return e=o.compareAndRemove(e,n.filter((function(r){return a.getObject(t.key,r)===a.getObject(t.key,e)}))[0],t.key)}));var s={method:this.options.updateType+" ",url:function(e,t,r){return"number"==typeof e[t][r]||a.parse.isGuid(e[t][r])?"("+e[t][r]+")":e[t][r]instanceof Date?"("+e[t][r].toJSON()+")":"('"+e[t][r]+"')"},data:function(e,t){return JSON.stringify(e[t])+"\n\n"}};return this.generateBodyContent(e,t,s,r)},t.getField=function(e){return e.replace(/\./g,"/")},t.prototype.generateBodyContent=function(e,t,r,n){for(var o="",s=0;s<e.length;s++)o+="\n"+t.cSet+"\n",o+=this.options.changeSetContent+"\n\n",o+=r.method,"POST "===r.method?o+=(n.dataSource.insertUrl||n.dataSource.crudUrl||t.url)+r.url(e,s,t.key)+" HTTP/1.1\n":"PUT "===r.method||"PATCH "===r.method?o+=(n.dataSource.updateUrl||n.dataSource.crudUrl||t.url)+r.url(e,s,t.key)+" HTTP/1.1\n":"DELETE "===r.method&&(o+=(n.dataSource.removeUrl||n.dataSource.crudUrl||t.url)+r.url(e,s,t.key)+" HTTP/1.1\n"),o+="Accept: "+this.options.accept+"\n",o+="Content-Id: "+this.pvt.changeSet+++"\n",o+=this.options.batchChangeSetContentType+"\n",sf.base.isNullOrUndefined(e[s]["@odata.etag"])?o+="\n":(o+="If-Match: "+e[s]["@odata.etag"]+"\n\n",delete e[s]["@odata.etag"]),o+=r.data(e,s);return o},t.prototype.processBatchResponse=function(e,t,r,n,o){if(r&&r.headers.get("Content-Type")&&-1!==r.headers.get("Content-Type").indexOf("xml"))return t.isCountRequired?{result:[],count:0}:[];if(n&&this.options.batch&&a.endsWith(n.url,this.options.batch)&&"post"===n.type.toLowerCase()){var s=r.headers.get("Content-Type"),i=void 0,u=void 0,c=e+"";if(s=s.substring(s.indexOf("=batchresponse")+1),(c=c.split(s)).length<2)return{};c=c[1];var l=/(?:\bContent-Type.+boundary=)(changesetresponse.+)/i.exec(c);l&&c.replace(l[0],"");for(var d=l?l[1]:"",p=(c=c.split(d)).length;p>-1;p--)/\bContent-ID:/i.test(c[p])&&/\bHTTP.+201/.test(c[p])&&(i=parseInt(/\bContent-ID: (\d+)/i.exec(c[p])[1],10),o.addedRecords[i]&&(u=a.parse.parseJson(/^\{.+\}/m.exec(c[p])[0]),sf.base.extend({},o.addedRecords[i],this.processResponse(u))));return o}return null},t.prototype.compareAndRemove=function(e,t,r){var n=this;return sf.base.isNullOrUndefined(t)||Object.keys(e).forEach((function(o){o!==r&&"@odata.etag"!==o&&(a.isPlainObject(e[o])?(n.compareAndRemove(e[o],t[o]),0===Object.keys(e[o]).filter((function(e){return"@odata.etag"!==e})).length&&delete e[o]):(e[o]===t[o]||e[o]&&t[o]&&e[o].valueOf()===t[o].valueOf())&&delete e[o])})),e},t}(d),f=function(e){function t(t){var r=e.call(this,t)||this;return r.options=sf.base.extend({},r.options,{requestType:"get",accept:"application/json, text/javascript, */*; q=0.01",multipartAccept:"multipart/mixed",sortBy:"$orderby",select:"$select",skip:"$skip",take:"$top",count:"$count",search:"$search",where:"$filter",expand:"$expand",batch:"$batch",changeSet:"--changeset_",batchPre:"batch_",contentId:"Content-Id: ",batchContent:"Content-Type: multipart/mixed; boundary=",changeSetContent:"Content-Type: application/http\nContent-Transfer-Encoding: binary ",batchChangeSetContentType:"Content-Type: application/json; charset=utf-8 ",updateType:"PATCH",localTime:!1,apply:"$apply"}),sf.base.extend(r.options,t||{}),r}return i(t,e),t.prototype.getModuleName=function(){return"ODataV4Adaptor"},t.prototype.onCount=function(e){return!0===e?"true":""},t.prototype.onPredicate=function(t,r,o){var s="",i=t.value,u=i instanceof Date;if(r instanceof n)for(var c=this.getQueryRequest(r),l=0;l<c.filters.length;l++)c.filters[l].e.key===t.value&&(o=!0);return s=e.prototype.onPredicate.call(this,t,r,o),u&&(s=s.replace(/datetime'(.*)'$/,"$1")),a.parse.isGuid(i)&&(s=s.replace("guid","").replace(/'/g,"")),s},t.prototype.onEachSearch=function(e){var t=this.pvt.searches||[];t.push(e.key),this.pvt.searches=t},t.prototype.onSearch=function(e){return this.pvt.searches.join(" OR ")},t.prototype.onExpand=function(e){var t=this,r={},n={},o=e.expands.slice(),s=[];return e.selects.filter((function(e){return e.indexOf(".")>-1})).forEach((function(e){var n=e.split(".");if(n[0]in r||(r[n[0]]=[]),2===n.length)r[n[0]].length&&-1!==Object.keys(r).indexOf(n[0])?-1!==r[n[0]][0].indexOf("$expand")&&-1===r[n[0]][0].indexOf(";$select=")?r[n[0]][0]=r[n[0]][0]+";$select="+n[1]:r[n[0]][0]=r[n[0]][0]+","+n[1]:r[n[0]].push("$select="+n[1]);else{for(var o="$select="+n[n.length-1],s="",a="",i=1;i<n.length-1;i++)s=s+"$expand="+n[i]+"(",a+=")";var u=s+o+a;if(r[n[0]].length&&-1!==Object.keys(r).indexOf(n[0])&&t.expandQueryIndex(r[n[0]],!0)){var c=t.expandQueryIndex(r[n[0]]);r[n[0]][c]=r[n[0]][c]+u.replace("$expand=",",")}else r[n[0]].push(u)}})),Object.keys(r).forEach((function(e){-1===o.indexOf(e)&&o.push(e)})),o.forEach((function(e){n[e]=e in r?e+"("+r[e].join(";")+")":e})),Object.keys(n).forEach((function(e){return s.push(n[e])})),s.join(",")},t.prototype.expandQueryIndex=function(e,t){for(var r=0;r<e.length;r++)if(-1!==e[r].indexOf("$expand"))return!!t||r;return!t&&0},t.prototype.onDistinct=function(e){return"groupby(("+e.map((function(e){return p.getField(e)})).join(",")+"))"},t.prototype.onSelect=function(t){return e.prototype.onSelect.call(this,t.filter((function(e){return-1===e.indexOf(".")})))},t.prototype.beforeSend=function(e,t,r){"POST"!==r.type&&"PUT"!==r.type&&"PATCH"!==r.type||t.headers.set("Prefer","return=representation"),t.headers.set("Accept",this.options.accept)},t.prototype.processResponse=function(t,r,n,o,s,i){if(s&&"GET"===s.type&&!this.rootUrl&&(t["@odata.context"]||t["@context"])){var u=t["@odata.context"]?t["@odata.context"].split("/$metadata#"):t["@context"].split("/$metadata#");this.rootUrl=u[0],this.resourceTableName=u[1]}var c=s&&s.pvtData,l=e.prototype.processBatchResponse.call(this,t,n,o,s,i);if(l)return l;var d=null;n&&n.isCountRequired&&("@odata.count"in t?d=t["@odata.count"]:"@count"in t&&(d=t["@count"])),t=sf.base.isNullOrUndefined(t.value)?t:t.value;var p={};return p.count=d,p.result=t,this.getAggregateResult(c,t,p,null,n),a.isNull(d)?p.result:{result:p.result,count:d,aggregates:p.aggregates}},t}(p),h=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return i(t,e),t.prototype.getModuleName=function(){return"WebApiAdaptor"},t.prototype.insert=function(e,t,r){return{type:"POST",url:e.dataSource.url,data:JSON.stringify(t)}},t.prototype.remove=function(e,t,r,n){return{type:"DELETE",url:e.dataSource.url+"/"+r,data:JSON.stringify(r)}},t.prototype.update=function(e,t,r,n){return{type:"PUT",url:e.dataSource.url,data:JSON.stringify(r)}},t.prototype.batchRequest=function(e,t,r){var n=this,o=r.guid=a.getGuid(this.options.batchPre),s=e.dataSource.url.replace(/\/*$/,"/"+this.options.batch);r.url=this.resourceTableName?this.resourceTableName:r.url;for(var i=[],u=function(n,s){t.addedRecords.forEach((function(s,a){var u=function(e,t,r){return""};i.push("--"+o),i.push("Content-Type: application/http; msgtype=request",""),i.push("POST /api/"+(e.dataSource.insertUrl||e.dataSource.crudUrl||r.url)+u(t.addedRecords,n,r.key)+" HTTP/1.1"),i.push("Content-Type: application/json; charset=utf-8"),i.push("Host: "+location.host),i.push("",s?JSON.stringify(s):"")}))},c=0,l=t.addedRecords.length;c<l;c++)u(c);var d=function(s,a){t.changedRecords.forEach((function(a,u){n.options.updateType;var c=function(e,t,r){return""};i.push("--"+o),i.push("Content-Type: application/http; msgtype=request",""),i.push("PUT /api/"+(e.dataSource.updateUrl||e.dataSource.crudUrl||r.url)+c(t.changedRecords,s,r.key)+" HTTP/1.1"),i.push("Content-Type: application/json; charset=utf-8"),i.push("Host: "+location.host),i.push("",a?JSON.stringify(a):"")}))};for(c=0,l=t.changedRecords.length;c<l;c++)d(c);var p=function(n,s){t.deletedRecords.forEach((function(s,u){var c=function(e,t,r){var n=a.getObject(r,e[t]);return"number"==typeof n||a.parse.isGuid(n)?"/"+n:n instanceof Date?"/"+e[t][r].toJSON():"/'"+n+"'"};i.push("--"+o),i.push("Content-Type: application/http; msgtype=request",""),i.push("DELETE /api/"+(e.dataSource.removeUrl||e.dataSource.crudUrl||r.url)+c(t.deletedRecords,n,r.key)+" HTTP/1.1"),i.push("Content-Type: application/json; charset=utf-8"),i.push("Host: "+location.host),i.push("",s?JSON.stringify(s):"")}))};for(c=0,l=t.deletedRecords.length;c<l;c++)p(c);return i.push("--"+o+"--",""),{type:"POST",url:s,contentType:"multipart/mixed; boundary="+o,data:i.join("\r\n")}},t.prototype.beforeSend=function(e,t,r){t.headers.set("Accept","application/json, text/javascript, */*; q=0.01")},t.prototype.processResponse=function(e,t,r,n,o,s){var i=o&&o.pvtData,u=null,c={};if(o&&"post"!==o.type.toLowerCase()){var l=n&&o.fetchRequest.headers.get("DataServiceVersion"),d=l&&parseInt(l,10)||2;r&&r.isCountRequired&&(a.isNull(e.Count)||(u=e.Count)),d<3&&e.Items&&(e=e.Items),c.count=u,c.result=e,this.getAggregateResult(i,e,c,null,r)}return c.result=c.result||e,a.isNull(u)?c.result:{result:c.result,count:c.count,aggregates:c.aggregates}},t}(p),g=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return i(t,e),t.prototype.processQuery=function(e,t,r){var n=(new d).processQuery(e,t,r),o=a.parse.parseJson(n.data),s={};if(o.param)for(var i=0;i<o.param.length;i++){var u=o.param[i],c=Object.keys(u)[0];s[c]=u[c]}s.value=o;return{data:JSON.stringify(s,a.parse.jsonDateReplacer),url:n.url,pvtData:n.pvtData,type:"POST",contentType:"application/json; charset=utf-8"}},t}(d),y=function(e){function t(){var t=e.call(this)||this;return sf.base.setValue("beforeSend",d.prototype.beforeSend,t),t}return i(t,e),t.prototype.insert=function(e,t,r,n,o){return this.pvt.position=o,this.updateType="add",{url:e.dataSource.insertUrl||e.dataSource.crudUrl||e.dataSource.url,data:JSON.stringify(sf.base.extend({},{value:t,table:r,action:"insert"},a.getAddParams(this,e,n)))}},t.prototype.remove=function(t,r,n,o,s){return e.prototype.remove.call(this,t,r,n),{type:"POST",url:t.dataSource.removeUrl||t.dataSource.crudUrl||t.dataSource.url,data:JSON.stringify(sf.base.extend({},{key:n,keyColumn:r,table:o,action:"remove"},a.getAddParams(this,t,s)))}},t.prototype.update=function(e,t,r,n,o){return this.updateType="update",this.updateKey=t,{type:"POST",url:e.dataSource.updateUrl||e.dataSource.crudUrl||e.dataSource.url,data:JSON.stringify(sf.base.extend({},{value:r,action:"update",keyColumn:t,key:r[t],table:n},a.getAddParams(this,e,o)))}},t.prototype.processResponse=function(t,r,n,o,s,i,u){var c;if(t="batch"===(s?JSON.parse(s.data):t).action?a.parse.parseJson(t):t,"add"===this.updateType&&e.prototype.insert.call(this,r,t,null,null,this.pvt.position),"update"===this.updateType&&e.prototype.update.call(this,r,this.updateKey,t),this.updateType=void 0,t.added)for(c=0;c<t.added.length;c++)e.prototype.insert.call(this,r,t.added[c]);if(t.changed)for(c=0;c<t.changed.length;c++)e.prototype.update.call(this,r,u.key,t.changed[c]);if(t.deleted)for(c=0;c<t.deleted.length;c++)e.prototype.remove.call(this,r,u.key,t.deleted[c]);return t},t.prototype.batchRequest=function(e,t,r,n,o){return{type:"POST",url:e.dataSource.batchUrl||e.dataSource.crudUrl||e.dataSource.url,contentType:"application/json; charset=utf-8",dataType:"json",data:JSON.stringify(sf.base.extend({},{changed:t.changedRecords,added:t.addedRecords,deleted:t.deletedRecords,action:"batch",table:r.url,key:r.key},a.getAddParams(this,e,n)))}},t.prototype.addParams=function(e){(new d).addParams(e)},t}(l),v=function(e){function t(t){var r=e.call(this)||this;return r.options=sf.base.extend({},r.options,{getData:new Function,addRecord:new Function,updateRecord:new Function,deleteRecord:new Function,batchUpdate:new Function}),sf.base.extend(r.options,t||{}),r}return i(t,e),t.prototype.getModuleName=function(){return"CustomDataAdaptor"},t}(d),m=function(e){function t(t){var r=e.call(this)||this;return r.opt=t,r.schema=r.opt.response,r.query=r.opt.query,r.getVariables=r.opt.getVariables?r.opt.getVariables:function(){},r.getQuery=function(){return r.query},r}return i(t,e),t.prototype.getModuleName=function(){return"GraphQLAdaptor"},t.prototype.processQuery=function(t,r){var n=e.prototype.processQuery.apply(this,arguments),o=JSON.parse(n.data),s=["skip","take","sorted","table","select","where","search","requiresCounts","aggregates","params"],a={},i="searchwhereparams";s.filter((function(e){a[e]=i.indexOf(e)>-1?JSON.stringify(o[e]):o[e]}));var u=this.getVariables()||{};u.datamanager=a;var c=JSON.stringify({query:this.getQuery(),variables:u});return n.data=c,n},t.prototype.processResponse=function(e,t,r,n,o){var s,i,u=e,c=sf.base.getValue(this.schema.result,u.data);this.schema.count&&(s=sf.base.getValue(this.schema.count,u.data)),this.schema.aggregates&&(i=sf.base.getValue(this.schema.aggregates,u.data),i=sf.base.isNullOrUndefined(i)?i:a.parse.parseJson(i));var l=o.pvtData||{},d={result:c,aggregates:i},p=d;return l&&l.groups&&l.groups.length&&this.getAggregateResult(l,p,d,null,r),sf.base.isNullOrUndefined(s)?d.result:{result:d.result,count:s,aggregates:i}},t.prototype.insert=function(){var t=e.prototype.insert.apply(this,arguments);return this.generateCrudData(t,"insert")},t.prototype.update=function(){var t=e.prototype.update.apply(this,arguments);return this.generateCrudData(t,"update")},t.prototype.remove=function(){var t=e.prototype.remove.apply(this,arguments);return this.generateCrudData(t,"remove")},t.prototype.batchRequest=function(t,r,n,o,s){var a=e.prototype.batchRequest.apply(this,arguments),i=JSON.parse(a.data);return i.key=n.key,a.data=JSON.stringify(i),this.generateCrudData(a,"batch")},t.prototype.generateCrudData=function(e,t){var r=JSON.parse(e.data);return e.data=JSON.stringify({query:this.opt.getMutation(t),variables:r}),e},t}(d),b=function(e){function t(t,r,n){var o=e.call(this)||this;o.isCrudAction=!1,o.isInsertAction=!1,sf.base.isNullOrUndefined(t)||(o.cacheAdaptor=t),o.pageSize=n,o.guidId=a.getGuid("cacheAdaptor");window.localStorage.setItem(o.guidId,JSON.stringify({keys:[],results:[]}));var s=o.guidId;return sf.base.isNullOrUndefined(r)||setInterval((function(){for(var e=a.parse.parseJson(window.localStorage.getItem(s)),t=[],n=0;n<e.results.length;n++){var o=+new Date,i=+new Date(e.results[n].timeStamp);e.results[n].timeStamp=o-i,o-i>r&&t.push(n)}for(n=0;n<t.length;n++)e.results.splice(t[n],1),e.keys.splice(t[n],1);window.localStorage.removeItem(s),window.localStorage.setItem(s,JSON.stringify(e))}),r),o}return i(t,e),t.prototype.generateKey=function(e,t){var r=this.getQueryRequest(t),o=n.filterQueryLists(t.queries,["onSelect","onPage","onSkip","onTake","onRange"]),s=e;"onPage"in o&&(s+=o.onPage.pageIndex),r.sorts.forEach((function(e){s+=e.e.direction+e.e.fieldName})),r.groups.forEach((function(e){s+=e.e.fieldName})),r.searches.forEach((function(e){s+=e.e.searchKey}));for(var a=0;a<r.filters.length;a++){var i=r.filters[a];if(i.e.isComplex){var u=t.clone();u.queries=[];for(var c=0;c<i.e.predicates.length;c++)u.queries.push({fn:"onWhere",e:i.e.predicates[c],filter:t.queries.filter});s+=i.e.condition+this.generateKey(e,u)}else s+=i.e.field+i.e.operator+i.e.value}return s},t.prototype.processQuery=function(e,t,r){var n=this.generateKey(e.dataSource.url,t),o=a.parse.parseJson(window.localStorage.getItem(this.guidId)),s=o?o.results[o.keys.indexOf(n)]:null;return null==s||this.isCrudAction||this.isInsertAction?(this.isCrudAction=null,this.isInsertAction=null,this.cacheAdaptor.processQuery.apply(this.cacheAdaptor,[].slice.call(arguments,0))):s},t.prototype.processResponse=function(e,t,r,n,o,s){if(this.isInsertAction||o&&this.cacheAdaptor.options.batch&&a.endsWith(o.url,this.cacheAdaptor.options.batch)&&"post"===o.type.toLowerCase())return this.cacheAdaptor.processResponse(e,t,r,n,o,s);e=this.cacheAdaptor.processResponse.apply(this.cacheAdaptor,[].slice.call(arguments,0));var i=r?this.generateKey(t.dataSource.url,r):t.dataSource.url,u={},c=(u=a.parse.parseJson(window.localStorage.getItem(this.guidId))).keys.indexOf(i);for(-1!==c&&(u.results.splice(c,1),u.keys.splice(c,1)),u.results[u.keys.push(i)-1]={keys:i,result:e.result,timeStamp:new Date,count:e.count};u.results.length>this.pageSize;)u.results.splice(0,1),u.keys.splice(0,1);return window.localStorage.setItem(this.guidId,JSON.stringify(u)),e},t.prototype.beforeSend=function(e,t,r){!sf.base.isNullOrUndefined(this.cacheAdaptor.options.batch)&&a.endsWith(r.url,this.cacheAdaptor.options.batch)&&"post"===r.type.toLowerCase()&&t.headers.set("Accept",this.cacheAdaptor.options.multipartAccept),e.dataSource.crossDomain||t.headers.set("Accept",this.cacheAdaptor.options.accept)},t.prototype.update=function(e,t,r,n){return this.isCrudAction=!0,this.cacheAdaptor.update(e,t,r,n)},t.prototype.insert=function(e,t,r){return this.isInsertAction=!0,this.cacheAdaptor.insert(e,t,r)},t.prototype.remove=function(e,t,r,n){return this.isCrudAction=!0,this.cacheAdaptor.remove(e,t,r,n)},t.prototype.batchRequest=function(e,t,r){return this.cacheAdaptor.batchRequest(e,t,r)},t}(d),O=function(){function e(e,t,o){var s,i=this;return this.dateParse=!0,this.timeZoneHandling=!0,this.persistQuery={},this.isInitialLoad=!1,this.requests=[],this.isInitialLoad=!0,e||this.dataSource||(e=[]),o=o||e.adaptor,e&&!1===e.timeZoneHandling&&(this.timeZoneHandling=e.timeZoneHandling),e instanceof Array?s={json:e,offline:!0}:"object"===r(e)?(e.json||(e.json=[]),e.enablePersistence||(e.enablePersistence=!1),e.id||(e.id=""),e.ignoreOnPersist||(e.ignoreOnPersist=[]),s={url:e.url,insertUrl:e.insertUrl,removeUrl:e.removeUrl,updateUrl:e.updateUrl,crudUrl:e.crudUrl,batchUrl:e.batchUrl,json:e.json,headers:e.headers,accept:e.accept,data:e.data,timeTillExpiration:e.timeTillExpiration,cachingPageSize:e.cachingPageSize,enableCaching:e.enableCaching,requestType:e.requestType,key:e.key,crossDomain:e.crossDomain,jsonp:e.jsonp,dataType:e.dataType,offline:void 0!==e.offline?e.offline:!(e.adaptor instanceof y||e.adaptor instanceof v)&&!e.url,requiresFormat:e.requiresFormat,enablePersistence:e.enablePersistence,id:e.id,ignoreOnPersist:e.ignoreOnPersist}):a.throwError("DataManager: Invalid arguments"),void 0!==s.requiresFormat||a.isCors()||(s.requiresFormat=!!sf.base.isNullOrUndefined(s.crossDomain)||s.crossDomain),void 0===s.dataType&&(s.dataType="json"),this.dataSource=s,this.defaultQuery=t,this.dataSource.enablePersistence&&this.dataSource.id&&window.addEventListener("unload",this.setPersistData.bind(this)),s.url&&s.offline&&!s.json.length?(this.isDataAvailable=!1,this.adaptor=o||new p,this.dataSource.offline=!1,this.ready=this.executeQuery(t||new n),this.ready.then((function(e){i.dataSource.offline=!0,i.isDataAvailable=!0,s.json=e.result,i.adaptor=new l}))):this.adaptor=s.offline?new l:new p,!s.jsonp&&this.adaptor instanceof p&&(s.jsonp="callback"),this.adaptor=o||this.adaptor,s.enableCaching&&(this.adaptor=new b(this.adaptor,s.timeTillExpiration,s.cachingPageSize)),this}return e.prototype.getPersistedData=function(e){var t=localStorage.getItem(e||this.dataSource.id);return JSON.parse(t)},e.prototype.setPersistData=function(e,t,r){localStorage.setItem(t||this.dataSource.id,JSON.stringify(r||this.persistQuery))},e.prototype.setPersistQuery=function(e){var t=this,r=this.getPersistedData();if(this.isInitialLoad&&r&&Object.keys(r).length){this.persistQuery=r,this.persistQuery.queries=this.persistQuery.queries.filter((function(e){if(t.dataSource.ignoreOnPersist&&t.dataSource.ignoreOnPersist.length&&e.fn&&t.dataSource.ignoreOnPersist.some((function(t){return e.fn===t})))return!1;if("onWhere"===e.fn){var r=e.e;if(r&&r.isComplex&&r.predicates instanceof Array){var n=r.predicates.map((function(e){if(e.predicates&&e.predicates instanceof Array){var t=e.predicates.map((function(e){var t=e.field,r=e.operator,n=e.value,s=e.ignoreCase,a=e.ignoreAccent,i=e.matchCase;return new o(t,r,n,s,a,i)}));return"and"===e.condition?o.and(t):o.or(t)}var r=e.field,n=e.operator,s=e.value,a=e.ignoreCase,i=e.ignoreAccent,u=e.matchCase;return new o(r,n,s,a,i,u)}));e.e=new o(n[0],r.condition,n.slice(1))}}return!0}));var s=sf.base.extend(new n,this.persistQuery);return this.isInitialLoad=!1,s}return this.persistQuery=e,this.isInitialLoad=!1,e},e.prototype.setDefaultQuery=function(e){return this.defaultQuery=e,this},e.prototype.executeLocal=function(t){this.defaultQuery||t instanceof n||a.throwError("DataManager - executeLocal() : A query is required to execute"),this.dataSource.json||a.throwError("DataManager - executeLocal() : Json data is required to execute"),this.dataSource.enablePersistence&&this.dataSource.id&&(t=this.setPersistQuery(t)),t=t||this.defaultQuery;var r=this.adaptor.processQuery(this,t);if(t.subQuery){var o=t.subQuery.fromTable,s=t.subQuery.lookups,i=t.isCountRequired?r.result:r;s&&s instanceof Array&&a.buildHierarchy(t.subQuery.fKey,o,i,s,t.subQuery.key);for(var u=0;u<i.length;u++)i[u][o]instanceof Array&&(i[u]=sf.base.extend({},{},i[u]),i[u][o]=this.adaptor.processResponse(t.subQuery.using(new e(i[u][o].slice(0))).executeLocal(),this,t))}return this.adaptor.processResponse(r,this,t)},e.prototype.executeQuery=function(t,r,o,s){var i=this;this.dataSource.enablePersistence&&this.dataSource.id&&(t=this.setPersistQuery(t)),"function"==typeof t&&(s=o,o=r,r=t,t=null),t||(t=this.defaultQuery),t instanceof n||a.throwError("DataManager - executeQuery() : A query is required to execute");var u=new S,c={query:t};if(!this.dataSource.offline&&void 0!==this.dataSource.url&&""!==this.dataSource.url||!sf.base.isNullOrUndefined(this.adaptor.makeRequest)||this.isCustomDataAdaptor(this.adaptor)){var l=this.adaptor.processQuery(this,t);sf.base.isNullOrUndefined(this.adaptor.makeRequest)?!sf.base.isNullOrUndefined(l.url)||this.isCustomDataAdaptor(this.adaptor)?(this.requests=[],this.makeRequest(l,u,c,t)):(c=e.getDeferedArgs(t,l,c),u.resolve(c)):this.adaptor.makeRequest(l,u,c,t)}else e.nextTick((function(){var r=i.executeLocal(t);c=e.getDeferedArgs(t,r,c),u.resolve(c)}));return(r||o)&&u.promise.then(r,o),s&&u.promise.then(s,s),u.promise},e.getDeferedArgs=function(e,t,r){return e.isCountRequired?(r.result=t.result,r.count=t.count,r.aggregates=t.aggregates):r.result=t,r},e.nextTick=function(e){(window.setImmediate||window.setTimeout)(e,0)},e.prototype.extendRequest=function(e,t,r){return sf.base.extend({},{type:"GET",dataType:this.dataSource.dataType,crossDomain:this.dataSource.crossDomain,jsonp:this.dataSource.jsonp,cache:!0,processData:!1,onSuccess:t,onFailure:r},e)},e.prototype.makeRequest=function(e,t,r,n){var o=this,s=!!n.subQuerySelector,i=function(e){r.error=e,t.reject(r)},u=function(e,n,o,s,a,i,u){r.xhr=o,r.count=n?parseInt(n.toString(),10):0,r.result=e,r.request=s,r.aggregates=i,r.actual=a,r.virtualSelectRecords=u,t.resolve(r)},c=function(e,t){var c=new S,l={parent:r};n.subQuery.isChild=!0;var d=o.adaptor.processQuery(o,n.subQuery,e?o.adaptor.processResponse(e):t),p=o.makeRequest(d,c,l,n.subQuery);return s||c.then((function(t){e&&(a.buildHierarchy(n.subQuery.fKey,n.subQuery.fromTable,e,t,n.subQuery.key),u(e,t.count,t.xhr))}),i),p},l=this.extendRequest(e,(function(e,t){if(o.isGraphQLAdaptor(o.adaptor)&&!sf.base.isNullOrUndefined(e.errors))return i(e.errors);o.isCustomDataAdaptor(o.adaptor)&&(t=sf.base.extend({},o.fetchReqOption,t)),-1===t.contentType.indexOf("xml")&&o.dateParse&&(e=a.parse.parseJson(e));var r=o.adaptor.processResponse(e,o,n,t.fetchRequest,t),l=0,d=null,p=e.virtualSelectRecords;n.isCountRequired&&(l=r.count,d=r.aggregates,r=r.result),n.subQuery?s||c(r,t):u(r,l,t.fetchRequest,t.type,e,d,p)}),i);if(this.isCustomDataAdaptor(this.adaptor)){this.fetchReqOption=l;var d=l;this.adaptor.options.getData({data:d.data,onSuccess:d.onSuccess,onFailure:d.onFailure})}else{var p=new sf.base.Fetch(l);p.beforeSend=function(){o.beforeSend(p.fetchRequest,p)},(l=p.send()).catch((function(e){return!0})),this.requests.push(p)}if(s){var f=n.subQuerySelector.call(this,{query:n.subQuery,parent:n});f&&f.length?Promise.all([l,c(null,f)]).then((function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var r=e[0],i=o.adaptor.processResponse(r[0],o,n,o.requests[0].fetchRequest,o.requests[0]),c=0;n.isCountRequired&&(c=i.count,i=i.result);var l=o.adaptor.processResponse(r[1],o,n.subQuery,o.requests[1].fetchRequest,o.requests[1]);c=0,n.subQuery.isCountRequired&&(c=l.count,l=l.result),a.buildHierarchy(n.subQuery.fKey,n.subQuery.fromTable,i,l,n.subQuery.key),s=!1,u(i,c,o.requests[0].fetchRequest)})):s=!1}return l},e.prototype.beforeSend=function(e,t){this.adaptor.beforeSend(this,e,t);for(var r,n=this.dataSource.headers,o=0;n&&o<n.length;o++){r=[];for(var s=0,a=Object.keys(n[o]);s<a.length;s++){var i=a[s];r.push(i),e.headers.set(i,n[o][i])}}},e.prototype.saveChanges=function(e,t,r,o,s){var a=this;r instanceof n&&(o=r,r=null);var i={url:r,key:t||this.dataSource.key},u=this.adaptor.batchRequest(this,e,i,o||new n,s);if(this.dataSource.offline)return u;if(sf.base.isNullOrUndefined(this.adaptor.dofetchRequest)){if(this.isCustomDataAdaptor(this.adaptor))return this.dofetchRequest(u,this.adaptor.options.batchUpdate);var c=new S,l=new sf.base.Fetch(u);return l.beforeSend=function(){a.beforeSend(l.fetchRequest,l)},l.onSuccess=function(t,r){a.isGraphQLAdaptor(a.adaptor)&&(sf.base.isNullOrUndefined(t.errors)||l.onFailure(JSON.stringify(t.errors))),c.resolve(a.adaptor.processResponse(t,a,null,r.fetchRequest,r,e,i))},l.onFailure=function(e){c.reject([{error:e}])},l.send().catch((function(e){return!0})),c.promise}return this.adaptor.dofetchRequest(u)},e.prototype.insert=function(e,t,r,o){t instanceof n&&(r=t,t=null);var s=this.adaptor.insert(this,e,t,r,o);return this.dataSource.offline?s:sf.base.isNullOrUndefined(this.adaptor.dofetchRequest)?this.dofetchRequest(s,this.adaptor.options.addRecord):this.adaptor.dofetchRequest(s)},e.prototype.remove=function(e,t,o,s){"object"===r(t)&&(t=a.getObject(e,t)),o instanceof n&&(s=o,o=null);var i=this.adaptor.remove(this,e,t,o,s);if(this.dataSource.offline)return i;if(sf.base.isNullOrUndefined(this.adaptor.dofetchRequest)){var u=this.adaptor.options.deleteRecord;return this.dofetchRequest(i,u)}return this.adaptor.dofetchRequest(i)},e.prototype.update=function(e,t,r,o,s){r instanceof n&&(o=r,r=null);var a=this.adaptor.update(this,e,t,r,o,s);if(this.dataSource.offline)return a;if(sf.base.isNullOrUndefined(this.adaptor.dofetchRequest)){var i=this.adaptor.options.updateRecord;return this.dofetchRequest(a,i)}return this.adaptor.dofetchRequest(a)},e.prototype.isCustomDataAdaptor=function(e){return this.adaptor.getModuleName&&"CustomDataAdaptor"===this.adaptor.getModuleName()},e.prototype.isGraphQLAdaptor=function(e){return this.adaptor.getModuleName&&"GraphQLAdaptor"===this.adaptor.getModuleName()},e.prototype.successFunc=function(e,t){if(this.isGraphQLAdaptor(this.adaptor)){var n="object"===r(e)?e:JSON.parse(e);sf.base.isNullOrUndefined(n.errors)||this.failureFunc(JSON.stringify(n.errors))}this.isCustomDataAdaptor(this.adaptor)&&(t=sf.base.extend({},this.fetchReqOption,t));try{a.parse.parseJson(e)}catch(t){e=[]}e=this.adaptor.processResponse(a.parse.parseJson(e),this,null,t.fetchRequest,t),this.fetchDeffered.resolve(e)},e.prototype.failureFunc=function(e){this.fetchDeffered.reject([{error:e}])},e.prototype.dofetchRequest=function(e,t){var r=this;if(e=sf.base.extend({},{type:"POST",contentType:"application/json; charset=utf-8",processData:!1},e),this.fetchDeffered=new S,this.isCustomDataAdaptor(this.adaptor))this.fetchReqOption=e,t.call(this,{data:e.data,onSuccess:this.successFunc.bind(this),onFailure:this.failureFunc.bind(this)});else{var n=new sf.base.Fetch(e);n.beforeSend=function(){r.beforeSend(n.fetchRequest,n)},n.onSuccess=this.successFunc.bind(this),n.onFailure=this.failureFunc.bind(this),n.send().catch((function(e){return!0}))}return this.fetchDeffered.promise},e.prototype.clearPersistence=function(){window.removeEventListener("unload",this.setPersistData.bind(this)),this.dataSource.enablePersistence=!1,this.persistQuery={},window.localStorage.setItem(this.dataSource.id,"[]")},e}(),S=function(){var e=this;this.promise=new Promise((function(t,r){e.resolve=t,e.reject=r})),this.then=this.promise.then.bind(this.promise),this.catch=this.promise.catch.bind(this.promise)};return e.Adaptor=c,e.CacheAdaptor=b,e.CustomDataAdaptor=v,e.DataManager=O,e.DataUtil=a,e.Deferred=S,e.GraphQLAdaptor=m,e.JsonAdaptor=l,e.ODataAdaptor=p,e.ODataV4Adaptor=f,e.Predicate=o,e.Query=n,e.RemoteSaveAdaptor=y,e.UrlAdaptor=d,e.WebApiAdaptor=h,e.WebMethodAdaptor=g,e},sf.data=sf.data({}),sfBlazor.initBlazorAdaptor()}}]);(async()=>{await import(`${document.baseURI}_content/Syncfusion.Blazor/scripts/syncfusion-blazor-base.min.js?v=26.1.undefined`).then(()=>{})})();