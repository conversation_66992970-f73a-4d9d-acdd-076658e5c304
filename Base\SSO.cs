﻿using Newtonsoft.Json.Linq;
using Newtonsoft.Json;
using OAKsIlanApp.OAKsIlanApp;
using OAKsIlanAppUser.OAKsIlanAppUser;
using OAKsIlanOrgTree.OAKsIlanOrgTree;

namespace Intra2025.Components.Base
{
    public class SSO
    {
        private readonly IHttpContextAccessor? _httpContextAccessor;
        public SSO(IHttpContextAccessor httpContextAccessor)
        {
            _httpContextAccessor = httpContextAccessor;
        }

        public string GetSSOInf()
        {
            // 設定主機訊息
            string api_url = "https://eipapi.e-land.gov.tw";
            string app_private_id = "21f8e88644556329aba29bab8b202779"; 
            string app_private_passwd = "4c86b69ff427c9dcaaa44b97b7a8f3bb";
            string public_app_user_sso_token = "";
            string l1_dept_code = "";
            string APP_USER_EMAIL = "";
            string APP_USER_CHT_NAME = "....";


            if (_httpContextAccessor?.HttpContext?.Request?.Cookies["PUBLIC_APP_USER_SSO_TOKEN"] != null) //判斷是否有取到token
            {
                var cookieValue = _httpContextAccessor.HttpContext.Request.Cookies["PUBLIC_APP_USER_SSO_TOKEN"];
                public_app_user_sso_token = System.Net.WebUtility.HtmlEncode(cookieValue?.ToString() ?? "");

                // 2-1. 應用系統身分認證===============================================
                string PRIVILEGED_APP_SSO_TOKEN = "";
                string PUBLIC_APP_SSO_TOKEN = "";
                string PRIVATE_APP_SSO_TOKEN = "";
                JObject Authjobj = new JObject();
                Authjobj.Add("APP_PRIVATE_ID", app_private_id);
                Authjobj.Add("APP_PRIVATE_PASSWD", app_private_passwd);
                app lanAppAuth = new app(api_url);
                JObject ResponseObj = new JObject();
                ResponseObj = lanAppAuth.call_ws("WS-Z01-A0-01", Authjobj);
                // 取得TOKEN，並回傳時沒有error code
                if (ResponseObj["ERROR_CODE"]?.ToString()?.Equals("0") == true)
                {
                    PRIVILEGED_APP_SSO_TOKEN = ResponseObj["PRIVILEGED_APP_SSO_TOKEN"]?.ToString() ?? "";
                    PUBLIC_APP_SSO_TOKEN = ResponseObj["PUBLIC_APP_SSO_TOKEN"]?.ToString() ?? "";
                    PRIVATE_APP_SSO_TOKEN = ResponseObj["PRIVATE_APP_SSO_TOKEN"]?.ToString() ?? "";
                }
                else
                {
                    //Response.Write("2-1===>" + ResponseObj["ERROR_CODE"]);
                    //Response.Write("<BR><BR><BR><BR>");
                }

                // 2-2. 取得使用者內部代碼物件===============================================
                JObject UserJobj = new JObject();
                UserJobj.Add("PRIVILEGED_APP_SSO_TOKEN", PRIVILEGED_APP_SSO_TOKEN);
                UserJobj.Add("PUBLIC_APP_USER_SSO_TOKEN_TO_QUERY", public_app_user_sso_token);
                app_user lanAppUser = new app_user(api_url);
                JObject ResponseUserObj = new JObject();
                string app_compy_uuid = "";
                string app_user_node_uuid = "";
                ResponseUserObj = lanAppUser.call_ws("WS-Z01-B0-06", UserJobj);
                if (ResponseUserObj["ERROR_CODE"]?.ToString()?.Equals("0") == true)
                {
                    app_compy_uuid = ResponseUserObj["APP_COMPANY_UUID"]?.ToString() ?? "";
                    app_user_node_uuid = ResponseUserObj["APP_USER_NODE_UUID"]?.ToString() ?? "";
                }
                else
                {
                    string output = "WS-Z01-B0-06" + "內部代碼認證失敗:<BR>" + ResponseUserObj["ERROR_CODE"];
                    //Response.Write(output);
                }

                // 2-3. 取得單一使用者節點屬性===============================================
                string OrgStr = GenerateJsonQuestString(PRIVILEGED_APP_SSO_TOKEN, public_app_user_sso_token, app_compy_uuid, app_user_node_uuid)?.ToString() ?? "";

                JObject? OrgSendObj = JsonConvert.DeserializeObject<JObject>(OrgStr);
                // New出查詢部門、人員資料元件，須傳入API網址
                org_tree lanOrgtree = new org_tree(api_url);
                JObject ResponseOrgObj = new JObject();
                // 傳入ws編號和參數(Json格式)
                if (OrgSendObj != null)
                {
                    ResponseOrgObj = lanOrgtree.call_ws("WS-Z01A-D-B05", OrgSendObj);
                }
                string outputOrgtree = JsonConvert.SerializeObject(ResponseOrgObj);
                // 取得單一節點
                if (ResponseOrgObj["ERROR_CODE"]?.ToString()?.Equals("0") == true)
                {
                    // '2-4 使用者資訊帳號檢查
                    JObject? NodeObj = ResponseOrgObj["APP_USER_BASIC_PROFILE"] as JObject;
                    if (NodeObj != null)
                    {
                        APP_USER_CHT_NAME = NodeObj["APP_USER_CHT_NAME"]?.ToString() ?? ""; //中文姓名
                        APP_USER_EMAIL = NodeObj["APP_USER_EMAIL"]?.ToString() ?? "";  //MAIL
                        l1_dept_code = NodeObj["l1_dept_code"]?.ToString() ?? ""; //機關代碼,ex: 117
                        string l1_dept_name = NodeObj["l1_dept_name"]?.ToString() ?? ""; //機關名稱,ex: 計畫處
                        string parent_dept_code = NodeObj["parent_dept_code"]?.ToString() ?? ""; //次單位代s130 07
                        string parent_dept_name = NodeObj["parent_dept_name"]?.ToString() ?? ""; //次單位名稱,ex: 資管科
                    }

                    _httpContextAccessor?.HttpContext?.Session?.SetString("APP_USER_CHT_NAME", APP_USER_CHT_NAME);
                    // 獲取 APP_USER_HR_PROFILE 的信息
                    NodeObj = ResponseOrgObj["APP_USER_HR_PROFILE"] as JObject;
                    string AppUserTwSsn = NodeObj?["APP_USER_TW_SSN"]?.ToString() ?? "";

                    // 獲取 APP_USER_EMPLOY_PROFILE 的信息
                    NodeObj = ResponseOrgObj["APP_USER_EMPLOY_PROFILE"] as JObject;
                    string AppUserEmployTitle = NodeObj?["APP_USER_EMPLOY_TITLE"]?.ToString() ?? "";

                    //Response.Write("<BR>=========部門、人員資料:===========================================<BR>");
                    //Response.Write("<BR>" + outputOrgtree + "<BR><BR>");
                }
                else
                {
                    //Response.Write("2-4===>" + ResponseOrgObj["ERROR_CODE"]);
                }
            }
            else
            {
                APP_USER_EMAIL = "NO Login";
            }
            return APP_USER_CHT_NAME;
        }

        public object GenerateJsonQuestString(string sso_token, string user_sso_token, string company_uuid, string user_uuid)
        {
            StringWriter sw = new StringWriter();
            JsonTextWriter writer = new JsonTextWriter(sw);
            writer.WriteStartObject();
            writer.WritePropertyName("PRIVILEGED_APP_SSO_TOKEN");
            writer.WriteValue(sso_token);
            writer.WritePropertyName("APP_COMPANY_UUID");
            writer.WriteValue(company_uuid);
            writer.WritePropertyName("PUBLIC_APP_USER_SSO_TOKEN");
            writer.WriteValue(user_sso_token);
            writer.WritePropertyName("APP_USER_NODE_UUID");
            writer.WriteValue(user_uuid);
            writer.WritePropertyName("APP_USER_BASIC_PROFILE");
            writer.WriteStartObject();
            // =指定回傳 用戶帳號
            writer.WritePropertyName("APP_USER_LOGIN_ID");
            writer.WriteValue("");
            // =指定回傳 員工編號
            writer.WritePropertyName("APP_USER_EMPNO");
            writer.WriteValue("");
            // =指定回傳 用戶姓名
            writer.WritePropertyName("APP_USER_CHT_NAME");
            writer.WriteValue("");
            writer.WritePropertyName("APP_USER_ENG_NAME");
            writer.WriteValue("");
            // =指定回傳 用戶電子郵件
            writer.WritePropertyName("APP_USER_EMAIL");
            writer.WriteValue("");
            // ====指定回傳 用戶其他屬性資訊
            writer.WritePropertyName("APP_USER_OFFICE_PHONE_NO");
            writer.WriteValue("");
            writer.WritePropertyName("APP_USER_MOBILE_PHONE_NO");
            writer.WriteValue("");
            writer.WritePropertyName("APP_USER_OFFICE_FAX_NO");
            writer.WriteValue("");
            writer.WritePropertyName("APP_USER_MFP_CARD_NO");
            writer.WriteValue("");
            writer.WritePropertyName("APP_USER_OFFICE_ADDRESS");
            writer.WriteValue("");
            writer.WritePropertyName("APP_USER_OFFICE_ZIP_CODE");
            writer.WriteValue("");
            // ======
            // =指定回傳用戶狀態 1: 啟用 0: 停用 (包含 差勤離職、差勤留職停薪、差勤借調、差勤刪除、差勤病故、差勤退休)
            writer.WritePropertyName("APP_USER_STATUS");
            writer.WriteValue("");
            writer.WritePropertyName("APP_USER_NODE_LAST_UPDATE_TIME");
            writer.WriteValue("");
            writer.WritePropertyName("APP_USER_NODE_LAST_UPDATE_TAG");
            writer.WriteValue("");
            // =指定回傳 用戶內部部門代碼
            writer.WritePropertyName("APP_DEPT_NODE_UUID");
            writer.WriteValue("");
            // ===指定回傳用戶所屬機關、所屬單位、直屬單位資訊==
            // =指定回傳 機關代碼
            writer.WritePropertyName("org_code");
            writer.WriteValue("");
            // =指定回傳 機關名稱
            writer.WritePropertyName("org_name");
            writer.WriteValue("");
            // =指定回傳 所屬機關代碼
            writer.WritePropertyName("l1_dept_code");
            writer.WriteValue("");
            // =指定回傳 所屬機關名稱
            writer.WritePropertyName("l1_dept_name");
            writer.WriteValue("");
            // =指定回傳 直屬機關代碼
            writer.WritePropertyName("parent_dept_code");
            writer.WriteValue("");
            // =指定回傳 直屬機關名稱
            writer.WritePropertyName("parent_dept_name");
            writer.WriteValue("");
            // =======
            writer.WriteEndObject();
            writer.WritePropertyName("APP_USER_HR_PROFILE");
            writer.WriteStartObject();
            writer.WritePropertyName("APP_USER_PASSPORT_NO");
            writer.WriteValue("");
            // =指定回傳 用戶身分證資訊，不一定指完整
            writer.WritePropertyName("APP_USER_TW_SSN");
            writer.WriteValue("");
            // =指定回傳 用戶性別
            writer.WritePropertyName("APP_USER_GENDER");
            writer.WriteValue("");
            // =指定回傳 用戶生日
            writer.WritePropertyName("APP_USER_BIRTHDATE");
            writer.WriteValue("");
            writer.WritePropertyName("APP_USER_NODE_LAST_UPDATE_TIME");
            writer.WriteValue("");
            writer.WritePropertyName("APP_USER_NODE_LAST_UPDATE_TAG");
            writer.WriteValue("");
            writer.WriteEndObject();
            // =使用者工作資料表
            writer.WritePropertyName("APP_USER_EMPLOY_PROFILE");
            writer.WriteStartObject();
            // ==指定回傳職稱
            writer.WritePropertyName("APP_USER_EMPLOY_TITLE");
            writer.WriteValue("");
            writer.WriteEndObject();
            writer.WriteEndObject();
            return sw.ToString();
        }
    }
}
