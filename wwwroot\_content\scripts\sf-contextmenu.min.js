/*!*  filename: sf-contextmenu.min.js 
*     version : 26.1.41 
*     Copyright Syncfusion Inc. 2001 - 2024. All rights reserved. 
*     Use of this code is subject to the terms of our license.
*     A copy of the current license can be obtained at any time by e-mailing 
*     <EMAIL>. Any infringement will be prosecuted under 
*     applicable laws. 
*/
(window.webpackJsonp=window.webpackJsonp||[]).push([[17],{"./bundles/sf-contextmenu.js":function(e,t,n){"use strict";n.r(t);n("./modules/sf-contextmenu.js")},"./modules/sf-contextmenu.js":function(e,t){window.sfBlazor=window.sfBlazor||{},window.sfBlazor.ContextMenu=function(){"use strict";var e="e-menu-item";function t(e,n,s,i){void 0===i&&(i=0);var l=e.children[n];return i===e.childElementCount?-1:((l.classList.contains("e-separator")||l.classList.contains("e-disabled")||l.classList.contains("e-menu-hide"))&&(n=s?n===e.childElementCount-1?0:n+1:0===n?e.childElementCount-1:n-1,i++),((l=e.children[n]).classList.contains("e-separator")||l.classList.contains("e-disabled")||l.classList.contains("e-menu-hide"))&&(n=t(e,n,s)),n)}var n="e-menu-parent",s="CloseMenuAsync",i="mousedown touchstart",l=function(){function l(e,t,n,s,i,l,o,a){this.dataId=e,this.element=t,this.target=n,this.filter=s,this.showOn=i,this.closeOn=l,this.dotnetRef=a,this.enableScrolling=o,window.sfBlazor.setCompInstance(this),this.addContextMenuEvent(),this.addEventListener()}return l.prototype.addContextMenuEvent=function(e){var t;if(void 0===e&&(e=!0),this.target){var n=sf.base.selectAll(this.target);if(n.length){for(var s=0,i=n.length;s<i;s++)if(t=n[s],e)sf.base.Browser.isIos?new sf.base.Touch(t,{tapHold:this.touchHandler.bind(this)}):sf.base.EventHandler.add(t,this.showOn,this.cmenuHandler,this);else if(sf.base.Browser.isIos){var l=sf.base.getInstance(t,sf.base.Touch);l&&l.destroy()}else sf.base.EventHandler.remove(t,this.showOn,this.cmenuHandler);if(sf.base.isNullOrUndefined(this.targetElement)&&(this.targetElement=t),e){sf.base.EventHandler.add(this.targetElement,"scroll",this.scrollHandler,this);for(var o=0,a=sf.popups.getScrollableParent(this.targetElement);o<a.length;o++){var r=a[o];sf.base.EventHandler.add(r,"scroll",this.scrollHandler,this)}}else{var c=void 0;this.targetElement.parentElement?(sf.base.EventHandler.remove(this.targetElement,"scroll",this.scrollHandler),c=sf.popups.getScrollableParent(this.targetElement)):c=sf.popups.getScrollableParent(t);for(var d=0,h=c;d<h.length;d++){var u=h[d];sf.base.EventHandler.remove(u,"scroll",this.scrollHandler)}this.targetElement=null}}}},l.prototype.scrollHandler=function(){if(sf.base.select("."+n,this.element)&&(this.dotnetRef.invokeMethodAsync(s,0,!1,!0,!1),this.enableScrolling)){var e=this;setTimeout((function(){e.destroyScroll()}),100)}},l.prototype.vscrollHandler=function(){var e=this.element.querySelector(".e-menu-parent");null!=e&&e.classList.contains("e-transparent")&&e.classList.remove("e-transparent")},l.prototype.touchHandler=function(e){this.cmenuHandler(e.originalEvent)},l.prototype.keyDownHandler=function(s){var i=s.target.classList;(i.contains("e-menu-item")||i.contains(n))&&s.preventDefault(),function(n,s,i,l){if(9===i&&l&&(i=39),40===i||38===i){var o=void 0,a=void 0,r=void 0;s.classList.contains("e-menu-parent")?((r=(a=s).querySelector("."+e+".e-focused"))?(o=Array.prototype.indexOf.call(a.children,r),o=40===i?o===a.childElementCount-1?0:o+1:0===o?a.childElementCount-1:o-1):o=0,o=t(a,o,40===i)):s.classList.contains(e)&&(r=(a=s.parentElement).querySelector("."+e+".e-focused"),o=Array.prototype.indexOf.call(a.children,r||s),o=t(a,o=40===i?o===a.childElementCount-1?0:o+1:0===o?a.childElementCount-1:o-1,40===i)),a&&-1!==o&&a.children[o].focus()}else if(((n.classList.contains("e-rtl")?39===i:37===i)||27===i||39===i&&!s.classList.contains("e-menu-caret-icon")&&l||13===i&&sf.base.closest(s,".e-menu-container"))&&(s.classList.contains("e-ul")||s.classList.contains(e)&&!s.parentElement.classList.contains("e-contextmenu"))){var c=void 0;l&&(c=sf.base.select(l));a=s.classList.contains("e-ul")?s:s.parentElement;var d=sf.base.closest(a,".e-menu-vscroll"),h=void 0,u=d?d.previousElementSibling:a.previousElementSibling;if(!c||u&&13!==i){var m=sf.base.closest(a,".e-hamburger");h=m?sf.base.select("."+e+".e-selected",m):sf.base.select("."+e+".e-selected",u)}else h=sf.base.select("."+e+".e-selected",c),(d=sf.base.select(".e-menu-vscroll",n))&&c.blazor__instance.destroyScroll("none");h&&h.focus()}}(this.element,s.target,s.keyCode,this.menuId)},l.prototype.cmenuHandler=function(e){if(this.cmTarget=e.target,this.filter){for(var t=!1,n=this.filter.split(" "),s=0,l=n.length;s<l;s++)if(sf.base.closest(e.target,n[s])){t=!0;break}if(!t)return}e.preventDefault(),e.stopPropagation();var o=e.changedTouches?e.changedTouches[0].clientX:e.clientX,a=e.changedTouches?e.changedTouches[0].clientY:e.clientY;if("mouseover"===this.showOn){var r=this.cmTarget.getBoundingClientRect();r.right>o&&o>r.right-5&&(o-=10),r.bottom>a&&a>r.bottom-5&&(a-=10)}this.closeOn!==i&&(sf.base.EventHandler.remove(e.target,this.showOn,this.cmenuHandler),"mouseleave"===this.closeOn?sf.base.EventHandler.add(e.target,this.closeOn,this.mouseLeaveHandler,this):sf.base.EventHandler.add(document,this.closeOn,this.delegateMouseDownHandler,this)),this.dotnetRef.invokeMethodAsync("OpenContextMenuAsync",Math.ceil(o),Math.ceil(a),this.cmTarget.id)},l.prototype.mouseLeaveHandler=function(e,t){var n=this.cmTarget.getBoundingClientRect(),i=n.top<e.clientY&&n.bottom-3>e.clientY;if(!(n.left<e.clientX&&n.right>e.clientX)||!i||t){if(this.dotnetRef.invokeMethodAsync(s,0,!1,!0,!1),this.enableScrolling){var l=this;setTimeout((function(){l.destroyScroll()}),100)}sf.base.EventHandler.remove(this.cmTarget,this.closeOn,this.mouseLeaveHandler),sf.base.EventHandler.add(this.cmTarget,this.showOn,this.cmenuHandler,this)}},l.prototype.clickHandler=function(e){if(e&&(sf.base.EventHandler.add(this.cmTarget,this.showOn,this.cmenuHandler,this),"mouseleave"===this.closeOn?sf.base.EventHandler.remove(this.cmTarget,this.closeOn,this.mouseLeaveHandler):sf.base.EventHandler.remove(document,this.closeOn,this.delegateMouseDownHandler)),this.enableScrolling){var t=this;setTimeout((function(){t.destroyScroll(null,!0)}),100)}},l.prototype.contextMenuPosition=function(e,t,n,s,i,l){var o=this.hideMenu(!0);if(o){this.subMenuOpen=!1,this.setBlankIconStyle(o,n);var a=o.getBoundingClientRect(),r=this.getMenuWidth(o,a.width,n);if(s&&sf.base.Browser.isDevice)return o.style.width=Math.ceil(r)+"px",void(o.style.visibility="");if(i){if(t+a.height>document.documentElement.clientHeight){var c=document.documentElement.clientHeight-a.height-20;c>document.documentElement.clientTop&&(t=c)}if(e+r>document.documentElement.clientWidth){var d=document.documentElement.clientWidth-r-20;d>document.documentElement.clientLeft&&(e=d)}}var h=(o=this.updateScroll(l,o)).classList.contains("e-menu-parent")?o:sf.base.select(".e-menu-parent",o);this.element.style.top=Math.ceil(t+1)+pageYOffset+"px",this.element.style.left=Math.ceil(e+1)+pageXOffset+"px",o.style.width=Math.ceil(r)+"px",this.element.style.zIndex=sf.popups.getZindexPartial(this.element).toString(),o.style.visibility="",h.style.visibility="",o.focus()}},l.prototype.setBlankIconStyle=function(e,t){var n=[].slice.call(e.getElementsByClassName("e-blankicon")),s=t?{padding:"paddingRight",cssSelector:"padding-right",margin:"marginLeft"}:{padding:"paddingLeft",cssSelector:"padding-left",margin:"marginRight"};if([].slice.call(e.querySelectorAll('.e-menu-item[style*="'+s.cssSelector+'"]:not(.e-blankicon)')).forEach((function(e){e.style[s.padding]=""})),n.length){var i=e.querySelector(".e-menu-item:not(.e-blankicon):not(.e-separator)"),l=i.querySelector(".e-menu-icon");if(l){var o=getComputedStyle(l),a=parseInt(o.fontSize,10);parseInt(o.width,10)&&parseInt(o.width,10)>a&&(a=parseInt(o.width,10));var r=a+parseInt(o[s.margin],10)+parseInt(getComputedStyle(i)[s.padding],10)+"px";n.forEach((function(e){e.style[s.padding]=r}))}}},l.prototype.getMenuWidth=function(e,t,n){var s=e.getElementsByClassName("e-caret")[0];return s&&(t+=parseInt(getComputedStyle(s)[n?"marginRight":"marginLeft"],10)),t<120?120:t},l.prototype.addEventListener=function(){this.delegateMouseDownHandler=this.mouseDownHandler.bind(this),this.delegateMouseOverHandler=this.mouseOverHandler.bind(this),this.closeOn===i&&sf.base.EventHandler.add(document,i,this.delegateMouseDownHandler,this),sf.base.EventHandler.add(document,"mouseover",this.delegateMouseOverHandler,this),sf.base.EventHandler.add(this.element,"keydown",this.keyDownHandler,this)},l.prototype.removeEventListener=function(){sf.base.EventHandler.remove(document,i,this.delegateMouseDownHandler),sf.base.EventHandler.remove(document,"mouseover",this.delegateMouseOverHandler),sf.base.EventHandler.remove(this.element,"keydown",this.keyDownHandler)},l.prototype.mouseDownHandler=function(e){var t=e.target;if("DIV"!==t.tagName&&"SPAN"!==t.tagName||!(t.className.indexOf("scroll")>-1||t.className.indexOf("arrow")>-1))if(document.getElementById(this.element.id)){if(!(/^\d+$/.test(this.element.id)||!this.element.id.includes("sfcontextmenu")?this.getClosest(e.target,this.element.id):sf.base.closest(e.target,"#"+this.element.id))&&(sf.base.isNullOrUndefined(this.menuId)||!sf.base.closest(e.target,this.menuId))&&sf.base.select("."+n,this.element)){if(sf.base.closest(sf.base.select("."+n,this.element),".e-dropdown-popup")||this.dotnetRef.invokeMethodAsync(s,0,!1,!0,!1),this.enableScrolling){var l=this;setTimeout((function(){l.destroyScroll()}),100)}this.closeOn!==i&&(sf.base.EventHandler.remove(document,this.closeOn,this.delegateMouseDownHandler),sf.base.EventHandler.add(this.cmTarget,this.showOn,this.cmenuHandler,this))}}else this.removeEventListener()},l.prototype.getClosest=function(e,t){for(var n=null,s=e;s;){if(s.id===t){n=s;break}s=s.parentElement}return n},l.prototype.isHavingChild=function(e){return e&&"LI"===e.tagName&&e.className.indexOf("e-menu-item")<0&&(e=e.closest(".e-menu-item")),!!(e&&"LI"===e.tagName&&e.className.indexOf("e-menu-item")>-1&&e.className.indexOf("e-menu-caret-icon")>-1)},l.prototype.mouseOverHandler=function(e){var t=e.target;if("DIV"!==t.tagName&&"SPAN"!==t.tagName||!(t.className.indexOf("scroll")>-1||t.className.indexOf("arrow")>-1))if(document.getElementById(this.element.id)){var i=[].slice.call(sf.base.selectAll("."+n,this.element));if(i.length){var l=sf.base.closest(t,".e-scroll-nav");if(this.enableScrolling&&!this.isHavingChild(t)){var o=this;setTimeout((function(){for(var e=[].slice.call(sf.base.selectAll(".e-menu-vscroll",this.element)),n=0;n<e.length;n++)t&&"LI"===t.tagName&&t.className.indexOf("e-menu-item")<0&&(t=t.closest(".e-menu-item")),t&&t.parentElement!=e[n].querySelectorAll(".e-menu-parent")[0]&&n==e.length-1&&o.destroyScroll(e[n])}),100)}if(this.subMenuOpen&&(i.length>1||!sf.base.isNullOrUndefined(this.menuId)&&!l)){if(!(/^\d+$/.test(this.element.id)||!this.element.id.includes("sfcontextmenu")?this.getClosest(t,this.element.id):sf.base.closest(t,"#"+this.element.id))&&(sf.base.isNullOrUndefined(this.menuId)||!sf.base.closest(t,this.menuId))||l){var a=1;if(!sf.base.isNullOrUndefined(this.menuId)&&(a=0,l&&(a=i.indexOf(sf.base.select("."+n,l.parentElement))+1)===i.length))return;if(this.dotnetRef.invokeMethodAsync(s,a,!1,!0,!1),this.enableScrolling){var r=this;setTimeout((function(){for(var e=a;e<i.length;e++)r.destroyScroll(i[e])}),100)}"mouseleave"===this.closeOn&&(this.mouseLeaveHandler(e),sf.base.EventHandler.remove(this.menuTarget,this.closeOn,this.menuMouseLeave)),sf.base.isNullOrUndefined(this.menuId)||sf.base.closest(t,".e-scroll-nav")||this.destroyMenuScroll(null)}var c=void 0;c=/^\d+$/.test(this.element.id)||!this.element.id.includes("sfcontextmenu")?this.getClosest(t,this.element.id):sf.base.closest(t,"#"+this.element.id),sf.base.isNullOrUndefined(this.menuId)||!c&&!sf.base.closest(t,this.menuId)||!sf.base.closest(t,".e-menu-item")||sf.base.closest(t,".e-selected")||this.destroyMenuScroll(sf.base.closest(t,"."+n))}else"mouseleave"===this.closeOn&&t.classList.contains("e-menu-item")&&(this.menuTarget=t.parentElement,sf.base.EventHandler.add(this.menuTarget,this.closeOn,this.menuMouseLeave,this));if(!this.openAsMenu){var d=document.activeElement;c=void 0;if(!(c=/^\d+$/.test(this.element.id)||!this.element.id.includes("sfcontextmenu")?this.getClosest(d,this.element.id):sf.base.closest(d,"#"+this.element.id))&&i.length&&"BODY"==d.tagName){var h=this.getLastMenu();h&&h.focus()}}}}else this.removeEventListener()},l.prototype.menuMouseLeave=function(e){var t=this.menuTarget.getBoundingClientRect(),n=t.top<e.clientY&&t.bottom>e.clientY;t.left<e.clientX&&t.right>e.clientX&&n||(this.mouseLeaveHandler(e,!0),sf.base.EventHandler.remove(this.menuTarget,this.closeOn,this.menuMouseLeave))},l.prototype.destroyMenuScroll=function(e){if(sf.base.select(".e-menu-vscroll",this.element)){var t=window.sfBlazor.getCompInstance(this.dataId);sf.base.isNullOrUndefined(t)||t.destroyScroll("none",e)}},l.prototype.hideMenu=function(e){var t;if(e){if(!(t=sf.base.select("."+n,this.element))||sf.base.isNullOrUndefined(this.element.parentElement))return null;this.element.parentElement===document.body||this.element.parentElement.classList.contains("e-dropDown-button")||document.body.appendChild(this.element)}else{var s=sf.base.selectAll("."+n,this.element);if(s.length<2)return null;t=s[s.length-1]}return t.style.width="",t.style.visibility="hidden",t.classList.remove("e-transparent"),t},l.prototype.subMenuPosition=function(e,t,s,i,l,o,a){if(e){var r=sf.base.selectAll("."+n,this.element),c=r[r.length-2].querySelector(".e-menu-item.e-selected").getBoundingClientRect(),d=this.element.getBoundingClientRect(),h=e.classList.contains(n)?e:sf.base.select("."+n,e);this.setBlankIconStyle(h,t);var u,m,f=h.getBoundingClientRect(),v=this.getMenuWidth(h,f.width,t);t?(m=parseInt(getComputedStyle(h).borderWidth,10),u=c.left-v-d.left):u="mouseleave"==this.closeOn?c.right-d.left-10:c.right-d.left;var p=c.top-d.top;if(t)c.left-m-v<document.documentElement.clientLeft&&c.right+v<document.documentElement.clientWidth&&(u=c.right-d.left);else if(c.right+v>document.documentElement.clientWidth){var g=c.left-v;g>document.documentElement.clientLeft&&(u=g-d.left,"mouseleave"==this.closeOn&&(u=g-d.left+10))}var b=l||f.height;if(c.top+b>document.documentElement.clientHeight){var y=document.documentElement.clientHeight-b-20;y>document.documentElement.clientTop&&(p=y-d.top)}o&&(e=this.updateScroll(l,e)).className.indexOf("scroll")>-1&&(h.style.left="0px",h.style.top="0px"),this.subMenuOpen=!s,e.style.left=Math.ceil(u)+"px",e.style.top=Math.ceil(p)+"px",e.style.width=Math.ceil(v)+"px",h.style.visibility="";var E=h.querySelector(".e-menu-item.e-focused");E?E.focus():a&&h.focus(),i&&(this.openAsMenu=!0)}},l.prototype.updateScroll=function(e,t){if(this.enableScrolling){this.destroyScroll(t),e>0&&t&&(t=sf.navigations.addScrolling(sf.base.createElement,this.element,t,"vscroll",!1,e));var n=this.element.querySelector(".e-vscroll-bar");n&&sf.base.EventHandler.add(n,"scroll",this.vscrollHandler,this)}return t},l.prototype.destroyScroll=function(e,t){sf.base.selectAll(".e-menu-vscroll",this.element).forEach((function(n){(t&&!n.querySelector("ul")||!t)&&sf.navigations.destroyScroll(sf.base.getInstance(n,sf.navigations.VScroll),n,e)}))},l.prototype.getLastMenu=function(){var e=sf.base.selectAll("."+n,this.element);return e.length?e[e.length-1]:null},l.prototype.onPropertyChanged=function(e,t){switch(e){case"Target":this.addContextMenuEvent(!1),this.target=t,this.addContextMenuEvent();break;case"Filter":this.filter=t;break;case"ShowOn":this.addContextMenuEvent(!1),this.showOn=t,this.addContextMenuEvent()}},l.prototype.destroy=function(e){this.removeEventListener(),this.addContextMenuEvent(!1),e&&e.parentElement&&e.previousElementSibling!==this.element&&e.parentElement.insertBefore(this.element,e),e&&e.parentElement||!this.element.parentElement||this.element.parentElement!==document.body||document.body.removeChild(this.element)},l.prototype.updateProperty=function(e,t){t&&(this.menuId="#"+t.id),this.subMenuOpen=!e},l}();return{initialize:function(e,t,n,s,i,o,a,r){sf.base.isNullOrUndefined(t)||new l(e,t,n,s,i,o,a,r)},contextMenuPosition:function(e,t,n,s,i,l,o){var a=window.sfBlazor.getCompInstance(e);sf.base.isNullOrUndefined(a)||a.contextMenuPosition(t,n,s,i,l,o)},subMenuPosition:function(e,t,n,s,i){var l=window.sfBlazor.getCompInstance(e);if(!sf.base.isNullOrUndefined(l)){var o=l.hideMenu();l.subMenuPosition(o,t,n,s,i,!0)}},onPropertyChanged:function(e,t,n){var s=window.sfBlazor.getCompInstance(e);sf.base.isNullOrUndefined(s)||s.onPropertyChanged(t,n)},click:function(e,t){var n=window.sfBlazor.getCompInstance(e);sf.base.isNullOrUndefined(n)||n.clickHandler(t)},destroy:function(e,t){var n=window.sfBlazor.getCompInstance(e);sf.base.isNullOrUndefined(n)||n.destroy(t)}}}()}}]);(async()=>{await import(`${document.baseURI}_content/Syncfusion.Blazor/scripts/syncfusion-blazor-base.min.js?v=26.1.undefined`).then(()=>{sfBlazor.loadDependencies('sfcontextmenu');})})();