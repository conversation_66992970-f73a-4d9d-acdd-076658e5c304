﻿using Intra2025.Services;
using Microsoft.AspNetCore.Components;
using Microsoft.JSInterop;

namespace Intra2025.Components.Base
{
    public class BasePageComponent : ComponentBase
    {
        [Inject] protected SsoService sso { get; set; } = null!;
        [Inject] protected UserState _userState { get; set; } = null!; // 注入 UserState
        [Inject] protected IWebHostEnvironment env { get; set; } = null!;
        [Inject] protected NavigationManager NavigationManager { get; set; } = null!;
        [Inject] protected ILogger<BasePageComponent> Logger { get; set; } = null!; // 注入 ILogger
        [Inject] protected IJSRuntime JS { get; set; } = null!;
        [Inject] protected ClientIpService ClientIpService { get; set; } = null!;
        [Inject] protected IHttpContextAccessor HttpContextAccessor { get; set; } = null!;
        
        protected override async Task OnInitializedAsync()
        {
            await base.OnInitializedAsync();

           // 自動執行 SsoService
            try
            {
                Logger.LogDebug(DateTime.Now.ToString() + " ##### 開始驗證和初始化 SSO 用戶 #####" +DateTime.Now.ToString() );
                await sso.ValidateAndInitializeUserAsync();
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "SSO 驗證失敗: {Message}", ex.Message);
            }
        }

        // 靜態方法直接使用全局的 IHttpContextAccessor
        public string GetClientIp(HttpContext? httpContext)
        {
            // 檢查 HttpContext 是否為 null
            if (httpContext == null)
            {
                //Logger.LogInformation("HttpContext 不存在，無法獲取 IP");
                // return "HttpContext 不存在";
            }

            // 嘗試從標頭中獲取 IP
            var xForwardedFor = httpContext?.Request.Headers["X-Forwarded-For"].FirstOrDefault();
            var xRealIp = httpContext?.Request.Headers["X-Real-IP"].FirstOrDefault();

            // 嘗試從 Connection 中獲取遠端 IP
            var remoteIp = httpContext?.Connection?.RemoteIpAddress?.ToString();

            // 返回獲取到的 IP 或默認值
            return xForwardedFor ?? xRealIp ?? remoteIp ?? "未知IP";
        }
    }
   
}
