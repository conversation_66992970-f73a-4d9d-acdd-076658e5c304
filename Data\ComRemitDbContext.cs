using Microsoft.EntityFrameworkCore;
using Intra2025.Models.ComRemit;

namespace Intra2025.Data
{
    public class ComRemitDbContext : DbContext
    {
        public ComRemitDbContext(DbContextOptions<ComRemitDbContext> options) : base(options)
        {
        }

        public DbSet<RemitedList> RemitedList { get; set; }
        public DbSet<Payee> Payee { get; set; }
        public DbSet<FinancialUnit> FinancialUnit { get; set; }
        public DbSet<DiaryEvent> DiaryEvent { get; set; }
        public DbSet<Financial> Financial { get; set; }
        public DbSet<FinancialInstitution> FinancialInstitution { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // 設定 RemitedList 資料表
            modelBuilder.Entity<RemitedList>()
                .HasKey(r => r.Sno);

            modelBuilder.Entity<RemitedList>()
                .Property(r => r.Sno)
                .UseIdentityColumn();

            // 設定 Payee 資料表 - 複合主鍵
            modelBuilder.Entity<Payee>()
                .Has<PERSON>ey(p => new { p.CollectNo, p.CollecAcc });

            // 設定 sn 欄位為自動遞增
            modelBuilder.Entity<Payee>()
                .Property(p => p.Sn)
                .UseIdentityColumn();

            // 設定 FinancialUnit 資料表
            modelBuilder.Entity<FinancialUnit>()
                .HasKey(f => f.Sno);

            // 設定 DiaryEvent 資料表
            modelBuilder.Entity<DiaryEvent>()
                .HasKey(d => d.Id);

            // 恢復 Financial 配置
            modelBuilder.Entity<Financial>()
                .HasKey(f => f.Id);

            // 設定 FinancialInstitution 資料表
            modelBuilder.Entity<FinancialInstitution>()
                .HasKey(f => f.Id);
        }
    }
}
