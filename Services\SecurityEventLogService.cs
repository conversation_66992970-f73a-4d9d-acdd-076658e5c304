using Intra2025.Models.Security;
using Intra2025.Components.Base;
using Microsoft.Extensions.Options;
using System.Collections.Concurrent;

namespace Intra2025.Services
{
    /// <summary>
    /// 安全事件日誌配置
    /// </summary>
    public class SecurityLogOptions
    {
        /// <summary>
        /// 是否啟用安全日誌記錄
        /// </summary>
        public bool Enabled { get; set; } = true;

        /// <summary>
        /// 是否記錄詳細資訊
        /// </summary>
        public bool LogDetails { get; set; } = true;

        /// <summary>
        /// 是否記錄使用者代理字串
        /// </summary>
        public bool LogUserAgent { get; set; } = true;

        /// <summary>
        /// 最小記錄等級
        /// </summary>
        public SecurityEventSeverity MinimumLogLevel { get; set; } = SecurityEventSeverity.Information;

        /// <summary>
        /// 是否啟用即時監控
        /// </summary>
        public bool EnableRealTimeMonitoring { get; set; } = true;

        /// <summary>
        /// 登入失敗警報閾值
        /// </summary>
        public int LoginFailureThreshold { get; set; } = 5;

        /// <summary>
        /// 登入失敗監控時間窗口（分鐘）
        /// </summary>
        public int LoginFailureWindowMinutes { get; set; } = 5;
    }

    /// <summary>
    /// 安全事件日誌服務
    /// </summary>
    public class SecurityEventLogService
    {
        private readonly ILogger<SecurityEventLogService> _logger;
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly UserState _userState;
        private readonly SecurityLogOptions _options;
        private readonly ConcurrentDictionary<string, List<DateTime>> _loginFailureTracker;

        public SecurityEventLogService(
            ILogger<SecurityEventLogService> logger,
            IHttpContextAccessor httpContextAccessor,
            UserState userState,
            IOptions<SecurityLogOptions> options)
        {
            _logger = logger;
            _httpContextAccessor = httpContextAccessor;
            _userState = userState;
            _options = options.Value;
            _loginFailureTracker = new ConcurrentDictionary<string, List<DateTime>>();
        }

        /// <summary>
        /// 記錄安全事件
        /// </summary>
        public async Task LogSecurityEventAsync(SecurityEvent securityEvent)
        {
            if (!_options.Enabled || securityEvent.Severity < _options.MinimumLogLevel)
                return;

            try
            {
                // 填充 HTTP 上下文資訊
                await EnrichEventWithContextAsync(securityEvent);

                // 記錄到日誌
                LogEventToLogger(securityEvent);

                // 即時監控檢查
                if (_options.EnableRealTimeMonitoring)
                {
                    await PerformRealTimeMonitoringAsync(securityEvent);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "記錄安全事件時發生錯誤");
            }
        }

        /// <summary>
        /// 記錄登入成功事件
        /// </summary>
        public async Task LogLoginSuccessAsync(string userId, string userName)
        {
            var ipAddress = GetClientIpAddress();
            var sessionId = GetSessionId();

            var securityEvent = SecurityEvent.CreateLoginSuccess(userId, userName, ipAddress, sessionId);
            await LogSecurityEventAsync(securityEvent);

            // 清除該 IP 的登入失敗記錄
            if (!string.IsNullOrEmpty(ipAddress))
            {
                _loginFailureTracker.TryRemove(ipAddress, out _);
            }
        }

        /// <summary>
        /// 記錄登入失敗事件
        /// </summary>
        public async Task LogLoginFailureAsync(string? userId, string reason)
        {
            var ipAddress = GetClientIpAddress();
            var attemptCount = TrackLoginFailure(ipAddress);

            var securityEvent = SecurityEvent.CreateLoginFailure(userId, ipAddress, reason, attemptCount);
            await LogSecurityEventAsync(securityEvent);
        }

        /// <summary>
        /// 記錄存取被拒絕事件
        /// </summary>
        public async Task LogAccessDeniedAsync(string resource, string reason)
        {
            var ipAddress = GetClientIpAddress();
            var securityEvent = SecurityEvent.CreateAccessDenied(
                _userState.Account, 
                _userState.UserName, 
                ipAddress, 
                resource, 
                reason);
            
            await LogSecurityEventAsync(securityEvent);
        }

        /// <summary>
        /// 記錄敏感資料存取事件
        /// </summary>
        public async Task LogSensitiveDataAccessAsync(string dataType, string operation)
        {
            var ipAddress = GetClientIpAddress();
            var securityEvent = SecurityEvent.CreateSensitiveDataAccess(
                _userState.Account ?? "Anonymous",
                _userState.UserName ?? "Unknown",
                ipAddress,
                dataType,
                operation);

            await LogSecurityEventAsync(securityEvent);
        }

        /// <summary>
        /// 記錄可疑活動事件
        /// </summary>
        public async Task LogSuspiciousActivityAsync(string activityType, string description)
        {
            var ipAddress = GetClientIpAddress();
            var securityEvent = SecurityEvent.CreateSuspiciousActivity(
                _userState.Account,
                ipAddress,
                activityType,
                description);

            await LogSecurityEventAsync(securityEvent);
        }

        /// <summary>
        /// 記錄自訂安全事件
        /// </summary>
        public async Task LogCustomEventAsync(SecurityEventType eventType, SecurityEventSeverity severity, string source, Dictionary<string, object>? details = null)
        {
            var securityEvent = new SecurityEvent
            {
                EventType = eventType,
                Severity = severity,
                UserId = _userState.Account,
                UserName = _userState.UserName,
                IpAddress = GetClientIpAddress(),
                Source = source,
                Details = details
            };

            await LogSecurityEventAsync(securityEvent);
        }

        /// <summary>
        /// 使用上下文資訊豐富事件
        /// </summary>
        private async Task EnrichEventWithContextAsync(SecurityEvent securityEvent)
        {
            var httpContext = _httpContextAccessor.HttpContext;
            if (httpContext != null)
            {
                // 填充 IP 位址（如果尚未設定）
                if (string.IsNullOrEmpty(securityEvent.IpAddress))
                {
                    securityEvent.IpAddress = GetClientIpAddress();
                }

                // 填充使用者代理字串
                if (_options.LogUserAgent && string.IsNullOrEmpty(securityEvent.UserAgent))
                {
                    securityEvent.UserAgent = httpContext.Request.Headers["User-Agent"].FirstOrDefault();
                }

                // 填充會話 ID
                if (string.IsNullOrEmpty(securityEvent.SessionId))
                {
                    securityEvent.SessionId = GetSessionId();
                }

                // 填充請求資訊
                securityEvent.RequestPath = httpContext.Request.Path;
                securityEvent.HttpMethod = httpContext.Request.Method;

                // 生成關聯 ID
                if (string.IsNullOrEmpty(securityEvent.CorrelationId))
                {
                    securityEvent.CorrelationId = httpContext.TraceIdentifier;
                }
            }

            // 填充使用者資訊（如果尚未設定）
            if (string.IsNullOrEmpty(securityEvent.UserId) && !string.IsNullOrEmpty(_userState.Account))
            {
                securityEvent.UserId = _userState.Account;
                securityEvent.UserName = _userState.UserName;
            }

            await Task.CompletedTask;
        }

        /// <summary>
        /// 將事件記錄到日誌系統
        /// </summary>
        private void LogEventToLogger(SecurityEvent securityEvent)
        {
            var logMessage = "Security Event: {EventType} | User: {UserId} | IP: {IpAddress} | Severity: {Severity}";
            var logArgs = new object[]
            {
                securityEvent.EventType,
                securityEvent.UserId ?? "Anonymous",
                securityEvent.IpAddress ?? "Unknown",
                securityEvent.Severity
            };

            // 添加詳細資訊到日誌
            if (_options.LogDetails && securityEvent.Details != null && securityEvent.Details.Any())
            {
                logMessage += " | Details: {Details}";
                logArgs = logArgs.Append(securityEvent.ToJson()).ToArray();
            }

            // 根據嚴重性選擇日誌等級
            switch (securityEvent.Severity)
            {
                case SecurityEventSeverity.Information:
                    _logger.LogInformation(logMessage, logArgs);
                    break;
                case SecurityEventSeverity.Warning:
                    _logger.LogWarning(logMessage, logArgs);
                    break;
                case SecurityEventSeverity.Error:
                    _logger.LogError(logMessage, logArgs);
                    break;
                case SecurityEventSeverity.Critical:
                    _logger.LogCritical(logMessage, logArgs);
                    break;
            }
        }

        /// <summary>
        /// 執行即時監控檢查
        /// </summary>
        private async Task PerformRealTimeMonitoringAsync(SecurityEvent securityEvent)
        {
            // 檢查多次登入失敗
            if (securityEvent.EventType == SecurityEventType.LOGIN_FAILURE)
            {
                await CheckMultipleLoginFailuresAsync(securityEvent);
            }

            // 檢查異常 IP 存取
            if (securityEvent.EventType == SecurityEventType.LOGIN_SUCCESS)
            {
                await CheckUnusualIpAccessAsync(securityEvent);
            }
        }

        /// <summary>
        /// 檢查多次登入失敗
        /// </summary>
        private async Task CheckMultipleLoginFailuresAsync(SecurityEvent securityEvent)
        {
            if (string.IsNullOrEmpty(securityEvent.IpAddress)) return;

            var failureCount = GetLoginFailureCount(securityEvent.IpAddress);
            if (failureCount >= _options.LoginFailureThreshold)
            {
                await LogSuspiciousActivityAsync(
                    "MULTIPLE_LOGIN_FAILURES",
                    $"IP {securityEvent.IpAddress} 在 {_options.LoginFailureWindowMinutes} 分鐘內登入失敗 {failureCount} 次");
            }
        }

        /// <summary>
        /// 檢查異常 IP 存取
        /// </summary>
        private async Task CheckUnusualIpAccessAsync(SecurityEvent securityEvent)
        {
            // 這裡可以實作 IP 白名單檢查或地理位置檢查
            // 目前只是記錄資訊
            if (!string.IsNullOrEmpty(securityEvent.IpAddress) && !IsKnownIpAddress(securityEvent.IpAddress))
            {
                _logger.LogInformation("使用者 {UserId} 從新的 IP 位址 {IpAddress} 登入", 
                    securityEvent.UserId, securityEvent.IpAddress);
            }

            await Task.CompletedTask;
        }

        /// <summary>
        /// 追蹤登入失敗次數
        /// </summary>
        private int TrackLoginFailure(string ipAddress)
        {
            if (string.IsNullOrEmpty(ipAddress)) return 1;

            var now = DateTime.UtcNow;
            var windowStart = now.AddMinutes(-_options.LoginFailureWindowMinutes);

            _loginFailureTracker.AddOrUpdate(ipAddress,
                new List<DateTime> { now },
                (key, existing) =>
                {
                    // 移除過期的記錄
                    existing.RemoveAll(time => time < windowStart);
                    existing.Add(now);
                    return existing;
                });

            return _loginFailureTracker[ipAddress].Count;
        }

        /// <summary>
        /// 獲取登入失敗次數
        /// </summary>
        private int GetLoginFailureCount(string ipAddress)
        {
            if (string.IsNullOrEmpty(ipAddress) || !_loginFailureTracker.ContainsKey(ipAddress))
                return 0;

            var now = DateTime.UtcNow;
            var windowStart = now.AddMinutes(-_options.LoginFailureWindowMinutes);
            var failures = _loginFailureTracker[ipAddress];

            return failures.Count(time => time >= windowStart);
        }

        /// <summary>
        /// 檢查是否為已知 IP 位址
        /// </summary>
        private bool IsKnownIpAddress(string ipAddress)
        {
            // 這裡可以實作 IP 白名單邏輯
            // 目前簡單檢查是否為內網 IP
            return ipAddress.StartsWith("192.168.") || 
                   ipAddress.StartsWith("10.") || 
                   ipAddress.StartsWith("172.") ||
                   ipAddress == "127.0.0.1" ||
                   ipAddress == "::1";
        }

        /// <summary>
        /// 獲取客戶端 IP 位址
        /// </summary>
        private string GetClientIpAddress()
        {
            var httpContext = _httpContextAccessor.HttpContext;
            if (httpContext == null) return "Unknown";

            // 檢查 X-Forwarded-For 標頭
            var forwardedFor = httpContext.Request.Headers["X-Forwarded-For"].FirstOrDefault();
            if (!string.IsNullOrEmpty(forwardedFor))
            {
                return forwardedFor.Split(',')[0].Trim();
            }

            // 檢查 X-Real-IP 標頭
            var realIp = httpContext.Request.Headers["X-Real-IP"].FirstOrDefault();
            if (!string.IsNullOrEmpty(realIp))
            {
                return realIp;
            }

            // 使用連線的遠端 IP
            return httpContext.Connection.RemoteIpAddress?.ToString() ?? "Unknown";
        }

        /// <summary>
        /// 獲取會話 ID
        /// </summary>
        private string? GetSessionId()
        {
            var httpContext = _httpContextAccessor.HttpContext;
            return httpContext?.Session?.Id;
        }
    }
}
