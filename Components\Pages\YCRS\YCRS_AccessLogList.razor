﻿@page "/YCRS_AccessLogList"
@layout YCRSLayout
@using Intra2025.Models.YouthCareReportService
@using Intra2025.Components.Base
@using Intra2025.Components.Layout
@using Intra2025.Data
@using Intra2025.Models
@using Microsoft.AspNetCore.Components.Forms
@using System.Globalization
@using Microsoft.EntityFrameworkCore
@using System.Text.Json
@using static Intra2025.Services.SsoService
@inherits BasePageComponent
@inject YCRSDbContext _context

<title>溫馨關懷表(操作紀錄)</title>

<style>
    table thead tr th {
        text-align: center;
        vertical-align: middle;
        background-color: #d5e1df !important;
    }
</style>

<!-- 守護寶貝即時通 Title -->
<div style="display: flex;justify-content: center;align-items: center;">
    <div class="loader3"></div>
</div>

<!-- 使用者資訊和導航現在由 YCRSLayout 統一處理 -->

<!-- 日期選擇與搜尋區塊 -->
<div class="d-flex flex-wrap align-items-center border p-3 rounded bg-light" style="width:1000px; margin-left: 2rem; margin-bottom:0.5rem">
    <!-- 日期選擇 -->
    <div class="me-3" style="width:150px">
        <label for="startDate" class="form-label mb-0 fw-bold">案件起始日期：</label>
        <InputDate TValue="DateTime?" @bind-Value="dt1" id="startDate" class="form-control" style="width: 140px;" />
    </div>

    <div class="me-3" style="width:150px">
        <label for="endDate" class="form-label mb-0 fw-bold">案件結束日期：</label>
        <InputDate TValue="DateTime?" @bind-Value="dt2" id="endDate" class="form-control" style="width: 140px;" />
    </div>

    <!-- 搜尋輸入框 -->
    <div class="flex-grow-1 me-3">
        <input type="text" @bind="searchTerm" placeholder="搜尋「來源IP、操作人員(帳號)」"
               class="form-control" style="width: 380px;font-size: 10px;font-size: 14px;" />
    </div>

    <!-- 搜尋按鈕 -->
    <div class="d-flex">
        <button class="btn btn-outline-primary me-2" @onclick="Search">
            搜尋
        </button>
        <button class="btn btn-outline-secondary" @onclick="Clear">
            清除搜尋
        </button>
    </div>
</div>

<table class="table table-striped">
    <thead style="line-height: 22px;background-color:darkgrey">
        <tr>
            <th style="width: 20px;">序號</th>
            <th style="width: 30px;">資料序號</th>
            <th style="width: 70px;">動作</th>
            <th style="width: 220px;">操作人員</th>
            <th style="width: 80px;">來源IP</th>
            <th style="width: 120px;">操作時間</th>
        </tr>
    </thead>
    <tbody>
        @if (FilteredRecords != null && FilteredRecords.Any())
        {
            int index = 1;
            @foreach (var record in pagedRecords)
            {
                <tr>
                    <td style="text-align: center">@index</td>
                    <td style="text-align: center">@record.Sn</td>
                    <td style="text-align: center">@record.Action</td>
                    <td style="text-align: center">@record.Account</td>
                    <td style="text-align: center">@record.IP</td>
                    <td style="text-align: center">@record.Timestamp.ToString("yyyy-MM-dd HH:mm:ss")</td>
                </tr>
                index++;
            }
        }
        else
        {
            <tr>
                <td colspan="6" style="text-align: center">無資料</td>
            </tr>
        }
    </tbody>
</table>

<div class="pagination">
    <div class="pagination-controls">
        <button @onclick="PreviousPage" disabled="@(currentPage == 1)">上一頁</button>
        <span>第 @currentPage 頁</span><span>【共 @totalPages 頁】(案件總數量: @totalRecordCount)</span>
        <button @onclick="NextPage" disabled="@(currentPage == totalPages)">下一頁</button>
    </div>
</div>

@code {
    private int currentPage = 1;
    private int pageSize = 10;
    private int totalRecordCount = 0;
    private int totalPages => (int)Math.Ceiling((double)FilteredRecords.Count / pageSize);
    private string searchTerm = "";
    private DateTime? dt1, dt2;
    private List<YCRS_AccessLog> pagedRecords = new();
    private List<YCRS_AccessLog> FilteredRecords = new();

    protected override async Task OnInitializedAsync()
    {
        // 初始化資料 (假設已加載至 FilteredRecords)
        FilteredRecords = await _context.YCRS_AccessLog
            .OrderByDescending(record => record.Timestamp)
            .ToListAsync();
        totalRecordCount = FilteredRecords.Count;
        UpdatePaged();
    }

    private void Search()
    {
        currentPage = 1;
        FilterRecords();
    }

    private void Clear()
    {
        NavigationManager.NavigateTo($"{Environment.GetEnvironmentVariable("ASPNETCORE_APPL_PATH")?.TrimEnd('/')}/YCRS_AccessLogList", true);
    }
    private void FilterRecords()
    {
        if (!_userState.IsAdmin)
        {
            // 若非 Admin，導向權限不足頁面或顯示錯誤訊息
            NavigationManager.NavigateTo("https://eip.e-land.gov.tw", true); // 導向權限不足頁面
            return;
        }

        // 若為 Admin，進行資料篩選
        FilteredRecords = FilteredRecords
            .Where(r => (string.IsNullOrWhiteSpace(searchTerm) ||
                         (r.IP?.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) ?? false) ||
                         (r.Account?.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) ?? false)) &&
                         (!dt1.HasValue || r.Timestamp >= dt1) &&
                         (!dt2.HasValue || r.Timestamp <= dt2?.AddDays(1)))
            .ToList();

        totalRecordCount = FilteredRecords.Count; // 計算資料總筆數
        currentPage = 1; // 每次搜尋時重置為第一頁
        UpdatePaged();
    }


    private void UpdatePaged()
    {
        pagedRecords = FilteredRecords
            .Skip((currentPage - 1) * pageSize)
            .Take(pageSize)
            .ToList();
    }

    private void PreviousPage()
    {
        if (currentPage > 1)
        {
            currentPage--;
            UpdatePaged();
        }
    }

    private void NextPage()
    {
        if (currentPage < totalPages)
        {
            currentPage++;
            UpdatePaged();
        }
    }

    public async Task ExportDataAsync()
    {
        // 匯出邏輯
        await Task.CompletedTask;
    }
}
