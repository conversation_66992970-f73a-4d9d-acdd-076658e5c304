using Intra2025.Data;
using Intra2025.Models.ComRemit;
using Intra2025.Components.Base;
using Microsoft.EntityFrameworkCore;

namespace Intra2025.Services
{
    public class ComRemitPayeeService
    {
        private readonly ComRemitDbContext _context;
        private readonly UserState _userState;

        public ComRemitPayeeService(ComRemitDbContext context, UserState userState)
        {
            _context = context;
            _userState = userState;
        }

        // 取得所有收款人資料（無權限控制 - 管理功能用）
        public async Task<List<Payee>> GetAllPayeesAsync()
        {
            try
            {
                return await _context.Payee
                    .OrderBy(p => p.CollectNo)
                    .ThenBy(p => p.CollecAcc)
                    .ToListAsync();
            }
            catch
            {
                return new List<Payee>();
            }
        }

        // 根據登入帳號取得收款人資料 (PayeeList 專用 - 原始邏輯)
        public async Task<List<Payee>> GetPayeesByUserAccountAsync(string userAccount)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(userAccount))
                {
                    return new List<Payee>();
                }

                // PayeeList 使用原始邏輯：只檢查 BelongAcc
                var result = await _context.Payee
                    .Where(p => p.BelongAcc == userAccount)
                    .OrderBy(p => p.CollectNo)
                    .ThenBy(p => p.CollecAcc)
                    .ToListAsync();

                return result;
            }
            catch (Exception)
            {
                // 記錄錯誤但不拋出異常，返回空列表
                return new List<Payee>();
            }
        }

        // 根據收款人戶名搜尋（無權限控制 - 管理功能用）
        public async Task<List<Payee>> SearchPayeesByNameAsync(string name)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(name))
                {
                    return await GetAllPayeesAsync();
                }

                return await _context.Payee
                    .Where(p => p.CollectName != null && p.CollectName.Contains(name))
                    .OrderBy(p => p.CollectNo)
                    .ThenBy(p => p.CollecAcc)
                    .ToListAsync();
            }
            catch
            {
                return new List<Payee>();
            }
        }

        // 根據登入帳號和收款人戶名搜尋 (PayeeList 專用 - 原始邏輯)
        public async Task<List<Payee>> SearchPayeesByNameAndUserAccountAsync(string name, string userAccount)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(userAccount))
                {
                    return new List<Payee>();
                }

                // PayeeList 使用原始邏輯：只檢查 BelongAcc
                var query = _context.Payee.Where(p => p.BelongAcc == userAccount);

                if (!string.IsNullOrWhiteSpace(name))
                {
                    query = query.Where(p => p.CollectName != null && p.CollectName.Contains(name));
                }

                return await query
                    .OrderBy(p => p.CollectNo)
                    .ThenBy(p => p.CollecAcc)
                    .ToListAsync();
            }
            catch
            {
                return new List<Payee>();
            }
        }

        // 根據登入帳號和多欄位搜尋 (PayeeList 專用 - 支援收款人戶名、金融局號、帳號)
        public async Task<List<Payee>> SearchPayeesByMultipleFieldsAndUserAccountAsync(string searchTerm, string userAccount)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(userAccount))
                {
                    return new List<Payee>();
                }

                // PayeeList 使用原始邏輯：只檢查 BelongAcc
                var query = _context.Payee.Where(p => p.BelongAcc == userAccount);

                if (!string.IsNullOrWhiteSpace(searchTerm))
                {
                    // 搜尋收款人戶名、金融局號或帳號
                    query = query.Where(p =>
                        (p.CollectName != null && p.CollectName.Contains(searchTerm)) ||
                        (p.CollectNo != null && p.CollectNo.Contains(searchTerm)) ||
                        (p.CollecAcc != null && p.CollecAcc.Contains(searchTerm))
                    );
                }

                return await query
                    .OrderBy(p => p.CollectNo)
                    .ThenBy(p => p.CollecAcc)
                    .ToListAsync();
            }
            catch
            {
                return new List<Payee>();
            }
        }

        // 取得單筆收款人資料
        public Task<Payee?> GetPayeeByIdAsync(int id)
        {
            // 由於現在使用複合主鍵，這個方法需要重新實作
            // 暫時返回null，建議使用GetPayeeByKeyAsync方法
            return Task.FromResult<Payee?>(null);
        }

        // 根據複合主鍵取得收款人資料（無權限控制 - 管理功能用）
        public async Task<Payee?> GetPayeeByKeyAsync(string collectNo, string collecAcc)
        {
            try
            {
                return await _context.Payee.FindAsync(collectNo, collecAcc);
            }
            catch
            {
                return null;
            }
        }

        // 根據複合主鍵取得收款人資料 (別名方法)
        public async Task<Payee?> GetPayeeByKeysAsync(string collectNo, string collecAcc)
        {
            return await GetPayeeByKeyAsync(collectNo, collecAcc);
        }

        // 新增收款人資料
        public async Task<bool> AddPayeeAsync(Payee payee)
        {
            try
            {
                _context.Payee.Add(payee);
                await _context.SaveChangesAsync();
                return true;
            }
            catch
            {
                return false;
            }
        }

        // 更新收款人資料
        public async Task<bool> UpdatePayeeAsync(Payee payee)
        {
            try
            {
                // 先從資料庫取得現有的實體
                var existingPayee = await _context.Payee
                    .FindAsync(payee.CollectNo, payee.CollecAcc);

                if (existingPayee == null)
                {
                    return false; // 找不到要更新的資料
                }

                // 只更新可以修改的欄位，不更新 Sn（自動遞增欄位）
                existingPayee.CollectName = payee.CollectName;
                existingPayee.CollectId = payee.CollectId;
                existingPayee.Tel = payee.Tel;
                existingPayee.Zip = payee.Zip;
                existingPayee.Addr = payee.Addr;
                existingPayee.BelongUnit = payee.BelongUnit;
                existingPayee.BelongAcc = payee.BelongAcc;
                existingPayee.Shared = payee.Shared;
                existingPayee.Createdate = payee.Createdate;

                await _context.SaveChangesAsync();
                return true;
            }
            catch
            {
                return false;
            }
        }

        // 刪除收款人資料
        public Task<bool> DeletePayeeAsync(int id)
        {
            // 由於使用複合主鍵，這個方法需要重新實作
            return Task.FromResult(false);
        }

        // 根據複合主鍵刪除收款人資料 (需要權限檢查)
        public async Task<bool> DeletePayeeByKeyAsync(string collectNo, string collecAcc)
        {
            try
            {
                var payee = await _context.Payee.FindAsync(collectNo, collecAcc);
                if (payee == null)
                    return false;

                // 權限檢查：只能刪除屬於自己的資料，或管理員可以刪除所有資料
                if (!_userState.IsAdmin && payee.BelongAcc != _userState.Account)
                {
                    throw new UnauthorizedAccessException("您只能刪除屬於自己的收款人資料");
                }

                _context.Payee.Remove(payee);
                await _context.SaveChangesAsync();
                return true;
            }
            catch
            {
                return false;
            }
        }

        // 根據複合主鍵刪除收款人資料 (重載方法)
        public async Task<bool> DeletePayeeAsync(string collectNo, string collecAcc)
        {
            return await DeletePayeeByKeyAsync(collectNo, collecAcc);
        }

        // 檢查資料庫連接
        public async Task<bool> TestConnectionAsync()
        {
            try
            {
                await _context.Database.CanConnectAsync();
                return true;
            }
            catch
            {
                return false;
            }
        }

        // 取得收款人總數
        public async Task<int> GetTotalPayeesAsync()
        {
            try
            {
                return await _context.Payee.CountAsync();
            }
            catch
            {
                return 0;
            }
        }

        // 檢查帳號是否已存在
        public async Task<bool> IsAccountExistsAsync(string collectNo, string collecAcc, int? excludeId = null)
        {
            try
            {
                return await _context.Payee
                    .Where(p => p.CollectNo == collectNo && p.CollecAcc == collecAcc)
                    .AnyAsync();
            }
            catch
            {
                return false;
            }
        }

        // 檢查帳號是否已存在 (重載方法，無excludeId參數)
        public Task<bool> IsAccountExistsAsync(string collectNo, string collecAcc)
        {
            return IsAccountExistsAsync(collectNo, collecAcc, null);
        }

        public async Task<List<string>> GetPayeeNameSuggestionsAsync(string searchTerm)
        {
            if (string.IsNullOrWhiteSpace(searchTerm))
                return new List<string>();

            var query = _context.Payee
                .Where(p => p.CollectName != null && p.CollectName.Contains(searchTerm));

            // 加入 RemitMt 專用的權限控制邏輯
            query = ApplyRemitMtPermissionFilter(query);

            return await query
                .Select(p => p.CollectName!)
                .Distinct()
                .Take(10)
                .ToListAsync();
        }

        public async Task<Payee?> GetPayeeByNameAsync(string name)
        {
            if (string.IsNullOrWhiteSpace(name))
                return null;

            var query = _context.Payee
                .Where(p => p.CollectName == name);

            // 加入 RemitMt 專用的權限控制邏輯
            query = ApplyRemitMtPermissionFilter(query);

            var payee = await query.FirstOrDefaultAsync();

            if (payee != null && !string.IsNullOrEmpty(payee.CollectNo))
            {
                payee.FinancialName = await GetFinancialNameByNoAsync(payee.CollectNo);
            }

            return payee;
        }

        public async Task<Payee?> GetPayeeByNameAndAccountAsync(string name, string account)
        {
            if (string.IsNullOrWhiteSpace(name) || string.IsNullOrWhiteSpace(account))
                return null;

            var query = _context.Payee
                .Where(p => p.CollectName == name && p.CollecAcc == account);

            // 加入 RemitMt 專用的權限控制邏輯
            query = ApplyRemitMtPermissionFilter(query);

            var payee = await query.FirstOrDefaultAsync();

            if (payee != null && !string.IsNullOrEmpty(payee.CollectNo))
            {
                payee.FinancialName = await GetFinancialNameByNoAsync(payee.CollectNo);
            }

            return payee;
        }

        public async Task<string> GetFinancialNameByNoAsync(string financialNo)
        {
            if (string.IsNullOrWhiteSpace(financialNo))
                return "";

            var financial = await _context.Financial
                .FirstOrDefaultAsync(f => f.Code == financialNo);

            return financial?.Name ?? "";
        }

        /// <summary>
        /// 除錯用：檢查特定帳號的收款人資料
        /// </summary>
        public async Task<List<Payee>> DebugGetPayeesByAccountAsync(string account)
        {
            try
            {
                var allPayees = await _context.Payee.ToListAsync();
                var matchingPayees = allPayees.Where(p => p.BelongAcc == account).ToList();

                // 記錄除錯資訊
                Console.WriteLine($"DebugGetPayeesByAccountAsync: 總收款人資料 {allPayees.Count} 筆");
                Console.WriteLine($"DebugGetPayeesByAccountAsync: 符合帳號 {account} 的資料 {matchingPayees.Count} 筆");

                foreach (var payee in matchingPayees)
                {
                    Console.WriteLine($"  - CollectName: {payee.CollectName}, BelongAcc: {payee.BelongAcc}, BelongUnit: {payee.BelongUnit}, Shared: {payee.Shared}");
                }

                return matchingPayees;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"DebugGetPayeesByAccountAsync 發生錯誤: {ex.Message}");
                return new List<Payee>();
            }
        }

        /// <summary>
        /// 根據 SSO 使用者權限過濾 Payee 查詢（專用於 RemitMt 自動完成功能）
        /// </summary>
        /// <param name="query">原始查詢</param>
        /// <returns>加入權限控制的查詢</returns>
        private IQueryable<Payee> ApplyRemitMtPermissionFilter(IQueryable<Payee> query)
        {
            // 如果使用者資訊未初始化，返回空結果
            if (!_userState.IsInitialized || string.IsNullOrEmpty(_userState.Account))
            {
                return query.Where(p => false); // 返回空結果
            }

            // RemitMt 專用權限控制邏輯：
            // 1. 當 Payee.Shared == "否"（非共用資料）時：SSO 的 Account 必須等於 Payee.BelongAcc
            // 2. 當 Payee.Shared == "是"（共用資料）時：SSO 的 DepCode 必須等於 Payee.BelongUnit

            var userAccount = _userState.Account;
            var userDepCode = _userState.DepCode;

            return query.Where(p =>
                (p.Shared == "否" && p.BelongAcc == userAccount) ||  // 非共用：屬於自己帳號的資料
                (p.Shared == "是" && p.BelongUnit == userDepCode)    // 共用：同部門的資料
            );
        }
    }
}
