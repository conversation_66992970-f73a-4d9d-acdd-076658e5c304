﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Intra2025.Models
{
    // Youth & Child Care Reporting Service (YCRS)
    public class YCRS_ChildRecord
    {
        [Key]
        public string? Id { get; set; } //系統代碼

        public DateTime? BelongDate { get; set; } // 案件所屬月份

        [Required(ErrorMessage = "【子女姓名】為必填欄位")]
        [MaxLength(30, ErrorMessage = "【子女姓名】不可超過 30 個字元")]
        public string? ChildName { get; set; } // 子女姓名

        [Required(ErrorMessage = "【子女身份證字號】為必填欄位")]
        [MaxLength(10, ErrorMessage = "【子女身份證字號】不可超過 10 個字元")]
        public string? ChildIdNumber { get; set; } // 子女身份證字號

        public DateTime? BirthDate { get; set; } // 子女出生年月日

        [MaxLength(100, ErrorMessage = "【父親姓名】不可超過 100 個字元")]
        public string? Parent1Name { get; set; } // 監護人1姓名

        [MaxLength(10, ErrorMessage = "【父親身份證字號】不可超過 10 個字元")]
        public string? Parent1Id { get; set; } // 監護人1身份證字號
        public bool Pt1Imprison {  get; set; } // 入監人口

        [MaxLength(20, ErrorMessage = "【與監護人1的關係】不可超過 20 個字元")]
        public string? Parent1Relation { get; set; } // 與監護人1的關係

        [MaxLength(100, ErrorMessage = "【母親姓名】不可超過 100 個字元")]
        public string? Parent2Name { get; set; } // 監護人2姓名

        [MaxLength(10, ErrorMessage = "【母親身份證字號】不可超過 10 個字元")]
        public string? Parent2Id { get; set; } // 監護人2身份證字號
        public bool Pt2Imprison { get; set; } // 入監人口

        [MaxLength(20, ErrorMessage = "【與監護人2的關係】不可超過 20 個字元")]
        public string? Parent2Relation { get; set; } // 與監護人2的關係

        [Required(ErrorMessage = "【戶籍地址】為必填欄位")]
        [MaxLength(100, ErrorMessage = "【戶籍地址】不可超過 100 個字元")]
        public string? HouseholdAddress { get; set; } // 戶籍地址

        public string? CurrentAddress { get; set; } // 實際居住地址

        [MaxLength(100, ErrorMessage = "【聯繫人】不可超過 100 個字元")]
        public string? Contactor { get; set; } // 聯繫人

        [MaxLength(100, ErrorMessage = "【聯繫電話】不可超過 100 個字元")]
        public string? ContactCommunication { get; set; } // 聯繫電話

        [Required(ErrorMessage = "【個案類型】為必填欄位")]
        [MaxLength(5, ErrorMessage = "【個案類型】不可超過 5 個字元")]
        public string? AttributesId { get; set; } // 個案類型

        [MaxLength(6, ErrorMessage = "【個案所屬戶所】不可超過 6 個字元")]
        public string? CaseBelongId { get; set; } // 個案所屬戶所

        [MaxLength(200, ErrorMessage = "【備註】不可超過 200 個字元")]
        public string? Remarks { get; set; } // 備註

        public bool IsChecked { get; set; } // 是否確認過此筆資料
        public bool IsMailed { get; set; } // 是否已MAIL通知過此筆資料給社會處承辦人      
        public DateTime? CreateDate { get; set; } // 案件新增日期
        public string? CreateName { get; set; } // 填表人

    }
}
