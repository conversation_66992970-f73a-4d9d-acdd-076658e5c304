@page "/ComRemit/FinancialInstitutionEdit"
@page "/ComRemit/FinancialInstitutionEdit/{id:int}"
@rendermode @(new InteractiveServerRenderMode(prerender: false))
@inject IJSRuntime JSRuntime
@inject NavigationManager Navigation
@inject ComRemitDatabaseInitializationService DatabaseService
@using Intra2025.Services
@using Intra2025.Models.ComRemit
@using Intra2025.Components.Base
@using Microsoft.AspNetCore.Components.Web

@inherits BasePageComponent

<PageTitle>@(Id.HasValue ? "編輯" : "新增")金融機關</PageTitle>

<div class="container-fluid mt-3">
    <div class="card">
        <div class="card-header">
            <h4 style="color: #0033CC; font-weight: bold;">@(Id.HasValue ? "編輯" : "新增")金融機構資料</h4>
        </div>
        <div class="card-body">
            @if (!_userState.IsAdmin)
            {
                <!-- 非管理員只顯示錯誤訊息，不顯示任何功能 -->
                <div class="alert alert-warning">
                    <h5>存取被拒絕</h5>
                    <p>此頁面僅限管理員使用。如果您認為這是錯誤，請聯繫系統管理員。</p>
                </div>
            }
            else
            {
                <div class="row justify-content-center">
                    <div class="col-md-8">
                        <EditForm Model="@institution" OnValidSubmit="HandleValidSubmit">
                            <DataAnnotationsValidator />
                            <ValidationSummary class="text-danger mb-3" />

                            <div class="mb-3">
                                <label for="name" class="form-label">金融機構名稱 <span class="text-danger">*</span></label>
                                <InputText id="name" class="form-control" @bind-Value="institution.Name" maxlength="40" />
                                <small class="form-text text-muted">最多40個字元</small>
                                <ValidationMessage For="@(() => institution.Name)" class="text-danger" />
                            </div>

                            <div class="mb-3">
                                <label for="code" class="form-label">金融機構代號 <span class="text-danger">*</span></label>
                                <InputText id="code" class="form-control" @bind-Value="institution.Code" maxlength="8" />
                                <small class="form-text text-muted">最多8個字元</small>
                                <ValidationMessage For="@(() => institution.Code)" class="text-danger" />
                            </div>

                            <div class="mb-3">
                                <label for="address" class="form-label">金融機構住址</label>
                                <InputText id="address" class="form-control" @bind-Value="institution.Address" maxlength="60" />
                                <small class="form-text text-muted">最多60個字元</small>
                                <ValidationMessage For="@(() => institution.Address)" class="text-danger" />
                            </div>

                            <div class="mb-3">
                                <label for="category" class="form-label">金融機構類別</label>
                                <InputSelect id="category" class="form-select" @bind-Value="institution.CategoryId">
                                    <option value="">請選擇</option>
                                    <option value="1">本國銀行</option>
                                    <option value="2">信用合作社</option>
                                    <option value="3">農漁會信用部</option>
                                    <option value="4">郵局</option>
                                    <option value="5">外國銀行</option>
                                </InputSelect>
                                <ValidationMessage For="@(() => institution.CategoryId)" class="text-danger" />
                            </div>

                            <div class="d-flex justify-content-end">
                                <button type="submit" class="btn btn-primary">
                                    @(Id.HasValue ? "更新" : "新增")
                                </button>
                            </div>
                        </EditForm>

                        <!-- 返回按鈕移到表單外面 -->
                        <div class="mt-3">
                            <button type="button" class="btn btn-secondary" @onclick="GoBack">返回</button>
                        </div>
                    </div>
                </div>
            } <!-- 結束 else 區塊 -->
        </div>
    </div>
</div>

@code {
    [Parameter] public int? Id { get; set; }
    
    private Financial institution = new Financial();

    protected override async Task OnInitializedAsync()
    {
        await base.OnInitializedAsync();
        
        // 檢查是否為管理者權限
        if (!_userState.IsAdmin)
        {
            return;
        }

        if (Id.HasValue)
        {
            await LoadInstitution(Id.Value);
        }
    }

    private async Task LoadInstitution(int id)
    {
        try
        {
            var loadedInstitution = await DatabaseService.GetFinancialByIdAsync(id);
            if (loadedInstitution != null)
            {
                institution = loadedInstitution;
            }
            else
            {
                await JSRuntime.InvokeVoidAsync("alert", "找不到指定的金融機構資料");
                Navigation.NavigateTo($"{Environment.GetEnvironmentVariable("ASPNETCORE_APPL_PATH")?.TrimEnd('/')}/ComRemit/FinancialInstitutionList", forceLoad: true);
            }
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("alert", $"載入資料失敗：{ex.Message}");
            Logger.LogError(ex, "載入金融機構資料時發生錯誤，ID: {Id}", id);
        }
    }

    private async Task HandleValidSubmit()
    {
        try
        {
            // 權限檢查：只有管理員可以新增/編輯
            if (!_userState.IsAdmin)
            {
                await JSRuntime.InvokeVoidAsync("alert", "您沒有權限執行此操作");
                return;
            }

            bool success = false;

            if (Id.HasValue)
            {
                // 更新資料
                success = await DatabaseService.UpdateFinancialAsync(institution);
                if (success)
                {
                    await JSRuntime.InvokeVoidAsync("alert", "更新成功!");
                    Logger.LogInformation($"管理員 {_userState.Account} 更新了金融機關資料 ID: {Id?.ToString() ?? "未知"}");
                }
                else
                {
                    await JSRuntime.InvokeVoidAsync("alert", "更新失敗!");
                    return;
                }
            }
            else
            {
                // 新增資料
                success = await DatabaseService.CreateFinancialAsync(institution);
                if (success)
                {
                    await JSRuntime.InvokeVoidAsync("alert", "新增成功!");
                    Logger.LogInformation($"管理員 {_userState.Account} 新增了金融機關資料：{institution.Name}");
                }
                else
                {
                    await JSRuntime.InvokeVoidAsync("alert", "新增失敗!");
                    return;
                }
            }

            Navigation.NavigateTo($"{Environment.GetEnvironmentVariable("ASPNETCORE_APPL_PATH")?.TrimEnd('/')}/ComRemit/FinancialInstitutionList", forceLoad: true);
        }
        catch (UnauthorizedAccessException)
        {
            await JSRuntime.InvokeVoidAsync("alert", "您沒有權限執行此操作");
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("alert", $"操作失敗：{ex.Message}");
            Logger.LogError(ex, "儲存金融機構資料時發生錯誤");
        }
    }

    private void GoBack()
    {
        // 直接導航到清單頁面，不執行任何資料更新操作
        Navigation.NavigateTo($"{Environment.GetEnvironmentVariable("ASPNETCORE_APPL_PATH")?.TrimEnd('/')}/ComRemit/FinancialInstitutionList");
    }
}
