@inherits LayoutComponentBase
@using Intra2025.Components.Base
@using Intra2025.Services
@inject UserState _userState
@inject SsoService sso
@inject IJSRuntime JSRuntime
@inject ILogger<YCRSLayout> Logger
@implements IDisposable

<div class="page">
    <header class="top-header">
        <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
            <div class="container-fluid">
                <a class="navbar-brand"
                    href="@(Environment.GetEnvironmentVariable("ASPNETCORE_APPL_PATH")?.TrimEnd('/'))/">守護寶貝即時通</a>

                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav"
                    aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                    <span class="navbar-toggler-icon"></span>
                </button>

                <div class="collapse navbar-collapse" id="navbarNav">
                    @if (isInitialized)
                    {
                        <ul class="navbar-nav ms-auto">
                            <li class="nav-item">
                                <a class="nav-link"
                                    href="@(Environment.GetEnvironmentVariable("ASPNETCORE_APPL_PATH")?.TrimEnd('/'))/ChildCaseList">
                                    【守護寶貝即時通】
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link"
                                    href="@(Environment.GetEnvironmentVariable("ASPNETCORE_APPL_PATH")?.TrimEnd('/'))/CareCaseList">
                                    【溫馨關懷表】
                                </a>
                            </li>
                            
                            @if (_userState.IsAdmin)
                            {
                                <li class="nav-item">
                                    <a class="nav-link"
                                        href="@(Environment.GetEnvironmentVariable("ASPNETCORE_APPL_PATH")?.TrimEnd('/'))/YCRS_AccessLogList">
                                        【歷程資料查詢】
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link"
                                        href="@(Environment.GetEnvironmentVariable("ASPNETCORE_APPL_PATH")?.TrimEnd('/'))/YCRS_ReportList">
                                        【統計報表】
                                    </a>
                                </li>
                            }

                            <!-- 使用者資訊 -->
                            <div class="navbar-text text-white me-3">
                                <span class="badge text-white"
                                    style="background-color:#127681; border-radius: 10px;font-size:14px">
                                    【@_userState.UnitName-@_userState.UserName】
                                </span>
                            </div>
                        </ul>
                    }
                </div>
            </div>
        </nav>
    </header>

    <main class="content">
        @if (isInitialized)
        {
            @Body
        }
        else
        {
            <div class="text-center mt-5">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <p class="mt-2">使用者驗證中，請稍候...</p>
            </div>
        }
    </main>
</div>

<div id="blazor-error-ui">
    An unhandled error has occurred.
    <a href="" class="reload">Reload</a>
    <a class="dismiss">🗙</a>
</div>

@code {
    private bool isInitialized = false;

    protected override async Task OnInitializedAsync()
    {
        // 監聽 UserState 的變化，以便在狀態更新時觸發 UI 重新渲染
        _userState.OnStateChanged += OnStateChanged;

        // 執行驗證
        await sso.ValidateAndInitializeUserAsync();

        // 標記初始化完成，並觸發第一次手動渲染
        isInitialized = true;
        StateHasChanged();
    }

    private void OnStateChanged()
    {
        // 當 UserState 改變時，調用 StateHasChanged 來更新 UI
        InvokeAsync(StateHasChanged);
    }

    public void Dispose()
    {
        // 取消監聽，防止記憶體洩漏
        _userState.OnStateChanged -= OnStateChanged;
    }
}
