# 安全日誌記錄與監控測試計劃

## 🎯 測試目標

驗證 Problem #9.1 (日誌記錄和監控不足) 的修復是否正確且所有安全事件都被正確記錄和監控。

---

## 🧪 測試項目

### 1. 基本安全事件記錄測試

#### 1.1 登入事件記錄測試
**目的**: 確認登入成功和失敗事件被正確記錄

**測試步驟**:
1. **登入成功測試**:
   - 清空應用程式日誌
   - 執行正常的 SSO 登入流程
   - 檢查日誌中是否有 LOGIN_SUCCESS 事件記錄
   - 驗證事件包含正確的使用者資訊、IP 位址、時間戳

2. **登入失敗測試**:
   - 移除或修改 SSO Token Cookie
   - 嘗試存取需要驗證的頁面
   - 檢查日誌中是否有 LOGIN_FAILURE 事件記錄
   - 驗證失敗原因被正確記錄

**預期結果**:
- ✅ 登入成功時記錄 LOGIN_SUCCESS 事件
- ✅ 登入失敗時記錄 LOGIN_FAILURE 事件
- ✅ 事件包含完整的上下文資訊
- ✅ 日誌格式符合預期的結構化格式

#### 1.2 檔案操作事件記錄測試
**目的**: 確認檔案下載和存取控制事件被正確記錄

**測試步驟**:
1. **合法檔案下載測試**:
   - 使用有權限的使用者下載檔案
   - 檢查日誌中是否有 FILE_DOWNLOAD 事件記錄
   - 驗證檔案資訊（名稱、大小、路徑）被正確記錄

2. **權限被拒絕測試**:
   - 嘗試下載無權限的檔案
   - 檢查日誌中是否有 ACCESS_DENIED 事件記錄
   - 驗證拒絕原因被正確記錄

3. **路徑遍歷攻擊測試**:
   - 嘗試使用包含 `../` 的檔案路徑
   - 檢查日誌中是否有 SUSPICIOUS_ACTIVITY 事件記錄
   - 驗證攻擊嘗試被正確檢測和記錄

**預期結果**:
- ✅ 檔案下載成功時記錄 FILE_DOWNLOAD 事件
- ✅ 權限檢查失敗時記錄 ACCESS_DENIED 事件
- ✅ 路徑遍歷攻擊時記錄 SUSPICIOUS_ACTIVITY 事件
- ✅ 所有事件包含相關的檔案和使用者資訊

### 2. 即時監控和威脅檢測測試

#### 2.1 多次登入失敗檢測測試
**目的**: 確認多次登入失敗監控機制正常運作

**測試步驟**:
1. 在短時間內（5分鐘內）多次嘗試無效登入（超過5次）
2. 檢查是否觸發 MULTIPLE_LOGIN_FAILURES 警報
3. 驗證登入失敗計數器是否正確工作
4. 測試成功登入後計數器是否被重置

**預期結果**:
- ✅ 達到閾值時觸發 MULTIPLE_LOGIN_FAILURES 事件
- ✅ 計數器正確追蹤失敗次數
- ✅ 成功登入後計數器被清除
- ✅ 時間窗口機制正確運作

#### 2.2 異常 IP 存取檢測測試
**目的**: 確認異常 IP 存取檢測功能

**測試步驟**:
1. 從不同的 IP 位址登入系統
2. 檢查日誌中是否記錄新 IP 的資訊事件
3. 驗證 IP 白名單機制是否正確運作

**預期結果**:
- ✅ 新 IP 登入時記錄資訊事件
- ✅ 內網 IP 被正確識別為已知 IP
- ✅ IP 檢測邏輯正確運作

### 3. 配置和自訂測試

#### 3.1 日誌等級配置測試
**目的**: 確認日誌等級配置正確運作

**測試步驟**:
1. 修改 `appsettings.json` 中的 `MinimumLogLevel` 為 "Warning"
2. 觸發不同嚴重性等級的事件
3. 驗證只有 Warning 和以上等級的事件被記錄
4. 恢復配置並測試所有等級的事件記錄

**預期結果**:
- ✅ 只有符合最小等級的事件被記錄
- ✅ 配置變更立即生效
- ✅ 不同嚴重性等級正確分類

#### 3.2 監控閾值配置測試
**目的**: 確認監控閾值可以正確配置

**測試步驟**:
1. 修改 `LoginFailureThreshold` 為 3
2. 測試 3 次登入失敗後是否觸發警報
3. 修改 `LoginFailureWindowMinutes` 為 2
4. 測試時間窗口是否正確運作

**預期結果**:
- ✅ 新的閾值設定立即生效
- ✅ 時間窗口設定正確運作
- ✅ 配置變更不影響系統穩定性

### 4. 整合和相容性測試

#### 4.1 現有功能相容性測試
**目的**: 確認新的安全日誌系統不影響現有功能

**測試步驟**:
1. 執行完整的 SSO 登入流程
2. 測試所有檔案上傳和下載功能
3. 驗證所有 YCRS 相關操作
4. 檢查是否有任何功能異常

**預期結果**:
- ✅ 所有現有功能正常運作
- ✅ 沒有效能明顯下降
- ✅ 使用者體驗不受影響

#### 4.2 傳統稽核日誌相容性測試
**目的**: 確認新系統與現有 AuditLogService 相容

**測試步驟**:
1. 觸發使用 AuditLogService 的操作
2. 檢查傳統稽核日誌是否正常記錄
3. 驗證新的安全事件日誌是否同時記錄
4. 確認兩套系統互不干擾

**預期結果**:
- ✅ 傳統稽核日誌繼續正常運作
- ✅ 新的安全事件日誌正確記錄
- ✅ 兩套系統和諧共存

### 5. 錯誤處理和邊界測試

#### 5.1 異常情況處理測試
**目的**: 確認系統在異常情況下的穩定性

**測試步驟**:
1. 模擬日誌記錄服務異常
2. 測試系統是否能繼續正常運作
3. 檢查錯誤處理是否適當
4. 驗證不會影響主要業務流程

**預期結果**:
- ✅ 日誌記錄失敗不影響主要功能
- ✅ 適當的錯誤處理和恢復機制
- ✅ 系統保持穩定運行

#### 5.2 大量事件處理測試
**目的**: 確認系統能處理大量安全事件

**測試步驟**:
1. 模擬大量並發的安全事件
2. 檢查系統效能和穩定性
3. 驗證所有事件都被正確記錄
4. 測試記憶體使用情況

**預期結果**:
- ✅ 系統能處理大量並發事件
- ✅ 效能保持在可接受範圍內
- ✅ 記憶體使用合理
- ✅ 沒有事件遺失

---

## 🔍 測試檢查清單

### 基本功能檢查
- [ ] LOGIN_SUCCESS 事件正確記錄
- [ ] LOGIN_FAILURE 事件正確記錄
- [ ] FILE_DOWNLOAD 事件正確記錄
- [ ] ACCESS_DENIED 事件正確記錄
- [ ] SUSPICIOUS_ACTIVITY 事件正確記錄

### 監控功能檢查
- [ ] 多次登入失敗檢測正常
- [ ] 路徑遍歷攻擊檢測正常
- [ ] 異常 IP 存取檢測正常
- [ ] 時間窗口機制正確運作

### 配置功能檢查
- [ ] 日誌等級配置生效
- [ ] 監控閾值配置生效
- [ ] 時間窗口配置生效
- [ ] 功能開關配置生效

### 相容性檢查
- [ ] 現有功能正常運作
- [ ] 傳統稽核日誌正常
- [ ] 效能沒有明顯下降
- [ ] 使用者體驗不受影響

### 穩定性檢查
- [ ] 異常情況處理正確
- [ ] 大量事件處理正常
- [ ] 記憶體使用合理
- [ ] 系統保持穩定

---

## 📊 測試報告模板

### 測試結果記錄

**測試日期**: ___________  
**測試人員**: ___________  
**測試環境**: ___________

#### 基本功能測試結果
- [ ] ✅ 通過 / [ ] ❌ 失敗 - 登入事件記錄
- [ ] ✅ 通過 / [ ] ❌ 失敗 - 檔案操作事件記錄
- [ ] ✅ 通過 / [ ] ❌ 失敗 - 存取控制事件記錄
- **備註**: ________________________________

#### 監控功能測試結果
- [ ] ✅ 通過 / [ ] ❌ 失敗 - 多次登入失敗檢測
- [ ] ✅ 通過 / [ ] ❌ 失敗 - 路徑遍歷攻擊檢測
- [ ] ✅ 通過 / [ ] ❌ 失敗 - 異常 IP 存取檢測
- **備註**: ________________________________

#### 配置功能測試結果
- [ ] ✅ 通過 / [ ] ❌ 失敗 - 日誌等級配置
- [ ] ✅ 通過 / [ ] ❌ 失敗 - 監控閾值配置
- [ ] ✅ 通過 / [ ] ❌ 失敗 - 功能開關配置
- **備註**: ________________________________

#### 整體評估
- [ ] ✅ 所有測試通過，修復成功
- [ ] ⚠️ 部分測試失敗，需要進一步修復
- [ ] ❌ 測試失敗，需要重新檢視修復方案

**總結**: ________________________________

---

## 🚀 部署前確認

在將修復部署到生產環境前，請確認：

### 功能確認
- [ ] 所有安全事件都被正確記錄
- [ ] 監控和警報機制正常運作
- [ ] 配置選項都能正確生效
- [ ] 現有功能沒有受到影響

### 效能確認
- [ ] 系統效能沒有明顯下降
- [ ] 記憶體使用在合理範圍內
- [ ] 日誌記錄不會造成效能瓶頸
- [ ] 大量事件處理正常

### 安全確認
- [ ] 敏感資訊不會洩露到日誌中
- [ ] 安全事件格式符合要求
- [ ] 威脅檢測機制有效
- [ ] 符合安全合規要求

### 維運確認
- [ ] 日誌輪轉和清理機制
- [ ] 監控和警報通道設定
- [ ] 故障排除文件準備
- [ ] 運維人員培訓完成

---

## 📞 支援資訊

如果在測試過程中遇到問題，請參考：
- `SECURITY_LOGGING_MONITORING_FIX.md` - 詳細的修復說明
- `Config/SecurityLogConfig.json` - 配置選項說明
- 應用程式日誌 - 除錯和故障排除資訊

**重要提醒**: 如果發現任何問題，請立即停止測試並聯繫開發團隊。
