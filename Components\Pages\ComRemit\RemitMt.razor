@page "/ComRemit/RemitMt"
@page "/ComRemit/RemitMt/{ConSnoParam}"
@rendermode @(new InteractiveServerRenderMode(prerender: false))
@implements IDisposable
@inject ComRemitService RemitService
@inject ComRemitPayeeService PayeeService
@inject IJSRuntime JSRuntime
@using Intra2025.Services
@using Intra2025.Models.ComRemit
@using Intra2025.Components.Base
@using Microsoft.AspNetCore.Components.Web
@using Microsoft.AspNetCore.Components.Forms
@using System.ComponentModel.DataAnnotations
@inherits BasePageComponent

<PageTitle>匯款整彙作業</PageTitle>

<!-- 樣式定義 -->
<style>
    .remit-mt-page {
        width: 100%;
        margin: 0;
        padding: 20px;
        box-sizing: border-box;
        font-family: 'Microsoft JhengHei', sans-serif;
    }

    .form-section {
        background-color: #ccffff;
        padding: 20px;
        margin-bottom: 20px;
        border-radius: 5px;
    }

    .batch-upload-section {
        background-color: #ccffcc;
        padding: 20px;
        margin-bottom: 20px;
        border-radius: 5px;
    }

    .search-section {
        background-color: #f8f9fa;
        padding: 20px;
        border-radius: 8px;
        margin-bottom: 20px;
    }

    .required {
        color: red;
        font-size: 18px;
        font-weight: bold;
    }

    .readonly-input {
        background-color: #cccccc;
    }

    .autocomplete-container {
        position: relative;
        display: inline-block;
        width: 300px;
    }

    .autocomplete-suggestions {
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background: white;
        border: 1px solid #ccc;
        border-top: none;
        max-height: 200px;
        overflow-y: auto;
        z-index: 1000;
    }

    .autocomplete-suggestion {
        padding: 8px 12px;
        cursor: pointer;
        border-bottom: 1px solid #eee;
    }

    .autocomplete-suggestion:hover {
        background-color: #f0f0f0;
    }

    .table-container {
        max-height: 600px;
        overflow-y: auto;
        border: 1px solid #dee2e6;
        border-radius: 8px;
    }

    .sticky-header {
        position: sticky;
        top: 0;
        z-index: 10;
        background-color: #507CD1;
    }

    .amount-input {
        width: 100px;
    }

    .memo-input {
        width: 200px;
    }

    .total-section {
        background-color: #e9ecef;
        padding: 15px;
        border-radius: 8px;
        margin: 20px 0;
    }
</style>

<div class="remit-mt-page">
    <div class="card">
        <div class="card-header">
             <h4 style="color: #0033CC; font-weight: bold;">【匯款整彙作業(step2)】</h4>
        </div>
        <div class="card-body" style="padding: 20px;">
            @if (!string.IsNullOrEmpty(errorMessage))
            {
                <div class="alert alert-danger">@errorMessage</div>
            }

            @if (!string.IsNullOrEmpty(successMessage))
            {
                <div class="alert alert-success">@successMessage</div>
            }

            <!-- 資料新增模式選擇 -->
            <div style="margin-bottom: 20px;">
                <label>資料新增模式:</label>
                <div style="margin-left: 20px;">
                    <input type="radio" id="single" name="dataMode" value="single" @onchange="OnDataModeChanged" checked="@(dataMode == "single")" />
                    <label for="single" style="margin-right: 20px;">單筆</label>

                    <input type="radio" id="batch" name="dataMode" value="batch" @onchange="OnDataModeChanged" checked="@(dataMode == "batch")" />
                    <label for="batch">整批匯入</label>
                </div>
            </div>

            <!-- 單筆新增區塊 -->
            @if (dataMode == "single")
            {
                <div class="form-section">
                    <table style="width: 100%;">
                        <tr>
                            <td style="width: 180px;"><span class="required">*</span>收款人戶名</td>
                            <td>
                                <div class="autocomplete-container">
                                    <input type="text" class="form-control" @bind="collectName" @oninput="OnCollectNameInput"
                                           @onfocusout="OnCollectNameBlur" placeholder="請輸入收款人戶名" style="width: 300px;" />
                                    @if (showSuggestions && suggestions.Any())
                                    {
                                        <div class="autocomplete-suggestions">
                                            @foreach (var suggestion in suggestions)
                                            {
                                                <div class="autocomplete-suggestion" @onclick="() => SelectSuggestion(suggestion)">
                                                    @suggestion
                                                </div>
                                            }
                                        </div>
                                    }
                                </div>
                            </td>
                            <td>身分證字號(統編)</td>
                            <td>
                                <input type="text" class="form-control readonly-input" @bind="collectId" readonly />
                            </td>
                        </tr>
                        <tr>
                            <td><span class="required">*</span>收款人帳號</td>
                            <td>
                                <input type="text" class="form-control readonly-input" @bind="collectAcc" readonly />
                            </td>
                            <td style="width: 180px;">解款行代號</td>
                            <td>
                                <input type="text" class="form-control" @bind="collectNo" @bind:event="oninput" @onblur="OnCollectNoChanged" style="width: 150px; display: inline-block;" />
                                <input type="text" class="form-control readonly-input" @bind="financialName" readonly style="width: 200px; display: inline-block; margin-left: 5px;" />
                            </td>
                        </tr>
                        <tr>
                            <td>電話</td>
                            <td>
                                <input type="text" class="form-control readonly-input" @bind="tel" readonly />
                            </td>
                            <td style="width: 180px;">郵遞區號</td>
                            <td>
                                <input type="text" class="form-control readonly-input" @bind="zip" readonly />
                            </td>
                        </tr>
                        <tr>
                            <td>住址</td>
                            <td colspan="3">
                                <input type="text" class="form-control readonly-input" @bind="addr" readonly style="width: 400px;" />
                            </td>
                        </tr>
                        <tr>
                            <td><span class="required">*</span>帳款金額</td>
                            <td>
                                <input type="number" class="form-control" @bind="remitPrice" step="0.01" min="0" />
                            </td>
                            <td><span class="required">*</span>手續費類別<br />(詳備註三)</td>
                            <td>
                                <select class="form-control" @bind="feeCategory" style="width: 100%;">
                                    <option value="">請選擇手續費類別</option>
                                    <option value="1">1. 給付本府及所屬機關學校員工之各項款項(不需手續費)</option>
                                    <option value="2">2. 給付其他機關、學校之各項公庫款項(不需手續費)</option>
                                    <option value="3">3. 補助團體、個人之各項社會福利性質款項(不需手續費)</option>
                                    <option value="4">4. 給付之郵資、電信(含通聯費)、水費、電費及油品等款項(不需手續費)</option>
                                    <option value="5">5. 給付小額 999 元以下之款項(不需手續費)</option>
                                    <option value="6">6. 受款人帳戶為代庫銀行帳戶者(不需手續費)</option>
                                    <option value="7">7. 郵局帳號(不需手續費)</option>
                                    <option value="8">8. 其它(需扣手續費)</option>
                                </select>
                            </td>
                        </tr>
                        <tr>
                            <td>備註</td>
                            <td>
                                <input type="text" class="form-control" @bind="remitMemo" maxlength="40" style="width: 300px;" />
                            </td>
                            <td> </td>
                            <td>
                                @* 彙整編號: <strong>@conSno</strong> *@
                            </td>
                        </tr>
                        <tr>
                            <td colspan="3">
                                <button class="btn btn-primary" @onclick="AddRemitItem">確認新增此筆彙款資料</button>
                            </td>
                            <td></td>
                        </tr>
                    </table>
                </div>
            }

            <!-- 整批匯入區塊 -->
            @if (dataMode == "batch")
            {
                <div class="batch-upload-section">
                    <div style="margin-bottom: 15px;">
                        <InputFile OnChange="HandleFileSelected" accept=".csv" />
                        <button class="btn btn-success" @onclick="ProcessBatchUpload" disabled="@(selectedFile == null)">整批匯入</button>
                        <a href="ExportTxt/匯入範例檔.csv" class="btn btn-link">下載範例檔</a>
                    </div>
                    <div style="color: #FF0066; font-weight: bold;">
                        (PS:備註欄位字數請控制在40字內，避免使用全形或特殊符號)
                    </div>
                </div>
            }

            <!-- 資料列表 -->
            <div style="margin-top: 30px;">
                @if (isLoading)
                {
                    <div style="text-align: center; padding: 30px;">
                        <div class="spinner-border" role="status">
                            <span class="visually-hidden">載入中...</span>
                        </div>
                        <p>正在載入資料...</p>
                    </div>
                }
                else
                {

                    <table class="table table-bordered table-striped">
                        <thead style="background-color: #507CD1; color: white;">
                            <tr>
                                <th>功能</th>
                                <th>收款人金融局號</th>
                                <th>收款人帳號</th>
                                <th>收款人戶名</th>
                                <th>身分證字號(統編)</th>
                                <th>帳款金額</th>
                                <th>備註</th>
                                <th>帳款是否有手續費</th>
                            </tr>
                        </thead>
                        <tbody>
                            @if (remitList.Any())
                            {
                                @foreach (var item in remitList)
                                {
                                    <tr>
                                        <td>
                                            @if (editingSno == item.Sno)
                                            {
                                                <button class="btn btn-sm btn-success" @onclick="() => SaveEdit(item.Sno)">更新</button>
                                                <button class="btn btn-sm btn-secondary" @onclick="CancelEdit">取消</button>
                                            }
                                            else
                                            {
                                                <button class="btn btn-sm btn-primary" @onclick="() => StartEdit(item.Sno)">編輯</button>
                                                <button class="btn btn-sm btn-danger" @onclick="() => DeleteItem(item.Sno)">刪除</button>
                                            }
                                        </td>
                                        <td>@item.CollectNo</td>
                                        <td>@item.CollecAcc</td>
                                        <td>@item.CollecName</td>
                                        <td>@item.CollectId</td>
                                        <td>
                                            @if (editingSno == item.Sno)
                                            {
                                                <input type="number" class="form-control" @bind="editingPrice" step="0.01" min="0" />
                                            }
                                            else
                                            {
                                                @if (item.RemitPrice != null)
                                                {
                                                    <span> @item.RemitPrice.Value.ToString("N0")</span>
                                                }
                                                else
                                                {
                                                    <span>0</span>
                                                }
                                            }
                                        </td>
                                        <td>
                                            @if (editingSno == item.Sno)
                                            {
                                                <input type="text" class="form-control" @bind="editingMemo" maxlength="40" />
                                            }
                                            else
                                            {
                                                @item.RemitMemo
                                            }
                                        </td>
                                        <td>
                                            @if (editingSno == item.Sno)
                                            {
                                                <select class="form-control" @bind="editingFeeCategory">
                                                    <option value="">請選擇手續費類別</option>
                                                    <option value="1">1. 給付本府及所屬機關學校員工之各項款項(不需手續費)</option>
                                                    <option value="2">2. 給付其他機關、學校之各項公庫款項(不需手續費)</option>
                                                    <option value="3">3. 補助團體、個人之各項社會福利性質款項(不需手續費)</option>
                                                    <option value="4">4. 給付之郵資、電信(含通聯費)、水費、電費及油品等款項(不需手續費)</option>
                                                    <option value="5">5. 給付小額 999 元以下之款項(不需手續費)</option>
                                                    <option value="6">6. 受款人帳戶為代庫銀行帳戶者(不需手續費)</option>
                                                    <option value="7">7. 郵局帳號(不需手續費)</option>
                                                    <option value="8">8. 其它(需扣手續費)</option>
                                                </select>
                                            }
                                            else
                                            {
                                                @GetFeeCategoryDisplay(item.IfFee)
                                            }
                                        </td>
                                    </tr>
                                }
                                <tr>
                                    <td colspan="5" style="text-align: right; font-weight: bold;">總計</td>
                                    <td style="font-weight: bold;">@TotalAmount.ToString("N0")</td>
                                    <td colspan="2"></td>
                                </tr>
                            }
                            else
                            {
                                <tr>
                                    <td colspan="8" style="text-align: center; padding: 20px;">
                                        尚無資料，請由上方新增
                                    </td>
                                </tr>
                            }
                        </tbody>
                    </table>
                }
            </div>

            <!-- 彙整單用途說明 -->
            @if (remitList.Any())
            {
                <div style="margin-top: 20px; padding: 15px; background-color: #f0f0f0; border-radius: 5px;">
                    <label style="font-weight: bold;">彙整單用途說明:</label>
                    <input type="text" class="form-control" @bind="remitPurpose" maxlength="100"
                           placeholder="請輸入彙整單用途說明" style="width: 400px; display: inline-block; margin-left: 10px;" />
                </div>

                <!-- 操作按鈕 -->
                <div class="mt-4 d-flex justify-content-between">
                    <div>
                        <button class="btn btn-secondary" @onclick="ClearAll">清空全部</button>
                    </div>
                    <div>
                        <button class="btn btn-success btn-lg" @onclick="CompleteAllItems"
                                disabled="@(!remitList.Any() || string.IsNullOrWhiteSpace(remitPurpose))">
                            完成彙整並送至已彙整清單 (@remitList.Count 筆)
                        </button>
                    </div>
                </div>

                <!-- 備註說明 -->
                <div class="mt-4 p-3 border-top">
                    <h6 class="text-primary">備註：</h6>
                    <ol class="small">
                        <li>若[收款人戶名]與[收款人帳號]不符，則無法成功匯款，請重新確認</li>
                        <li>若[收款人帳號]為郵局，[解款行代號]必須為7000021</li>
                        <li>依據「臺灣銀行宜蘭分行扣收匯款手續費30元審核原則」，若解款行非「臺灣銀行」、「郵局」、「土地銀行」者，每筆須加收30元手續費</li>
                    </ol>
                </div>
            }
            else
            {
                <div class="text-center py-5">
                    <h5 class="text-muted">請先搜尋收款人或載入彙整清單</h5>
                    <p class="text-muted">您可以透過收款人戶名搜尋並加入彙整，或直接載入現有的彙整清單</p>
                </div>
            }
        </div>
    </div>
</div>

@code {
    [Parameter] public string? ConSnoParam { get; set; }

    // 基本變數
    private int conSno = 0;
    private string remitPurpose = "";
    private string errorMessage = "";
    private string successMessage = "";
    private bool isLoading = false;

    // 資料新增模式
    private string dataMode = "single";

    // 單筆新增相關變數
    private string collectName = "";
    private string collectId = "";
    private string collectAcc = "";
    private string collectNo = "";
    private string financialName = "";
    private string tel = "";
    private string zip = "";
    private string addr = "";
    private decimal? remitPrice = null;
    private string ifFee = "否";
    private string feeCategory = "";
    private string remitMemo = "";

    // 自動完成相關變數
    private List<string> suggestions = new List<string>();
    private bool showSuggestions = false;
    private Timer? debounceTimer;

    // 批次上傳相關變數
    private IBrowserFile? selectedFile;

    // 編輯相關變數
    private int editingSno = 0;
    private decimal? editingPrice = null;
    private string editingMemo = "";
    private string editingIfFee = "否";
    private string editingFeeCategory = "";

    private List<Intra2025.Models.ComRemit.RemitedList> remitList = new List<Intra2025.Models.ComRemit.RemitedList>();

    private decimal TotalAmount => remitList.Sum(x => x.RemitPrice ?? 0);

    protected override async Task OnInitializedAsync()
    {
        await base.OnInitializedAsync();

        // 如果有傳入彙整編號參數，自動載入
        if (!string.IsNullOrEmpty(ConSnoParam) && int.TryParse(ConSnoParam, out var paramConSno))
        {
            conSno = paramConSno;
            await LoadRemitList();
        }
        else
        {
            // 生成新的彙整編號
            conSno = await GenerateNewConSno();
        }
    }

    // 資料新增模式切換
    private void OnDataModeChanged(ChangeEventArgs e)
    {
        dataMode = e.Value?.ToString() ?? "single";
        ClearForm();
    }

    // 清空表單
    private void ClearForm()
    {
        collectName = "";
        collectId = "";
        collectAcc = "";
        collectNo = "";
        financialName = "";
        tel = "";
        zip = "";
        addr = "";
        remitPrice = null;
        ifFee = "否";
        feeCategory = "";
        remitMemo = "";
        showSuggestions = false;
        suggestions.Clear();
    }

    private async Task<int> GenerateNewConSno()
    {
        try
        {
            // 使用服務生成新的彙整編號
            return await RemitService.GetNextConSnoAsync();
        }
        catch
        {
            return new Random().Next(100000, 999999);
        }
    }

    // 自動完成功能
    private void OnCollectNameInput(ChangeEventArgs e)
    {
        var value = e.Value?.ToString() ?? "";
        collectName = value;

        debounceTimer?.Dispose();
        debounceTimer = new Timer(async _ =>
        {
            await InvokeAsync(async () =>
            {
                if (!string.IsNullOrWhiteSpace(collectName) && collectName.Length >= 2)
                {
                    suggestions = await PayeeService.GetPayeeNameSuggestionsAsync(collectName);
                    showSuggestions = suggestions.Any();
                }
                else
                {
                    suggestions.Clear();
                    showSuggestions = false;
                }
                StateHasChanged();
            });
        }, null, 300, Timeout.Infinite);
    }

    private void OnCollectNameBlur()
    {
        // 延遲隱藏建議，讓用戶有時間點擊
        Task.Delay(200).ContinueWith(_ => InvokeAsync(() =>
        {
            showSuggestions = false;
            StateHasChanged();
        }));
    }

    private async Task SelectSuggestion(string suggestion)
    {
        collectName = suggestion;
        showSuggestions = false;

        // 自動填入收款人資料
        var payee = await PayeeService.GetPayeeByNameAsync(suggestion);
        if (payee != null)
        {
            collectId = payee.CollectId ?? "";
            collectAcc = payee.CollecAcc ?? "";
            collectNo = payee.CollectNo ?? "";
            tel = payee.Tel ?? "";
            zip = payee.Zip ?? "";
            addr = payee.Addr ?? "";

            if (!string.IsNullOrEmpty(payee.CollectNo))
            {
                financialName = await PayeeService.GetFinancialNameByNoAsync(payee.CollectNo);
            }

            // 檢查是否為郵局代號，自動選擇手續費類別
            if (collectNo == "7000021")
            {
                feeCategory = "7";
            }
        }

        StateHasChanged();
    }

    // 手續費類別相關方法
    private string GetFeeCategoryDisplay(string? ifFeeValue)
    {
        if (string.IsNullOrEmpty(ifFeeValue)) return "";

        return ifFeeValue switch
        {
            "1" => "1. 給付本府及所屬機關學校員工之各項款項(不需手續費)",
            "2" => "2. 給付其他機關、學校之各項公庫款項(不需手續費)",
            "3" => "3. 補助團體、個人之各項社會福利性質款項(不需手續費)",
            "4" => "4. 給付之郵資、電信(含通聯費)、水費、電費及油品等款項(不需手續費)",
            "5" => "5. 給付小額 999 元以下之款項(不需手續費)",
            "6" => "6. 受款人帳戶為代庫銀行帳戶者(不需手續費)",
            "7" => "7. 郵局帳號(不需手續費)",
            "8" => "8. 其它(需扣手續費)",
            _ => ifFeeValue
        };
    }

    private string ConvertFeeCategoryToIfFee(string feeCategory)
    {
        return feeCategory == "8" ? "是" : "否";
    }

    // 手續費選擇（保留向後兼容）
    private void OnIfFeeChanged(ChangeEventArgs e)
    {
        ifFee = e.Value?.ToString() ?? "否";
    }

    // 解款行代號變更處理
    private async Task OnCollectNoChanged()
    {
        // 當解款行代號為 7000021（郵局）時，自動選擇手續費類別第7項
        if (collectNo == "7000021")
        {
            feeCategory = "7";

            // 更新金融機構名稱
            if (!string.IsNullOrEmpty(collectNo))
            {
                financialName = await PayeeService.GetFinancialNameByNoAsync(collectNo);
            }
        }
        else
        {
            // 如果不是郵局代號，更新金融機構名稱
            if (!string.IsNullOrEmpty(collectNo))
            {
                financialName = await PayeeService.GetFinancialNameByNoAsync(collectNo);
            }
        }

        StateHasChanged();
    }

    // 新增單筆資料
    private async Task AddRemitItem()
    {
        try
        {
            errorMessage = "";

            // 驗證必填欄位
            if (string.IsNullOrWhiteSpace(collectName))
            {
                errorMessage = "請輸入收款人戶名";
                return;
            }

            if (string.IsNullOrWhiteSpace(collectAcc))
            {
                errorMessage = "請輸入收款人帳號";
                return;
            }

            if (!remitPrice.HasValue || remitPrice <= 0)
            {
                errorMessage = "請輸入有效的帳款金額";
                return;
            }

            if (string.IsNullOrWhiteSpace(feeCategory))
            {
                errorMessage = "請選擇手續費類別";
                return;
            }

            // 檢查是否已存在
            var exists = remitList.Any(x => x.CollectNo == collectNo && x.CollecAcc == collectAcc);
            if (exists)
            {
                errorMessage = "此收款人已在彙整清單中";
                return;
            }

            // 將手續費類別轉換為 IfFee 值（1-6 為 "否"，7 為 "是"）
            var finalIfFee = ConvertFeeCategoryToIfFee(feeCategory);

            // 檢查解款行代號，如果是 7000021（郵局）則強制設定 IfFee 為 "否"
            if (collectNo == "7000021")
            {
                finalIfFee = "否";
            }

            var newRemitItem = new Intra2025.Models.ComRemit.RemitedList
            {
                ConSno = conSno,
                CollectNo = collectNo,
                CollecAcc = collectAcc,
                CollecName = collectName,
                CollectId = collectId,
                RemitPrice = (int?)remitPrice,
                RemitMemo = remitMemo,
                IfFee = finalIfFee, // 使用處理後的手續費設定
                ConPer = _userState.Account,
                ConUnit = _userState.UnitName,
                ConDate = DateTime.Now,
                Kindno = 1
            };

            var success = await RemitService.AddRemitItemAsync(newRemitItem);
            if (success)
            {
                // 在清空表單前先保存收款人姓名用於顯示訊息
                var savedCollectName = collectName;
                await LoadRemitList(); // 重新載入清單
                ClearForm();
                successMessage = $"已成功新增「{savedCollectName}」的匯款資料";
            }
            else
            {
                errorMessage = "新增失敗，請稍後再試";
            }
        }
        catch (Exception ex)
        {
            errorMessage = $"新增時發生錯誤：{ex.Message}";
            Logger.LogError(ex, "新增匯款資料時發生錯誤");
        }
    }

    // 批次上傳功能
    private void HandleFileSelected(InputFileChangeEventArgs e)
    {
        selectedFile = e.File;
        errorMessage = "";
        successMessage = "";
    }

    private async Task ProcessBatchUpload()
    {
        if (selectedFile == null)
        {
            errorMessage = "請先選擇要上傳的檔案。";
            return;
        }

        isLoading = true;
        StateHasChanged();

        try
        {
            var batchItems = new List<Intra2025.Models.ComRemit.RemitedList>();
            var stream = selectedFile.OpenReadStream(maxAllowedSize: 5 * 1024 * 1024); // 5MB limit
            using var reader = new System.IO.StreamReader(stream, System.Text.Encoding.GetEncoding("big5"));
            string? line;
            await reader.ReadLineAsync(); // Skip header row

            while ((line = await reader.ReadLineAsync()) != null)
            {
                try
                {
                    var values = line.Split(',');
                    if (values.Length < 5) continue; // 至少需要5個欄位才能取得帳款金額

                    // 取得解款行代號（局號）
                    var collectNoFromCsv = values[0].Trim();

                    // 取得手續費類別並轉換為 IfFee 值（1-6 為 "否"，7 為 "是"）
                    var feeCategoryFromCsv = values.Length > 5 ? values[5].Trim() : "1";
                    var ifFeeFromCsv = ConvertFeeCategoryToIfFee(feeCategoryFromCsv);

                    // 檢查解款行代號，如果是 7000021（郵局）則強制設定 IfFee 為 "否"
                    if (collectNoFromCsv == "7000021")
                    {
                        ifFeeFromCsv = "否";
                    }

                    var newRemitItem = new Intra2025.Models.ComRemit.RemitedList()
                    {
                        ConSno = conSno,
                        // 根據您的CSV格式：局號,帳號,匯款戶名,統編(身分證),帳款金額,手續費類別,備註
                        RemitPrice = decimal.TryParse(values[4].Trim(), out var price) ? (int)price : (int?)null, // 第5欄是帳款金額
                        IfFee = ifFeeFromCsv, // 使用處理後的手續費設定
                        RemitMemo = values.Length > 6 ? values[6].Trim() : "", // 第7欄是備註
                        ConPer = _userState.Account,
                        ConDate = DateTime.Now,
                        Kindno = 1
                    };

                    // 根據您的CSV格式：局號,帳號,匯款戶名,統編(身分證),帳款金額,是否有手續費,備註
                    Payee? payee = await PayeeService.GetPayeeByNameAndAccountAsync(
                        values[2].Trim(), // 匯款戶名
                        values[1].Trim().Replace("-", "") // 帳號
                    );

                    if (payee != null)
                    {
                        newRemitItem.CollecName = payee.CollectName;
                        newRemitItem.CollecAcc = payee.CollecAcc;
                        newRemitItem.CollectId = payee.CollectId;
                        newRemitItem.CollectNo = payee.CollectNo;
                    }
                    else
                    {
                        newRemitItem.CollecName = values[2].Trim(); // 匯款戶名
                        newRemitItem.CollecAcc = values[1].Trim(); // 帳號
                        newRemitItem.CollectId = values[3].Trim(); // 統編(身分證)
                        newRemitItem.CollectNo = collectNoFromCsv; // 使用預先取得的局號
                    }

                    batchItems.Add(newRemitItem);
                }
                catch (Exception)
                {
                    // 忽略無效的行，繼續處理下一行
                }
            }

            if (batchItems.Any())
            {
                await RemitService.AddRemitItemsAsync(batchItems);
                await LoadRemitList();

                // 如果 remitPurpose 仍然是空的，設定一個預設值
                if (string.IsNullOrWhiteSpace(remitPurpose))
                {
                    var firstRemitMemo = batchItems.FirstOrDefault(x => !string.IsNullOrEmpty(x.RemitMemo))?.RemitMemo;
                    if (!string.IsNullOrEmpty(firstRemitMemo))
                    {
                        remitPurpose = firstRemitMemo;
                    }
                }

                successMessage = $"成功匯入 {batchItems.Count} 筆資料。";
            }
            else
            {
                errorMessage = "CSV 檔案中沒有可匯入的有效資料。";
            }
        }
        catch (Exception ex)
        {
            errorMessage = $"處理檔案時發生錯誤: {ex.Message}";
        }
        finally
        {
            isLoading = false;
            selectedFile = null;
        }
    }

    private async Task LoadRemitList()
    {
        try
        {
            errorMessage = "";

            if (conSno <= 0)
            {
                errorMessage = "請輸入有效的彙整編號";
                return;
            }

            remitList = await RemitService.GetRemitListByConSnoAsync(conSno);

            if (remitList.Any())
            {
                successMessage = $"載入彙整編號 {conSno} 的資料，共 {remitList.Count} 筆";
                // 如果有資料，取第一筆的用途說明作為預設值
                var firstConMemo = remitList.First().ConMemo;
                if (!string.IsNullOrEmpty(firstConMemo))
                {
                    remitPurpose = firstConMemo;
                }
                // 如果 ConMemo 為空，但有 RemitMemo，則使用第一筆的 RemitMemo 作為預設值
                else if (string.IsNullOrEmpty(remitPurpose))
                {
                    var firstRemitMemo = remitList.FirstOrDefault(x => !string.IsNullOrEmpty(x.RemitMemo))?.RemitMemo;
                    if (!string.IsNullOrEmpty(firstRemitMemo))
                    {
                        remitPurpose = firstRemitMemo;
                    }
                }
            }
            else
            {
                successMessage = $"彙整編號 {conSno} 目前沒有資料";
            }
        }
        catch (Exception ex)
        {
            errorMessage = $"載入彙整清單時發生錯誤：{ex.Message}";
            Logger.LogError(ex, "載入彙整清單時發生錯誤");
        }
    }

    // 編輯功能
    private void StartEdit(int sno)
    {
        var item = remitList.FirstOrDefault(x => x.Sno == sno);
        if (item != null)
        {
            editingSno = sno;
            editingPrice = item.RemitPrice;
            editingMemo = item.RemitMemo ?? "";
            editingIfFee = item.IfFee ?? "否";
            editingFeeCategory = item.IfFee ?? "";
        }
    }

    private async Task SaveEdit(int sno)
    {
        try
        {
            var item = remitList.FirstOrDefault(x => x.Sno == sno);
            if (item != null)
            {
                item.RemitPrice = (int?)editingPrice;
                item.RemitMemo = editingMemo;

                // 將手續費類別轉換為 IfFee 值（1-6 為 "否"，7 為 "是"）
                var finalIfFee = ConvertFeeCategoryToIfFee(editingFeeCategory);

                // 檢查解款行代號，如果是 7000021（郵局）則強制設定 IfFee 為 "否"
                if (item.CollectNo == "7000021")
                {
                    finalIfFee = "否";
                }

                item.IfFee = finalIfFee;

                await RemitService.UpdateRemitItemAsync(item);
                successMessage = "資料已更新";
                editingSno = 0;
                StateHasChanged();
            }
        }
        catch (Exception ex)
        {
            errorMessage = $"更新時發生錯誤：{ex.Message}";
            Logger.LogError(ex, "更新匯款資料時發生錯誤");
        }
    }

    private void CancelEdit()
    {
        editingSno = 0;
        editingPrice = null;
        editingMemo = "";
        editingIfFee = "否";
    }

    private async Task DeleteItem(int sno)
    {
        try
        {
            var item = remitList.FirstOrDefault(x => x.Sno == sno);
            if (item == null) return;

            var confirmed = await JSRuntime.InvokeAsync<bool>("confirm",
                $"確定要刪除「{item.CollecName}」的匯款資料嗎？");

            if (confirmed)
            {
                var success = await RemitService.DeleteRemitItemAsync(sno);
                if (success)
                {
                    remitList.Remove(item);
                    successMessage = $"已刪除「{item.CollecName}」的匯款資料";
                    StateHasChanged();
                }
                else
                {
                    errorMessage = "刪除失敗";
                }
            }
        }
        catch (Exception ex)
        {
            errorMessage = $"刪除時發生錯誤：{ex.Message}";
            Logger.LogError(ex, "刪除匯款資料時發生錯誤");
        }
    }

    // 開啟新的彙整編號
    private async Task StartNewBatch()
    {
        var confirmed = await JSRuntime.InvokeAsync<bool>("confirm", "確定要放棄目前所有資料，並開啟一個新的彙整編號嗎?");
        if (confirmed)
        {
            conSno = await RemitService.GetNextConSnoAsync();
            remitList.Clear();
            ClearForm();
            remitPurpose = "";
            successMessage = $"已開啟新的彙整編號: {conSno}";
        }
    }

    // 導向管理者頁面
    private void GoToAdminPage()
    {
        if (remitList.Any())
        {
            NavigationManager.NavigateTo($"{Environment.GetEnvironmentVariable("ASPNETCORE_APPL_PATH")?.TrimEnd('/')}/ComRemit/AdminList?searchId={conSno}");
        }
        else
        {
            NavigationManager.NavigateTo($"{Environment.GetEnvironmentVariable("ASPNETCORE_APPL_PATH")?.TrimEnd('/')}/ComRemit/AdminList");
        }
    }

    private async Task ClearAll()
    {
        try
        {
            var confirmed = await JSRuntime.InvokeAsync<bool>("confirm",
                $"確定要清空所有彙整資料嗎？共 {remitList.Count} 筆資料將被移除。");

            if (confirmed)
            {
                // 刪除資料庫中的資料
                foreach (var item in remitList.Where(x => x.Sno > 0))
                {
                    await RemitService.DeleteRemitItemAsync(item.Sno);
                }

                remitList.Clear();
                remitPurpose = "";
                successMessage = "已清空所有彙整資料";
                StateHasChanged();
            }
        }
        catch (Exception ex)
        {
            errorMessage = $"清空資料時發生錯誤：{ex.Message}";
            Logger.LogError(ex, "清空資料時發生錯誤");
        }
    }

    private async Task CompleteAllItems()
    {
        try
        {
            if (!remitList.Any())
            {
                errorMessage = "沒有資料可以彙整";
                return;
            }

            if (string.IsNullOrWhiteSpace(remitPurpose))
            {
                errorMessage = "請輸入用途說明";
                return;
            }

            // 檢查是否所有項目都有金額
            var itemsWithoutAmount = remitList.Where(x => (x.RemitPrice ?? 0) <= 0).ToList();
            if (itemsWithoutAmount.Any())
            {
                var names = string.Join("、", itemsWithoutAmount.Select(x => x.CollecName).Take(3));
                if (itemsWithoutAmount.Count > 3) names += "等";
                errorMessage = $"以下項目尚未設定匯款金額：{names}";
                return;
            }

            var confirmed = await JSRuntime.InvokeAsync<bool>("confirm",
                $"確定要完成彙整編號 {conSno} 的所有資料嗎？\n" +
                $"共 {remitList.Count} 筆資料，總金額 {TotalAmount:N0} 元\n" +
                $"用途：{remitPurpose}\n" +
                "完成後資料將移至已彙整清單，無法再修改！");

            if (confirmed)
            {
                // 更新所有項目的用途說明
                foreach (var item in remitList)
                {
                    item.ConMemo = remitPurpose;
                }

                var allSnos = remitList.Select(x => x.Sno).ToList();
                var result = await RemitService.CompleteSelectedRemitWithClassificationAsync(allSnos, remitPurpose);

                if (result.Success)
                {
                    // 建立詳細的成功訊息
                    var detailMessage = result.Message;
                    if (result.CreatedBatches.Count > 1)
                    {
                        detailMessage += "\n\n彙整單詳細資訊：";
                        foreach (var batch in result.CreatedBatches)
                        {
                            detailMessage += $"\n• {batch.BatchType}彙整單 {batch.ConSno}：{batch.ItemCount} 筆，共 {batch.TotalAmount:N0} 元";
                        }
                    }

                    successMessage = result.Message;
                    remitList.Clear();
                    remitPurpose = "";
                    await JSRuntime.InvokeVoidAsync("alert", $"彙整完成！\n\n{detailMessage}\n\n系統將自動導向已彙整清單頁面。");

                    // 顯示訊息後自動導向已彙整清單頁面
                    GoToRemitedList();
                }
                else
                {
                    errorMessage = result.Message;
                }
            }
        }
        catch (Exception ex)
        {
            errorMessage = $"彙整時發生錯誤：{ex.Message}";
            Logger.LogError(ex, "彙整時發生錯誤");
        }
    }

    private void GoToRemitedList()
    {
        NavigationManager.NavigateTo($"{Environment.GetEnvironmentVariable("ASPNETCORE_APPL_PATH")?.TrimEnd('/')}/ComRemit/RemitedList");
    }

    public void Dispose()
    {
        debounceTimer?.Dispose();
    }
}


