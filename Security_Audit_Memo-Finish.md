# Gemini 安全性審查備忘錄 - 已完成項目

這份文件記錄了已完成的安全性修復與改善項目。

---

## **【已完成】**

### **A04:2021 – Insecure Design (不安全的設計)**

-   **Problem #4.1:** [V] **【高風險】** **機敏資料未遮罩**:
    -   **問題**: 身分證號碼在前端直接顯示，未經遮罩處理。
    -   **位置**: `ChildCaseList.razor`
    -   **影響**: 敏感個資外洩
    -   **建議**: 對身分證號碼進行遮罩處理，例如 `A12****789`。
    -   **修復日期**: 2025-08-12
    -   **修復說明**: 引入 `DataMaskingService`，並在 `ChildCaseList.razor` 中根據使用者是否為管理員或案件所屬戶所來決定是否顯示完整或遮罩後的身份證號碼。

-   **Problem #4.5:** [V] **【中風險】** **未驗證的重新導向**:
    -   **問題**: `ReturnUrl` 未經驗證，可能導致開放式重新導向攻擊。
    -   **位置**: `Login.razor`
    -   **影響**: 使用者可能被導向到惡意網站
    -   **建議**: 驗證 `ReturnUrl` 是否為安全的本機 URL。
    -   **修復日期**: 2025-08-12
    -   **修復說明**: 在處理登入邏輯前，使用 `Url.IsLocalUrl(ReturnUrl)` 來驗證返回的 URL 是否為應用程式的本機 URL，如果不是，則將其重置為預設的安全首頁，有效防止了開放式重新導向攻擊。

---

### **A08:2021 – Software and Data Integrity Failures (軟體與資料完整性故障)**

-   **Problem #8.4:** [V] **【高風險】** **無限制檔案上傳漏洞**:
    -   **問題**: 在 `wwwroot/uploads/Reports/` 等目錄中，可能存在未經適當驗證檔案類型或內容的無限制檔案上傳，允許攻擊者上傳惡意檔案。
    -   **建議**: 實作嚴格的檔案上傳驗證，包括白名單檔案類型、限制檔案大小、掃描惡意內容，並將上傳檔案儲存在非 Web 可存取的目錄中。
    -   **修復日期**: 2025-08-12
    -   **修復說明**: 建立了 `SecureFileUploadService`，它會執行多層次驗證（檔名、副檔名、MIME類型、檔案簽名），並將檔案儲存到非網頁可直接存取的 `App_Data/SecureUploads` 目錄下。同時，下載功能也透過 `SecureFileDownloadService` 進行了權限驗證。

---

### **A05:2021 – Security Misconfiguration (安全設定錯誤)**

-   **Problem #5.1:** [V] **【中風險】** **錯誤處理不當 - 資訊洩露**:
    -   **問題**: 錯誤處理直接向使用者顯示異常訊息，可能洩露系統內部資訊。
    -   **位置**:
        - `ChildCaseList.razor`: `ErrorMessage = $"刪除過程中發生錯誤：{ex.Message}";`
        - `RemitedList.razor`: `await JSRuntime.InvokeVoidAsync("alert", $"匯出失敗: {ex.Message}");`
    -   **影響**: 可能洩露敏感系統資訊給攻擊者
    -   **建議**: 實作通用錯誤訊息，詳細錯誤資訊僅記錄到日誌中
    -   **修復日期**: 2025-08-13
    -   **修復說明**: 注入 `ILogger` 到相關的 Razor 元件中。修改 `catch` 區塊，使用 Logger 記錄完整的例外訊息，並只對使用者顯示通用的錯誤提示，例如「發生無法預期的錯誤，請聯繫系統管理員」。

-   **Problem #5.4:** [V] **【低風險】** **輸入驗證不足**:
    -   **問題**: 搜尋輸入框缺乏長度限制和格式驗證。
    -   **位置**: `ChildCaseList.razor`, `CareCaseList.razor` 等搜尋功能
    -   **影響**: 可能導致效能問題或異常行為
    -   **建議**: 添加輸入長度限制和格式驗證
    -   **修復日期**: 2025-08-13
    -   **修復說明**: 在 `ChildCaseList.razor` 和 `CareCaseList.razor` 的搜尋 `input` 標籤中，添加了 `maxlength="50"` 屬性，限制使用者輸入的長度。

---

### **A07:2021 – Identification and Authentication Failures (身分識別與驗證失敗)**

-   **Problem #7.2:** [V] **【中風險】** **會話固定攻擊風險**:
    -   **問題**: 使用者登入時沒有重新生成 Session ID，可能存在會話固定攻擊風險。
    -   **位置**: Session 配置在 `Program.cs` 中
    -   **影響**: 攻擊者可能利用會話固定攻擊劫持使用者會話
    -   **建議**: 在使用者成功登入後重新生成 Session ID
    -   **修復日期**: 2025-08-13
    -   **備註**: 已在 `Services/SsoService.cs` 中實作 `RegenerateSessionIdAsync` 方法，在使用者成功登入後自動重新生成 Session ID，同時保持 Session 資料的完整性。詳見 `SESSION_SECURITY_FIX.md`

-   **Problem #7.3:** [V] **【中風險】** **SSO Token 處理不當**:
    -   **問題**: SSO Token 在日誌中被記錄，可能洩露敏感資訊。
    -   **位置**: `Services/SsoService.cs` 中的日誌記錄
    -   **影響**: Token 可能通過日誌洩露給未授權人員
    -   **建議**: 避免在日誌中記錄完整的 Token，僅記錄部分資訊用於除錯
    -   **修復日期**: 2025-08-13
    -   **備註**: 已實作 `GetSafeTokenInfo` 方法，所有 SSO Token 相關的日誌記錄現在只顯示安全格式（如：`abcd...xyz9 [64 chars]`），避免洩露完整 Token 內容。詳見 `SESSION_SECURITY_FIX.md`

---

### **A09:2021 – Security Logging and Monitoring Failures (安全日誌記錄與監控失敗)**

-   **Problem #9.1:** [V] **【中風險】** **日誌記錄和監控不足**:
    -   **問題**: 安全相關事件（例如登入失敗、授權失敗、異常活動）的日誌記錄不足，或缺乏有效的監控機制。
    -   **建議**: 實作全面的安全日誌記錄，包括所有安全相關事件，並建立監控和警報機制。
    -   **修復日期**: 2025-08-13
    -   **備註**: 已實作完整的安全日誌記錄和監控系統，包括 SecurityEventLogService、統一的安全事件模型、即時威脅檢測、登入失敗監控、路徑遍歷攻擊檢測等。所有關鍵安全事件現在都被正確記錄和監控。詳見 `SECURITY_LOGGING_MONITORING_FIX.md`