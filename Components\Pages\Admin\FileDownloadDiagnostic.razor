@page "/Admin/FileDownloadDiagnostic"
@using Intra2025.Data
@using Intra2025.Models
@using Intra2025.Services
@using Intra2025.Components.Base
@using Microsoft.EntityFrameworkCore
@inject YCRSDbContext _ycrsContext
@inject IWebHostEnvironment _environment
@inject ILogger<FileDownloadDiagnostic> _logger
@inject UserState _userState

<h3>檔案下載診斷工具</h3>

@if (!_userState.IsAdmin)
{
    <div class="alert alert-danger">
        <strong>存取被拒絕</strong><br />
        此頁面僅限管理員使用。
    </div>
}
else
{
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h5>YCRS 檔案診斷</h5>
                </div>
                <div class="card-body">
                    <button class="btn btn-primary" @onclick="RunDiagnostic">執行診斷</button>
                    
                    @if (diagnosticResults.Any())
                    {
                        <div class="mt-3">
                            <h6>診斷結果：</h6>
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>檔案 ID</th>
                                            <th>檔案名稱</th>
                                            <th>資料庫路徑</th>
                                            <th>實際檔案路徑</th>
                                            <th>檔案存在</th>
                                            <th>檔案大小</th>
                                            <th>狀態</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach (var result in diagnosticResults)
                                        {
                                            <tr class="@(result.FileExists ? "table-success" : "table-danger")">
                                                <td>@result.FileId</td>
                                                <td>@result.FileName</td>
                                                <td><small>@result.DatabasePath</small></td>
                                                <td><small>@result.ActualPath</small></td>
                                                <td>
                                                    @if (result.FileExists)
                                                    {
                                                        <span class="badge bg-success">✓</span>
                                                    }
                                                    else
                                                    {
                                                        <span class="badge bg-danger">✗</span>
                                                    }
                                                </td>
                                                <td>@result.FileSize</td>
                                                <td>@result.Status</td>
                                            </tr>
                                        }
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>

    @if (diagnosticSummary != null)
    {
        <div class="row mt-3">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">
                        <h5>診斷摘要</h5>
                    </div>
                    <div class="card-body">
                        <ul>
                            <li>總檔案數：@diagnosticSummary.TotalFiles</li>
                            <li>檔案存在：@diagnosticSummary.FilesExist</li>
                            <li>檔案遺失：@diagnosticSummary.FilesMissing</li>
                            <li>路徑格式問題：@diagnosticSummary.PathFormatIssues</li>
                        </ul>
                        
                        @if (diagnosticSummary.CommonIssues.Any())
                        {
                            <h6>常見問題：</h6>
                            <ul>
                                @foreach (var issue in diagnosticSummary.CommonIssues)
                                {
                                    <li class="text-warning">@issue</li>
                                }
                            </ul>
                        }
                    </div>
                </div>
            </div>
        </div>
    }
}

@code {
    private List<DiagnosticResult> diagnosticResults = new();
    private DiagnosticSummary? diagnosticSummary;

    private async Task RunDiagnostic()
    {
        diagnosticResults.Clear();
        
        try
        {
            // 取得所有 YCRS 檔案記錄
            var files = await _ycrsContext.YCRS_Files
                .Where(f => !f.IsMarkedForDeletion)
                .OrderByDescending(f => f.Createdate)
                .Take(50) // 限制最近 50 個檔案
                .ToListAsync();

            foreach (var file in files)
            {
                var result = new DiagnosticResult
                {
                    FileId = file.Id ?? "N/A",
                    FileName = file.FileName ?? "N/A",
                    DatabasePath = file.Filepath ?? "N/A"
                };

                // 計算實際檔案路徑
                result.ActualPath = GetActualFilePath(file.Filepath ?? "");
                
                // 檢查檔案是否存在
                if (File.Exists(result.ActualPath))
                {
                    result.FileExists = true;
                    var fileInfo = new FileInfo(result.ActualPath);
                    result.FileSize = $"{fileInfo.Length / 1024} KB";
                    result.Status = "正常";
                }
                else
                {
                    result.FileExists = false;
                    result.FileSize = "N/A";
                    result.Status = "檔案遺失";
                }

                diagnosticResults.Add(result);
            }

            // 生成摘要
            diagnosticSummary = new DiagnosticSummary
            {
                TotalFiles = diagnosticResults.Count,
                FilesExist = diagnosticResults.Count(r => r.FileExists),
                FilesMissing = diagnosticResults.Count(r => !r.FileExists),
                PathFormatIssues = diagnosticResults.Count(r => !r.DatabasePath.StartsWith("/secure-uploads/")),
                CommonIssues = GetCommonIssues()
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "診斷過程中發生錯誤");
        }
    }

    private string GetActualFilePath(string relativePath)
    {
        if (string.IsNullOrEmpty(relativePath)) return "";
        
        var cleanPath = relativePath.TrimStart('/');
        
        if (cleanPath.StartsWith("secure-uploads/"))
        {
            var secureUploadPath = Path.Combine(_environment.ContentRootPath, "App_Data", "SecureUploads");
            var subPath = cleanPath.Substring("secure-uploads/".Length);
            return Path.Combine(secureUploadPath, subPath);
        }
        
        return Path.Combine(_environment.ContentRootPath, "App_Data", "SecureUploads", cleanPath);
    }

    private List<string> GetCommonIssues()
    {
        var issues = new List<string>();
        
        if (diagnosticResults.Any(r => !r.DatabasePath.StartsWith("/secure-uploads/")))
        {
            issues.Add("部分檔案使用舊的路徑格式，需要執行檔案遷移");
        }
        
        if (diagnosticResults.Any(r => !r.FileExists))
        {
            issues.Add("部分檔案在檔案系統中遺失");
        }
        
        return issues;
    }

    public class DiagnosticResult
    {
        public string FileId { get; set; } = "";
        public string FileName { get; set; } = "";
        public string DatabasePath { get; set; } = "";
        public string ActualPath { get; set; } = "";
        public bool FileExists { get; set; }
        public string FileSize { get; set; } = "";
        public string Status { get; set; } = "";
    }

    public class DiagnosticSummary
    {
        public int TotalFiles { get; set; }
        public int FilesExist { get; set; }
        public int FilesMissing { get; set; }
        public int PathFormatIssues { get; set; }
        public List<string> CommonIssues { get; set; } = new();
    }
}
