/*!*  filename: sf-breadcrumb.min.js 
*     version : 26.1.41 
*     Copyright Syncfusion Inc. 2001 - 2024. All rights reserved. 
*     Use of this code is subject to the terms of our license.
*     A copy of the current license can be obtained at any time by e-mailing 
*     <EMAIL>. Any infringement will be prosecuted under 
*     applicable laws. 
*/
(window.webpackJsonp=window.webpackJsonp||[]).push([[9],{"./bundles/sf-breadcrumb.js":function(e,t,o){"use strict";o.r(t);o("./modules/sf-breadcrumb.js")},"./modules/sf-breadcrumb.js":function(e,t){window.sfBlazor=window.sfBlazor||{},window.sfBlazor.Breadcrumb=function(){"use strict";var e=function(){function e(e,t,o,n,s,i,r){this.dataId=e,this.element=t,this.menu=r,this.popup=i,this.overflowMode=n,this.maxItems=s,this.dotnetRef=o,window.sfBlazor.setCompInstance(this),this.calculateMaxItems(),this.wireEvents()}return e.prototype.calculateMaxItems=function(){if(this.prevWidth=this.element.offsetWidth,"Default"===this.overflowMode||"Collapsed"===this.overflowMode||"Menu"===this.overflowMode){var e=-1,t=this.element.offsetWidth,o=[].slice.call(this.element.children[0].children).reverse(),n="Menu"===this.overflowMode?0:o[o.length-1].offsetWidth+(o[o.length-2]?o[o.length-2].offsetWidth:0);if("Menu"===this.overflowMode){var s=this.getMenuElement();this.element.append(s),n+=s.offsetWidth,s.remove()}for(var i=0;i<o.length-2;i++){if(n>t){e=Math.ceil((i-1)/2)+("Menu"===this.overflowMode&&i<=2?0:1);break}if("Menu"===this.overflowMode&&2===i&&(n+=o[o.length-1].offsetWidth+o[o.length-2].offsetWidth)>t){e=1;break}if(("Menu"!==this.overflowMode||!o[i].classList.contains("e-breadcrumb-menu"))&&(n+=o[i].offsetWidth)>t){e=Math.ceil(i/2)+("Menu"===this.overflowMode&&i<=2?0:1);break}}this.dotnetRef.invokeMethodAsync("ChangeMaxItems",e)}else if(("Wrap"===this.overflowMode||"Scroll"===this.overflowMode)&&this.maxItems>0){t=0;if((o=this.element.querySelectorAll(".e-breadcrumb-item,.e-breadcrumb-separator")).length>this.maxItems+this.maxItems-1){for(i="Wrap"===this.overflowMode?1:0;i<this.maxItems+this.maxItems-1;i++)t+=o[i].offsetWidth;t=t+5+2*parseInt(getComputedStyle(this.element.children[0]).paddingLeft,10),"Wrap"===this.overflowMode?this.element.querySelector(".e-breadcrumb-wrapped-ol").style.width=t+"px":this.element.style.width=t+"px"}}},e.prototype.resize=function(){this.element.offsetWidth>0&&this.prevWidth!=this.element.offsetWidth&&this.dotnetRef.invokeMethodAsync("ResizeHandler")},e.prototype.getMenuElement=function(){return sf.base.createElement("li",{className:"e-icons e-breadcrumb-menu"})},e.prototype.openPopup=function(e,t){var o,n;document.body.appendChild(t);var s=e.getBoundingClientRect(),i=t.getBoundingClientRect();o=s.left+pageXOffset,n=s.bottom+pageYOffset,s.bottom+i.height>document.documentElement.clientHeight&&n-s.height-i.height>document.documentElement.clientTop&&(n=n-s.height-i.height),s.left+i.width>document.documentElement.clientWidth&&s.right-i.width>document.documentElement.clientLeft&&(o=o+s.width-i.width),this.addEventListener(),t.style.left=Math.ceil(o)+"px",t.style.top=Math.ceil(n)+"px",t.style.zIndex=sf.popups.getZindexPartial(this.element)+"",t.style.visibility="",t.firstElementChild.focus()},e.prototype.addEventListener=function(){sf.base.EventHandler.add(document,"mousedown",this.mousedownHandler,this),this.popup&&sf.base.EventHandler.add(this.popup,"keydown",this.popupKeyDownHandler,this)},e.prototype.popupKeyDownHandler=function(e){"Escape"===e.key&&this.dotnetRef.invokeMethodAsync("ClosePopup",null)},e.prototype.mousedownHandler=function(e){if(this.popup&&this.popup.parentElement){var t=e.target;sf.base.closest(t,"#"+this.menu.id)||sf.base.closest(e.target,"#"+this.popup.id)||(this.dotnetRef.invokeMethodAsync("ClosePopup",null),this.removeEventListener())}else this.removeEventListener()},e.prototype.removeEventListener=function(){sf.base.EventHandler.remove(document,"mousedown",this.mousedownHandler),this.popup&&sf.base.EventHandler.remove(this.popup,"keydown",this.popupKeyDownHandler)},e.prototype.wireEvents=function(){window.addEventListener("resize",this.resize.bind(this))},e.prototype.unWireEvents=function(){window.removeEventListener("resize",this.resize.bind(this))},e.prototype.destroy=function(){this.unWireEvents()},e}();return{initialize:function(t,o,n,s,i){o&&new e(t,o,n,s,i)},calculateMaxItems:function(e,t){var o=window.sfBlazor.getCompInstance(e);sf.base.isNullOrUndefined(o)||(o.overflowMode=t,o.calculateMaxItems())},openPopup:function(e,t,o){var n=window.sfBlazor.getCompInstance(e);sf.base.isNullOrUndefined(n)||(n.popup=o,n.menu=t,n.openPopup(t,o))},destroy:function(e){var t=window.sfBlazor.getCompInstance(e);sf.base.isNullOrUndefined(t)||t.destroy()}}}()}}]);(async()=>{await import(`${document.baseURI}_content/Syncfusion.Blazor/scripts/syncfusion-blazor-base.min.js?v=26.1.undefined`).then(()=>{sfBlazor.loadDependencies('sfbreadcrumb');})})();