using Intra2025.Data;
using Intra2025.Models.ComRemit;
using Intra2025.Components.Base;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;

namespace Intra2025.Services
{
    /// <summary>
    /// 彙整完成結果
    /// </summary>
    public class RemitCompletionResult
    {
        public bool Success { get; set; }
        public string Message { get; set; } = "";
        public List<RemitBatchInfo> CreatedBatches { get; set; } = new();
    }

    /// <summary>
    /// 彙整批次資訊
    /// </summary>
    public class RemitBatchInfo
    {
        public int ConSno { get; set; }
        public string BatchType { get; set; } = ""; // "郵局" 或 "台銀"
        public int ItemCount { get; set; }
        public decimal TotalAmount { get; set; }
    }

    public class ComRemitService
    {
        private readonly ComRemitDbContext _context;
        private readonly ComRemitPayeeService _payeeService;
        private readonly UserState _userState;
        private readonly AuditLogService _auditLogService;

        public ComRemitService(ComRemitDbContext context, ComRemitPayeeService payeeService, UserState userState, AuditLogService auditLogService)
        {
            _context = context;
            _payeeService = payeeService;
            _userState = userState;
            _auditLogService = auditLogService;
        }

        public async Task<List<RemitedList>> GetRemitListByConSnoAsync(int conSno)
        {
            var conSnoString = conSno;
            var query = _context.RemitedList
                .Where(r => r.ConSno == conSnoString && r.CashDate == null);

            // 如果不是管理員，則只允許查看自己單位下的資料
            if (!_userState.IsAdmin)
            {
                query = query.Where(r => r.ConUnit == _userState.UnitName); // 假設 ConUnit 儲存了單位名稱
            }

            return await query
                .OrderBy(r => r.Sno)
                .ToListAsync();
        }

        public async Task<RemitedList?> GetRemitItemBySnoAsync(int sno)
        {
            return await _context.RemitedList.FindAsync(sno);
        }

        public async Task<bool> AddRemitItemAsync(RemitedList item)
        {
            // 伺服器端驗證
            if (item.RemitPrice <= 0)
            {
                throw new ArgumentException("金額必須為正數。", nameof(item.RemitPrice));
            }

            try
            {
                _context.RemitedList.Add(item);
                await _context.SaveChangesAsync();
                _auditLogService.Log("AddRemitItem", $"Sno={item.Sno}, Price={item.RemitPrice}");
                return true;
            }
            catch
            {
                return false;
            }
        }

        public async Task AddRemitItemsAsync(List<RemitedList> items)
        {
            await _context.RemitedList.AddRangeAsync(items);
            await _context.SaveChangesAsync();
            _auditLogService.Log("AddRemitItems", $"Count={items.Count}, TotalPrice={items.Sum(i => i.RemitPrice)}");
        }

        public async Task UpdateRemitItemAsync(RemitedList item)
        {
            var existing = await _context.RemitedList.FindAsync(item.Sno);
            if (existing != null)
            {
                // 權限檢查：只有建立者或管理員可以修改
                if (existing.ConPer != _userState.Account && !_userState.IsAdmin)
                {
                    throw new UnauthorizedAccessException("您沒有權限修改此資料。");
                }

                // 手動更新，避免 EF Core 追蹤問題
                if (item.RemitPrice <= 0)
                {
                    throw new ArgumentException("金額必須為正數。", nameof(item.RemitPrice));
                }
                existing.RemitPrice = item.RemitPrice;
                existing.IfFee = item.IfFee;
                existing.RemitMemo = item.RemitMemo;

                _context.RemitedList.Update(existing);
                await _context.SaveChangesAsync();
                _auditLogService.Log("UpdateRemitItem", $"Sno={item.Sno}, NewPrice={item.RemitPrice}");
            }
        }

        public async Task<bool> DeleteRemitItemAsync(int sno)
        {
            try
            {
                var item = await _context.RemitedList.FindAsync(sno);
                if (item == null)
                    return false;

                // 權限檢查
                if (item.CashDate.HasValue) // 已彙整的資料
                {
                    if (!_userState.IsAdmin)
                    {
                        throw new UnauthorizedAccessException("已完成彙整的資料只能由管理員刪除");
                    }
                }
                else // 未彙整的資料
                {
                    if (item.ConPer != _userState.Account && !_userState.IsAdmin)
                    {
                        throw new UnauthorizedAccessException("您沒有權限刪除此資料。");
                    }
                }

                _context.RemitedList.Remove(item);
                await _context.SaveChangesAsync();
                _auditLogService.Log("DeleteRemitItem", $"Sno={sno}");
                return true;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 獲取下一個彙整編號，格式為 YYMMDD00n
        /// </summary>
        /// <param name="count">需要獲取的連續編號數量，預設為1</param>
        /// <returns>第一個可用的 ConSno（如果需要多個，後續編號為連續遞增）</returns>
        public async Task<int> GetNextConSnoAsync(int count = 1)
        {
            try
            {
                if (count < 1) count = 1;

                // 獲取當前日期並格式化為 YYMMDD
                var today = DateTime.Now;
                var datePrefix = $"{today.Year % 100:D2}{today.Month:D2}{today.Day:D2}";
                var datePrefixInt = int.Parse(datePrefix) * 1000; // 轉換為 YYMMDD000 格式的基礎數字

                // Console.WriteLine($"GetNextConSnoAsync: 請求 {count} 個連續編號，日期前綴 = {datePrefix}");

                // 查詢今天的所有編號（包含所有資料，不論是否已彙整）
                var allConSnos = await _context.RemitedList
                    .Where(r => r.ConSno.HasValue)
                    .OrderBy(r => r.ConSno)
                    .Select(r => r.ConSno!.Value)
                    .ToListAsync();

                // 篩選今天的編號
                var todayConSnos = allConSnos
                    .Where(conSno => conSno >= datePrefixInt && conSno < datePrefixInt + 1000)
                    .OrderBy(x => x)
                    .ToList();

                // Console.WriteLine($"GetNextConSnoAsync: 今天現有編號: [{string.Join(", ", todayConSnos)}]");

                var maxConSno = todayConSnos.DefaultIfEmpty(datePrefixInt).Max();

                // 如果今天還沒有編號，返回 YYMMDD001，否則返回最大編號+1
                var nextConSno = maxConSno == datePrefixInt ? datePrefixInt + 1 : maxConSno + 1;

                // 確保有足夠的空間分配連續編號（不超過999）
                if (nextConSno + count - 1 >= datePrefixInt + 1000)
                {
                    throw new InvalidOperationException($"今天的編號已用盡，無法分配 {count} 個連續編號");
                }

                // Console.WriteLine($"GetNextConSnoAsync: 分配編號範圍 {nextConSno} - {nextConSno + count - 1}");

                return nextConSno;
            }
            catch
            {
                // 如果發生任何錯誤，返回今天日期的第一個編號
                var today = DateTime.Now;
                var datePrefix = $"{today.Year % 100:D2}{today.Month:D2}{today.Day:D2}";
                return int.Parse(datePrefix) * 1000 + 1; // 返回 YYMMDD001
            }
        }

        /// <summary>
        /// 完成彙整 - 將指定 ConSno 的所有資料標記為已彙整
        /// </summary>
        /// <param name="conSno">彙整編號</param>
        /// <returns>是否成功彙整</returns>
        public async Task<bool> CompleteRemitAsync(int conSno)
        {
            // 權限檢查：只有管理員可以執行
            if (!_userState.IsAdmin)
            {
                throw new UnauthorizedAccessException("您沒有權限執行此操作。");
            }

            try
            {
                using var transaction = await _context.Database.BeginTransactionAsync();
                var items = await _context.RemitedList
                    .Where(r => r.ConSno == conSno && r.CashDate == null) // 只更新未彙整的資料
                    .ToListAsync();

                if (!items.Any())
                {
                    return false; // 沒有待彙整的資料
                }

                // 標記為已彙整
                foreach (var item in items)
                {
                    item.CashDate = DateTime.Now; // 設定彙整完成時間
                }

                await _context.SaveChangesAsync();
                await transaction.CommitAsync();
                _auditLogService.Log("CompleteRemit", $"ConSno={conSno}, Count={items.Count}");
                return true;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 檢查指定 ConSno 是否已完成彙整
        /// </summary>
        /// <param name="conSno">彙整編號</param>
        /// <returns>是否已完成彙整</returns>
        public async Task<bool> IsRemitCompletedAsync(int conSno)
        {
            try
            {
                // 檢查是否有待彙整的資料（CashDate 為 null）
                var hasUncompletedItems = await _context.RemitedList
                    .AnyAsync(r => r.ConSno == conSno && r.CashDate == null);

                // 只要有待彙整的資料，就表示尚未完成彙整
                return !hasUncompletedItems;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 完成選中項目的彙整 - 將指定 Sno 的資料標記為已彙整（支援自動分類）
        /// </summary>
        /// <param name="snoList">要彙整的 Sno 列表</param>
        /// <param name="purpose">彙整單用途說明</param>
        /// <returns>彙整結果詳細資訊</returns>
        public async Task<RemitCompletionResult> CompleteSelectedRemitWithClassificationAsync(List<int> snoList, string purpose = "")
        {
            // 權限檢查：只有管理員可以執行
            if (!_userState.IsAdmin)
            {
                throw new UnauthorizedAccessException("您沒有權限執行此操作。");
            }

            var result = new RemitCompletionResult();

            try
            {
                if (!snoList.Any())
                {
                    result.Message = "沒有資料可以彙整";
                    return result;
                }

                var items = await _context.RemitedList
                    .Where(r => snoList.Contains(r.Sno) && r.CashDate == null) // 只更新未彙整的資料
                    .ToListAsync();

                if (!items.Any())
                {
                    result.Message = "沒有待彙整的資料";
                    return result;
                }

                // 檢查是否有混合類型的資料
                var postalItems = items.Where(r => r.CollectNo == "7000021").ToList();
                var bankItems = items.Where(r => r.CollectNo != "7000021").ToList();

                // Console.WriteLine($"CompleteSelectedRemitWithClassificationAsync: 總資料 {items.Count} 筆");
                // Console.WriteLine($"CompleteSelectedRemitWithClassificationAsync: 郵局資料 {postalItems.Count} 筆");
                // Console.WriteLine($"CompleteSelectedRemitWithClassificationAsync: 台銀資料 {bankItems.Count} 筆");
                // Console.WriteLine($"CompleteSelectedRemitWithClassificationAsync: 郵局資料詳細: {string.Join(", ", postalItems.Select(x => $"Sno:{x.Sno},CollectNo:{x.CollectNo}"))}");
                // Console.WriteLine($"CompleteSelectedRemitWithClassificationAsync: 台銀資料詳細: {string.Join(", ", bankItems.Select(x => $"Sno:{x.Sno},CollectNo:{x.CollectNo}"))}");

                // 如果只有一種類型，使用原有邏輯
                if (!postalItems.Any() || !bankItems.Any())
                {
                    // 標記為已彙整
                    foreach (var item in items)
                    {
                        item.CashDate = DateTime.Now;
                        item.ConDate = DateTime.Now;
                        if (!string.IsNullOrWhiteSpace(purpose))
                        {
                            item.ConMemo = purpose;
                        }
                    }

                    await _context.SaveChangesAsync();

                    var batchType = postalItems.Any() ? "郵局" : "台銀";
                    var totalAmount = items.Sum(x => (decimal)(x.RemitPrice ?? 0));
                    var conSno = items.First().ConSno ?? 0;

                    result.CreatedBatches.Add(new RemitBatchInfo
                    {
                        ConSno = conSno,
                        BatchType = batchType,
                        ItemCount = items.Count,
                        TotalAmount = totalAmount
                    });

                    // Console.WriteLine($"CompleteSelectedRemitWithClassificationAsync: 單一類型處理完成 - {batchType}");
                    result.Success = true;
                    result.Message = $"已成功彙整 {items.Count} 筆{batchType}資料";
                    return result;
                }

                // Console.WriteLine($"CompleteSelectedRemitWithClassificationAsync: 檢測到混合類型，開始分類處理");

                // 有混合類型，需要分別處理
                // 先在交易外獲取所需的 ConSno，避免 DbContext 並發問題
                int? postalConSno = null;
                int? bankConSno = null;

                // 修復競爭條件：確保獲取不同的 ConSno
                if (postalItems.Any() && bankItems.Any())
                {
                    // 同時需要兩個 ConSno，一次性獲取連續的兩個編號
                    var firstConSno = await GetNextConSnoAsync(2);

                    postalConSno = firstConSno;
                    bankConSno = firstConSno + 1;

                    // Console.WriteLine($"CompleteSelectedRemitWithClassificationAsync: 郵局資料分配 ConSno = {postalConSno}");
                    // Console.WriteLine($"CompleteSelectedRemitWithClassificationAsync: 台銀資料分配 ConSno = {bankConSno}");
                }
                else if (postalItems.Any())
                {
                    postalConSno = await GetNextConSnoAsync(1);
                    // Console.WriteLine($"CompleteSelectedRemitWithClassificationAsync: 郵局資料分配 ConSno = {postalConSno}");
                }
                else if (bankItems.Any())
                {
                    bankConSno = await GetNextConSnoAsync(1);
                    // Console.WriteLine($"CompleteSelectedRemitWithClassificationAsync: 台銀資料分配 ConSno = {bankConSno}");
                }

                using var transaction = await _context.Database.BeginTransactionAsync();

                try
                {
                    // 處理郵局資料
                    if (postalItems.Any() && postalConSno.HasValue)
                    {
                        foreach (var item in postalItems)
                        {
                            item.ConSno = postalConSno.Value;
                            item.CashDate = DateTime.Now;
                            item.ConDate = DateTime.Now;
                            if (!string.IsNullOrWhiteSpace(purpose))
                            {
                                item.ConMemo = purpose;
                            }
                        }

                        var postalTotalAmount = postalItems.Sum(x => (decimal)(x.RemitPrice ?? 0));
                        result.CreatedBatches.Add(new RemitBatchInfo
                        {
                            ConSno = postalConSno.Value,
                            BatchType = "郵局",
                            ItemCount = postalItems.Count,
                            TotalAmount = postalTotalAmount
                        });
                    }

                    // 處理台銀資料
                    if (bankItems.Any() && bankConSno.HasValue)
                    {
                        foreach (var item in bankItems)
                        {
                            item.ConSno = bankConSno.Value;
                            item.CashDate = DateTime.Now;
                            item.ConDate = DateTime.Now;
                            if (!string.IsNullOrWhiteSpace(purpose))
                            {
                                item.ConMemo = purpose;
                            }
                        }

                        var bankTotalAmount = bankItems.Sum(x => (decimal)(x.RemitPrice ?? 0));
                        result.CreatedBatches.Add(new RemitBatchInfo
                        {
                            ConSno = bankConSno.Value,
                            BatchType = "台銀",
                            ItemCount = bankItems.Count,
                            TotalAmount = bankTotalAmount
                        });
                    }

                    // Console.WriteLine($"CompleteSelectedRemitWithClassificationAsync: 準備保存變更");
                    await _context.SaveChangesAsync();
                    // Console.WriteLine($"CompleteSelectedRemitWithClassificationAsync: 變更已保存，準備提交交易");
                    await transaction.CommitAsync();
                    _auditLogService.Log("CompleteSelectedRemitWithClassification", $"PostalCount={postalItems.Count}, BankCount={bankItems.Count}");
                    // Console.WriteLine($"CompleteSelectedRemitWithClassificationAsync: 交易已提交");

                    result.Success = true;
                    result.Message = $"已自動分類彙整：郵局 {postalItems.Count} 筆、台銀 {bankItems.Count} 筆，共產生 {result.CreatedBatches.Count} 個彙整單";
                    return result;
                }
                catch (Exception)
                {
                    // Console.WriteLine($"CompleteSelectedRemitWithClassificationAsync: 發生異常，回滾交易: {ex.Message}");
                    await transaction.RollbackAsync();
                    throw;
                }
            }
            catch (Exception ex)
            {
                result.Message = $"彙整時發生錯誤：{ex.Message}";
                return result;
            }
        }

        /// <summary>
        /// 完成選中項目的彙整 - 將指定 Sno 的資料標記為已彙整（原有方法，保持向後兼容）
        /// </summary>
        /// <param name="snoList">要彙整的 Sno 列表</param>
        /// <param name="purpose">彙整單用途說明</param>
        /// <returns>是否成功彙整</returns>
        public async Task<bool> CompleteSelectedRemitAsync(List<int> snoList, string purpose = "")
        {
            // 權限檢查：只有管理員可以執行
            if (!_userState.IsAdmin)
            {
                throw new UnauthorizedAccessException("您沒有權限執行此操作。");
            }

            try
            {
                using var transaction = await _context.Database.BeginTransactionAsync();
                if (!snoList.Any())
                {
                    return false;
                }

                var items = await _context.RemitedList
                    .Where(r => snoList.Contains(r.Sno) && r.CashDate == null) // 只更新未彙整的資料
                    .ToListAsync();

                if (!items.Any())
                {
                    return false; // 沒有待彙整的資料
                }

                // 標記為已彙整
                foreach (var item in items)
                {
                    item.CashDate = DateTime.Now; // 設定彙整完成時間
                    item.ConDate = DateTime.Now; // 設定彙整日期
                    if (!string.IsNullOrWhiteSpace(purpose))
                    {
                        // 儲存彙整單用途說明到 ConMemo 欄位
                        item.ConMemo = purpose;
                    }
                }

                await _context.SaveChangesAsync();
                await transaction.CommitAsync();
                _auditLogService.Log("CompleteSelectedRemit", $"Count={items.Count}");
                return true;
            }
            catch
            {
                return false;
            }
        }
    }
}
